const nextTranslate = require("next-translate-plugin");
const config =
  //nextTranslate(
  {
    experimental: {},

    images: {
      remotePatterns: [
        {
          protocol: "https",
          hostname: "osen-aia-a.s3-eu-north-1.amazonaws.com",
          port: "",
          pathname: "**/*",
        },
        {
          protocol: "https",
          hostname: "aia-staging.onrender.com",
          port: "",
          pathname: "/uploads/**",
        },
        {
          protocol: "https",
          hostname: "www.gravatar.com",
          port: "",
          pathname: "/avatar/**",
        },
        {
          protocol: "https",
          hostname: "themes.coderthemes.com",
          port: "",
          pathname: "/images/**",
        },
        {
          protocol: "http",
          hostname: "**************",
          port: "3333",
          pathname: "/uploads/**",
        },
        {
          protocol: "http",
          hostname: "**************",
          port: "3333",
          pathname: "/uploads/**",
        },
        {
          protocol: "http",
          hostname: "127.0.0.1",
          port: "3333",
          pathname: "/uploads/**",
        },
        {
          port: "",
          hostname: "ui-avatars.com",
          protocol: "https",
        },
      ],
    },
  }
  // );

// config.i18n = {
//   ...config.i18n,
//   localesToIgnore: [],
// };

/** @type {import("next").NextConfig} */
module.exports = config;
