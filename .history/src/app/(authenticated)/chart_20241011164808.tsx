"use client";

import Chart from "react-apexcharts";

export default function HomeChart() {
  const state = {
    series: [
      {
        name: "Products",
        type: "column",
        data: [ 11, 22, 27, 13, 22, 37, 21, 44, 22, 30],
        color: "#65A694",
      },
      {
        name: "Orders",
        type: "area",
        data: [55, 41, 67, 22, 43, 21, 41, 56, 27, 43],
        color: "#00E396",
      },
      {
        name: "Payments",
        type: "line",
        data: [25, 36, 30, 45, 35, 64, 52, 59, 36, 39],
        color: "#FEB019",
      },
    ],
    options: {
      chart: {
        height: 350,
        type: "line" as const,
        stacked: false,
      },
      stroke: {
        width: [0, 2, 5],
        curve: "smooth" as const,
      },
      plotOptions: {
        bar: {
          columnWidth: "50%",
        },
      },

      fill: {
        opacity: [0.85, 0.25, 1],
        gradient: {
          inverseColors: false,
          shade: "light",
          type: "vertical",
          opacityFrom: 0.85,
          opacityTo: 0.55,
          stops: [0, 100, 100, 100],
        },
      },
      labels: [
        "01/01/2024",
        "02/01/2024",
        "03/01/2024",
        "04/01/2024",
        "05/01/2024",
        "06/01/2024",
        "07/01/2024",
        "08/01/2024",
        "09/01/2024",
        "10/01/2024",
        "11/01/2024",
      ],
      markers: {
        size: 0,
      },
      xaxis: {
        type: "datetime" as const,
      },
      yaxis: {
        title: {
          text: "Points",
        },
        min: 0,
      },
      tooltip: {
        shared: true,
        intersect: false,
        y: {
          // formatter: function (y: ApexTooltipY) {
          // 	if (typeof y !== "undefined") {
          // 		return y.toFixed(0) + " points";
          // 	}
          // 	return y;
          // },
        },
      },
    },
  };

  return (
    <Chart
      options={state.options}
      series={state.series}
      type="line"
      height={600}
    />
  );
}
