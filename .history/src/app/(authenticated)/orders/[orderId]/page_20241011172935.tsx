import { api, imagePath } from "@/lib/api";
import { formatDate, formatDistance } from "date-fns";
import Image from "next/image";
import Link from "next/link";
import { cache } from "react";

// Fetch order details from the API
const fetchOrder = cache((id: string) => api.get<Order>(`temp-orders/${id}`));

export const generateMetadata = async ({ params }: { params: { orderId: string } }) => {
  const order = await fetchOrder(params.orderId);

  return {
    title: order
      ? `Order ${order?.id.toUpperCase()} - ${formatDate(
          new Date(order?.createdAt),
          "MMM dd, yyyy hh:mm a",
        )}`
      : "Order",
  };
};

export default async function page({ params }: { params: { orderId: string } }) {
  const order = await fetchOrder(params.orderId);

  return (
    <>
      {order && (
        <div className="page-content space-y-6 p-6">
          {/* Order information */}
          <div className="flex w-full items-center justify-between">
            <h1 className="text-lg font-semibold">Order Details</h1>
            <Link className="text-base font-medium text-primary" href="/orders">
              Back to List
            </Link>
          </div>

          {/* Render order details */}
          <div className="space-y-5">
            <div className="border p-4 rounded-lg bg-white">
              <h4 className="text-base font-semibold">Customer Name: {order.customer?.name}</h4>
              <p>Email: {order.customer?.email}</p>
              <p>Phone: {order.customer?.phone}</p>
            </div>

            {/* List all items in the order */}
            <div className="border p-4 rounded-lg bg-white">
              <h4 className="text-base font-semibold">Order Items</h4>
              {order.items.map((item) => (
                <div key={item.id} className="py-2">
                  <p>Name: {item.name}</p>
                  <p>Price: KES {item.price}</p>
                  <p>Quantity: {item.quantity}</p>
                </div>
              ))}
            </div>
            

            <div className="border p-4 rounded-lg bg-white">
              <h4 className="text-base font-semibold">Total: KES {order.total}</h4>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
