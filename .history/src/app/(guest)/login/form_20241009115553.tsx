"use client";

import Link from "next/link";
import { useState } from "react";
import { useForm } from "react-hook-form";
import toast from "react-hot-toast";

export default function LoginForm({
  loginUser,
}: {
  loginUser: (payload: {
    username: string;
    password: string;
    identifier?: string;
    portal?: boolean;
  }) => Promise<any>; // Modified to accept any type of response for full data logging
}) {
  const [isStaff, setIsStaff] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState("");
  const { register, handleSubmit, reset, formState } = useForm<{
    username: string;
    password: string;
    identifier?: string;
    portal?: boolean;
  }>({
    defaultValues: {
      username: "",
      password: "",
      portal: true,
    },
  });

  const onSubmit = async (payload: {
    username: string;
    password: string;
    identifier?: string;
    portal?: boolean;
  }) => {
    if (!isStaff && payload.identifier) {
      delete payload.identifier;
    }

    try {
      // Display a loading toast
      toast.loading("Logging in...");

      // Attempt to login the user and capture the response
      const response = await loginUser(payload);

      // Dismiss loading toast
      toast.dismiss();

      // Log the complete response data to the console
      console.log("Full Login Response:", response);

      // Display success toast and reset form
      toast.success("Logged in successfully!");
      reset();
    } catch (err) {
      // Dismiss any loading toast before showing error
      toast.dismiss();

      // Log error and display error toast
      console.error("Login error:", err);
      setError("Error logging in. Please check your credentials.");
      toast.error("Error logging in");
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      {error && (
        <div className="py-5 text-sm text-red-500">
          <p>{error}</p>
          {!isStaff && (
            <p className="text-sm text-default-600">
              Trying to login as staff?{" "}
              <a
                href="#"
                onClick={() => setIsStaff(true)}
                className="text-sm text-default-600 underline"
              >
                {isStaff ? "Admin" : "Staff"} login
              </a>
            </p>
          )}
        </div>
      )}

      <div className="relative mb-6 max-w-full">
        <label
          className="mb-2 block text-sm font-medium text-default-900"
          htmlFor="email"
        >
          Email address or phone number
        </label>
        <div className="relative max-w-full">
          <input
            type="text"
            placeholder="Enter your email or phone number"
            {...register("username", { required: true })}
            className="form-input w-full rounded-lg border border-default-200 bg-white px-4 py-3 text-black dark:bg-default-50"
          />
        </div>
      </div>

      <div className="relative mb-1 w-full">
        <label
          className="mb-2 block text-sm font-medium text-default-900"
          htmlFor="password"
        >
          Account password
        </label>
        <div className="flex w-full">
          <div className="relative w-full">
            <input
              {...register("password", { required: true })}
              type={showPassword ? "text" : "password"}
              className="w-full rounded-e-none rounded-s-lg border border-default-200 px-4 py-3 text-black focus:border-primary dark:bg-default-50"
            />
          </div>
          <button
            className="password-toggle ms-[1px] inline-flex items-center justify-center rounded-e-lg border border-s-0 border-default-200 bg-white px-4 py-3 dark:bg-default-50"
            type="button"
            onClick={() => setShowPassword(!showPassword)}
          >
            {showPassword ? (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="h-6 w-6 text-default-600"
                height="20"
                width="20"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"
                />
              </svg>
            ) : (
              <svg
                stroke="currentColor"
                fill="none"
                strokeWidth="2"
                viewBox="0 0 24 24"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-default-600"
                height="20"
                width="20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path>
                <circle cx="12" cy="12" r="3"></circle>
              </svg>
            )}
          </button>
        </div>
      </div>

      <div className="my-3 flex justify-between">
        <a
          href="#"
          onClick={() => setIsStaff(!isStaff)}
          className="text-sm text-default-600 underline"
        >
          {isStaff ? "Admin" : "Staff"} login
        </a>

        <Link
          className="float-right text-end text-sm text-default-600 underline"
          href="/password/forgot"
        >
          Forgot Password?
        </Link>
      </div>

      {isStaff && (
        <div className="relative mb-6 max-w-full">
          <label
            className="mb-2 block text-sm font-medium text-default-900"
            htmlFor="identifier"
          >
            Staff ID
          </label>
          <div className="relative max-w-full">
            <input
              type="text"
              placeholder="Enter your staff ID"
              {...register("identifier", { required: true })}
              className="form-input w-full rounded-lg border border-default-200 bg-white px-4 py-3 dark:bg-default-50"
            />
          </div>
        </div>
      )}

      <button
        type="submit"
        className="mt-5 w-full rounded-lg bg-primary px-6 py-3 text-base capitalize text-white transition-all hover:bg-default-500"
      >
        Log In
      </button>
    </form>
  );
}
