import Pagination from "./pagination";
import { ReactNode, isValidElement } from "react";

export default function PaginatedTable<M>({
  records,
  columns,
  title,
  path,
  tools,
  paginate = true,
}: {
  records: PaginatedData<M>;
  columns: TableColumn<M>[];
  title?: JSX.Element | string;
  path: string;
  tools?: ReactNode;
  paginate?: boolean;
}) {
  return (
    <div className="relative overflow-x-auto">
      {tools && tools}

      <table className="w-full text-left text-sm text-default-500 dark:text-default-400">
        <thead className="bg-default-50 text-xs uppercase text-default-700 dark:bg-default-700 dark:text-default-400">
          <tr>
            <th scope="col" className="p-4">
              <div className="flex items-center">
                <input
                  id="checkbox-all-search"
                  type="checkbox"
                  className="h-4 w-4 rounded border-default-300 bg-default-100 text-default-600 focus:ring-2 focus:ring-default-500 dark:border-default-600 dark:bg-default-700 dark:ring-offset-default-800 dark:focus:ring-default-600 dark:focus:ring-offset-default-800"
                />
                <label htmlFor="checkbox-all-search" className="sr-only">
                  checkbox
                </label>
              </div>
            </th>
            {columns.map((column) => (
              <th
                scope="col"
                key={column.id}
                className={column.class || "px-6 py-3"}
              >
                {isValidElement(column.title) ? (
                  column.title
                ) : (
                  <span className="sub-text">{column.title}</span>
                )}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {records?.data?.map((item, index) => (
            <tr
              key={`u-${index}`}
              className="border-b bg-white hover:bg-default-50 dark:border-default-700 dark:bg-default-800 dark:hover:bg-default-600"
            >
              <td className="w-4 p-4">
                <div className="flex items-center">
                  <input
                    id="checkbox-table-search-1"
                    type="checkbox"
                    className="h-4 w-4 rounded border-default-300 bg-default-100 text-default-600 focus:ring-2 focus:ring-default-500 dark:border-default-600 dark:bg-default-700 dark:ring-offset-default-800 dark:focus:ring-default-600 dark:focus:ring-offset-default-800"
                  />
                  <label htmlFor="checkbox-table-search-1" className="sr-only">
                    checkbox
                  </label>
                </div>
              </td>

              {columns.map((column) => (
                <td key={column.id} className={column.class || "px-6 py-4"}>
                  {column.render ? (
                    column.render(item)
                  ) : (
                    <span>{(item as any)[column.id]}</span>
                  )}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>

      {paginate && (
        <div className="flex items-center justify-center bg-white p-4 dark:bg-default-900">
          <Pagination meta={records.meta} path={path} />
        </div>
      )}
    </div>
  );
}
