interface Meta {
  currentPage: number;
  totalPages: number;
  perPage: number;
  totalRecords: number;
}

interface PaginationProps {
  meta: Meta;
  path: string;
  onPageChange: (page: number) => void;
}

export default function Pagination({
  meta,
  path,
  onPageChange,
}: PaginationProps) {
  const { currentPage, totalPages } = meta;

  if (totalPages <= 1) return null;

  const handlePageClick = (page: number) => {
    onPageChange(page);
  };

  const renderPageNumbers = () => {
    let pages = [];
    for (let i = 1; i <= totalPages; i++) {
      pages.push(
        <button
          key={i}
          className={`px-3 py-2 ${
            i === currentPage
              ? "bg-default-600 text-white"
              : "bg-white text-default-600"
          } border border-default-300 rounded mx-1`}
          onClick={() => handlePageClick(i)}
        >
          {i}
        </button>
      );
    }
    return pages;
  };

  return (
    <nav>
      <div className="flex justify-center">
        {currentPage > 1 && (
          <button
            className="px-3 py-2 bg-white border border-default-300 rounded mx-1"
            onClick={() => handlePageClick(currentPage - 1)}
          >
            Previous
          </button>
        )}
        {renderPageNumbers()}
        {currentPage < totalPages && (
          <button
            className="px-3 py-2 bg-white border border-default-300 rounded mx-1"
            onClick={() => handlePageClick(currentPage + 1)}
          >
            Next
          </button>
        )}
      </div>
    </nav>
  );
}
