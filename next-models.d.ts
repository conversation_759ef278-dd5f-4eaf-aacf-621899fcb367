type PaginationMeta = {
	total: number;
	perPage: number;
	currentPage: number;
	lastPage: number;
	firstPage: number;
	firstPageUrl: string;
	lastPageUrl: string;
	nextPageUrl: string | null;
	previousPageUrl: string | null;
};

interface AttachmentContract {
	name: string;
	url: string;
	size: number;
	extname: string;
	mimeType: string;
	isLocal: boolean;
	isPersisted: boolean;
	isDeleted: boolean;
}

interface DatabaseNotification {
	id: string;
	data: Record<string, string | number | any>;
	created_at: string;
	updated_at: string;
	read_at: string;
}

interface Role {
	id: string
	name: string
	createdAt: string
	updatedAt: string
  }

interface DbRole {
	id: string;
	name: string;
	createdAt: string
	updatedAt: string
}

interface Permission {
	id: string;
	name: string;
}

interface Device {
	name: string;
	id: string;
	details: string;
	status: string;
	createdAt: DateTime;
	updatedAt: DateTime;
	deletedAt: DateTime;
	$forceDelete: boolean;
	trashed: boolean;
	token: string;
	userId: string;
	meta: Record<string, any>;
}

interface User {
	id: string;
	title: string;
	user_id?: string
	firstName: string;
	lastName: string;
	gender: string | null;
	dob: string | null;
	email: string;
	phone: string;
	idpass: string;
	rememberMeToken: string | null;
	details: string | null;
	location: Record<string, any> | null;
	geom: string | null;
	avatar?: AttachmentContract | null;
	createdAt: string;
	updatedAt: string;
	name: string;
	status: string;
	avatarUrl: string;
	initials: string;
	createdAt: string;
	updatedAt: string;
	devices: Device[];
	roles: Role[];
	permissions: Permission[];
	notifications: DatabaseNotification[];
	first_name?: string
	last_name?: string

	// computed
	identifier: string;
	online: boolean;
	vendorId: string;

	meta?:{ username: null, online: false }
}

interface VendorType {
	id: string;
	name: string;
	details: string;
	slug: string;
	serviceId: string;
	image: AttachmentContract | null;
	createdAt: string;
	updatedAt: string;

	service: Service;
	vendorCategories: VendorCategory[];
}

interface VendorCategory {
	id: string;
	name: string;
	details: string;
	image: AttachmentContract | null;
	vendorTypeId: string;
	createdAt: string;
	updatedAt: string;
	type: VendorType;
}

interface Vendor {
	id: string;
	name: string;
	slug: string;
	email: string;
	phone: string;
	reg: string;
	kra: string;
	active: boolean;
	featured: boolean;
	permit: string;
	details: string | null;
	location: Record<string, any> | null;
	geom: string | null;
	logo: AttachmentContract | null;
	cover: AttachmentContract | null;
	hours: { schedule: ScheduleHours[] } | null;
	createdAt: string;
	updatedAt: string;
	logoUrl: string;
	vendorCategoryId: string;
	createdAt: string;
	updatedAt: string;
	category: VendorCategory;
	employees: (User & {
		role: string;
	})[];
	userId: string;
}


interface StaffUpdateData {
	identifier: string
	firstName: string
	lastName: string
	phone: string
	idpass: string
	vendorId: string
	userId: string
	email: string
	roles: any
  }

interface NewStaff {
	vendorId: string
	identifier: string
	firstName: string
	lastName: string
	email: string
	phone: string
	idpass: string
	gender?: string
	dob?: string
	password?: string
	role?: string
	details?: string
  }


interface Branch {
	id: string;
	name: string;
	details: string;
	email: string;
	phone: string;
	image: AttachmentContract | null;
	cover: AttachmentContract | null;
	vendorId: string;
	location: Record<string, any> | null;
	geom: string | null;
	hours: { schedule: ScheduleHours[] } | null;
	createdAt: string;
	updatedAt: string;
	vendor: Vendor;
	sections: Section[];
	staff: User[];
	sectionCount: number;
	staffCount: number;
	orderCount: number;
}

interface CustomerGroup {
	id: string;
	vendorId: string;
	branchId: string;
	name: string;
	details: string;
	image: AttachmentContract;
	createdAt: string;
	updatedAt: string;
}

interface MessageTemplate {
	id: string;
	name: string;
	content: string;
	slug: string;
	active: boolean;
	createdAt: string;
	updatedAt: string;
	deletedAt: string;
	image: AttachmentContract | null;
	imageUrl: string;
	messages: Message[];
}

interface Message {
	id: string;
	details: string;
	userId: string;
	templateId: string;
	createdAt: string;
	updatedAt: string;
	deletedAt: string;
	template: MessageTemplate;
	author: User;
}

interface Section {
	id: string;
	name: string;
	details: string;
	image: AttachmentContract | null;
	branchId: string;
	createdAt: string;
	updatedAt: string;
	branch: Branch;
}

interface Lot {
	id: string;
	name: string;
	image: AttachmentContract | null;
	details: string;
	branchId: string;
}

interface ScheduleHours {
	day: string;
	from: string;
	to: string;
}

interface ScheduleForm {
	hours: ScheduleHours[];
}

interface Industry {
	id: string;
	name: string;
	details: string;
	image: AttachmentContract | null;
	createdAt: string;
	updatedAt: string;
}

interface Speciality {
	id: string;
	name: string;
	details: string;
	serviceId: string;
	image: AttachmentContract | null;

	service: Service;
}

interface Task {
	id: string;
	name: string;
	slug: string;
	details: string;
	active: boolean;
	image: AttachmentContract | null;
	imageUrl: string;
	createdAt: string;
	updatedAt: string;

	services: Service[];
}

interface Service {
	id: string;
	name: string;
	slug: string;
	details: string;
	active: boolean;
	image: AttachmentContract | null;
	taskId: string;
	task: Task;
	createdAt: string;
	updatedAt: string;
}

interface ProductType {
	id: string;
	name: string;
	details: string;
	serviceId: string;
	image: AttachmentContract | null;
	createdAt: string;
	updatedAt: string;

	service: Service;
}

interface ProductCategory {
	id: string;
	name: string;
	slug: string;
	details: string;
	image: AttachmentContract | null;
	productTypeId: string;
	productType: ProductType;
	createdAt: string;
	updatedAt: string;
}

interface FieldType {
	key: string;
	label: string;
}

interface FieldOPtion {
	value: string;
	label: string;
}

interface FormField {
	id: string;
	name: string;
	label: string;
	defaultValue: string;
	type: string;
	placeholder: string;
	required: boolean;
	attrs?: Record<string, string | number | boolean>;
	options?: Record<string, string | number>[];
	repeatable?: boolean;
	repeats?: number;
	min?: number;
	max?: number;
	hasCost?: boolean;
	cost?: number;
	multiple?: boolean;
}

interface FormSection {
	id: string;
	name: string;
	details: string;
	skippable?: boolean;
	repeatable?: boolean;
	repeats?: number;
	hasCost?: boolean;
	cost?: number;
	fields: FormField[];
}

interface ProductForm {
	id: string;
	name: string;
	details: string;
	sections: FormSection[];
	action: "Save" | "Send" | "Save and Send";
	link: string;
	image: AttachmentContract;
	productId: string;
	auth: { [x: string]: {} | undefined };
	headers: { [x: string]: {} | undefined };
	createdAt: string;
	updatedAt: string;
}

interface FormTemplate {
	id: string;
	name: string;
	details: string;
	sections: FormSection[];
	createdAt: string;
	updatedAt: string;
}

interface ProductPayload extends Omit<Product, "accompaniments" | "upsells"> {
	productTypeId: string;
	imageUpload: File;
	galleryUpload: File[];
	hasExpiry: boolean;
	tagIds: number[];
	accompaniments: {
		[key: string]: { id: string; price: number; name: string };
	};
	upsells: { [key: string]: { id: string; price: number; name: string } };
}

enum MeasurementUnit {
	KG = "kg",
	G = "g",
	LB = "lb",
	OZ = "oz",
	L = "l",
	ML = "ml",
	PT = "pt",
	GAL = "gal",
	UNIT = "unit",
	PACK = "pack",
	BOX = "box",
	SET = "set",
	PAIR = "pair",
	DOZEN = "dozen",
	BUNDLE = "bundle",
	ROLL = "roll",
	BAG = "bag",
	CASE = "case",
	EACH = "each",
	OTHER = "other",
	MM = "mm",
	CM = "cm",
	M = "m",
	IN = "in",
	FT = "ft",
	YD = "yd",
	SQM = "sqm",
	SQFT = "sqft",
	SQYD = "sqyd",
	ACRE = "acre",
	HA = "ha",
	SQMM = "sqmm",
	SQCM = "sqcm",
}

interface Product {
	id: string;
	name: string;
	ref: string;
	details: string;
	price: number;
	discounted: number | null;
	stock: number;
	active: boolean;
	featured: boolean;
	type: "Physical" | "Digital" | "Service";
	condition: "New" | "Used" | "Refurbished";
	status: "Draft" | "Pending" | "Published" | "Unpublished" | "Archived";
	availability: "In Stock" | "Out of Stock" | "Pre Order";
	shipping: "Free" | "Paid" | "Pickup";
	unit: MeasurementUnit;
	mode: "Single" | "Variable";
	payment: "Free" | "Prepaid" | "Postpaid";
	visibility: "Private" | "Public" | "Restricted";
	productCategoryId: string;
	userId: string;
	vendorId: string;
	branchId: string;
	serviceId: string;
	formId: string;
	meta: Record<string, any>;
	extra: Record<string, any>;
	image: AttachmentContract | null;
	expiresAt: string | null;
	createdAt: string;
	updatedAt: string;
	gallery: AttachmentContract[];
	category: ProductCategory;
	service: Service;
	vendor: Vendor;
	branch: Branch;
	forms: ProductForm[];
	tags: Tag[];
	accompaniments: Product[];
	upsells: Product[]
}

interface OrderItem extends Product {
	quantity: number;
	meta: Record<string, any>;
}

interface Order {
	id: string;
	userId: string;
	vendorId: string;
	branchId: string;
	sectionId: string;
	lotId: string;
	staffId: string;
	action: string;
	delivery: string;
	type: string;
	ref: string;
	meta: Record<string, any>;
	status: string;
	total: number;
	qrCode: string;
	createdAt: string;
	updatedAt: string;
	acceptedAt: string

	customer: User;
	staff: User;
	vendor: Vendor;
	branch: Branch;
	items: OrderItem[];
	invoices: Invoice[];
	payments: Payment[];
}

interface TempOrder {
	id: string;
	userId: string;
	vendorId: string;
	branchId: string;
	sectionId: string;
	lotId: string;
	staffId: string;
	action: string;
	delivery: string;
	type: string;
	ref: string;
	meta: Record<string, any>;
	status: string;
	total: number;
	qrCode: string;
	createdAt: string;
	updatedAt: string;
	acceptedAt: string

	customer: User;
	staff: User;
	vendor: Vendor;
	branch: Branch;
	items: OrderItem[];
	invoices: Invoice[];
	payments: Payment[];
}

interface Lead {
	id: string;
	name: string;
	details: string;
	image: AttachmentContract | null;
	price: number;
	discounted: number;
	active: boolean;
	featured: boolean;
	maxClaim: number;
	vendorId: string;
	vendor: Vendor;
	categoryId: string;
	category: Category;
	startAt: string;
	endAt: string;
	createdAt: string;
	updatedAt: string;
}

interface ProductCreate extends Omit<Product, "image" | "startAt" | "endAt"> {
	image: File | null;
	startAt: Date;
	endAt: Date;
}

interface Setting {
	id: string;
	name: string;
	options: Record<string, any>;
	branchId?: string;
	createdAt: string;
	updatedAt: string;

	branch: Branch | null;
}

interface Tag {
	id: number;
	name: string;
	details: string;
	createdAt: string;
	updatedAt: string;
}

interface Invoice {
	id: string;
	number: string;
	amount: number;
	orderId: string;
	userId: string;
	status: string;
	createdAt: string;
	updatedAt: string;

	order: Order;
	customer: User;
	payments: Payment[];
}

interface Payment {
	id: string;
	amount: number;
	method: string;
	ref: string;
	receipt: string;
	invoiceId: string;
	userId: string;
	status: string;
	createdAt: string;
	updatedAt: string;

	barcode?: string;

	invoice: Invoice;
	customer: User;
}

interface Claim {
	id: string;
	productId: string;
	userId: string;
	status: string;
	user: User;
	product: Product;
}

interface Campaign {
	id: string;
	name: string;
	details: string;
	link?: string;
	status: "Draft" | "Pending" | "Approved" | "Expired";
	vendorId: string;
	branchId: string;
	startDate: string;
	endDate: string;
	image: AttachmentContract | null;
	createdAt: string;
	updatedAt: string;
	vendor: BelongsTo<typeof Vendor>;
	branch: BelongsTo<typeof Branch>;
}

interface VendorRating {
	name: string;
	id: number;
	customerId: string;
	vendorId: string;
	points: number;
	comment: string;
	meta: Record<string, any>;
	createdAt: string;
	updatedAt: string;

	customer: User;
	vendor: Vendor;
}

interface TableColumn<M> {
	id: string;
	title: JSX.Element | string;
	class?: string;
	render?: (item: M) => JSX.Element;
}

interface PaginatedData<M> {
	meta: PaginationMeta;
	data: M[];
	sum: Record<string, number>;
}
