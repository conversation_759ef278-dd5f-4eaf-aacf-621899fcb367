{"name": "nextauth-credential", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbo", "build": "next build", "start": "next start", "format": "prettier --write \"src/**/*.{js,ts,jsx,tsx}\"", "lint": "next lint"}, "dependencies": {"@caldwell619/react-kanban": "^0.0.11", "@ckeditor/ckeditor5-build-classic": "^41.1.0", "@ckeditor/ckeditor5-react": "^6.2.0", "@fullcalendar/bootstrap5": "^6.1.11", "@fullcalendar/core": "^6.1.11", "@fullcalendar/daygrid": "^6.1.11", "@fullcalendar/interaction": "^6.1.11", "@fullcalendar/react": "^6.1.11", "@fullcalendar/timegrid": "^6.1.11", "@iconify/json": "^2.2.210", "@nextui-org/react": "^2.3.6", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-slot": "^1.0.2", "@react-stately/data": "^3.11.3", "@types/qrcode": "^1.5.5", "@types/react-datepicker": "^7.0.0", "apexcharts": "^3.46.0", "axios": "^1.6.7", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "date-fns": "^3.3.1", "dayjs": "^1.11.13", "dnd-core": "^16.0.1", "firebase": "^10.8.1", "framer-motion": "^11.2.0", "immutability-helper": "^3.1.1", "next": "14.1.0", "next-auth": "^5.0.0-beta.25", "next-themes": "^0.3.0", "next-translate": "^2.6.2", "nextjs-toploader": "^1.6.12", "qrcode": "^1.5.3", "react": "^18.2.0", "react-apexcharts": "^1.4.1", "react-datepicker": "^7.6.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-google-places-autocomplete": "^4.0.1", "react-hook-form": "^7.50.1", "react-hot-toast": "^2.4.1", "react-icons": "^5.0.1", "react-phone-number-input": "^3.4.3", "react-quill": "^2.0.0", "react-select": "^5.8.0", "react-simple-star-rating": "^5.1.7", "react-tailwindcss-datepicker": "^1.6.6", "sharp": "^0.33.4", "simplebar-react": "^3.2.4", "swiper": "^11.0.7", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@iconify/tailwind": "^1.1.1", "@types/node": "^20.11.22", "@types/react": "^18.2.60", "@types/react-dom": "^18.2.19", "autoprefixer": "^10.4.17", "eslint": "^8.57.0", "eslint-config-next": "14.1.0", "next-translate-plugin": "^2.6.2", "postcss": "^8.4.35", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.14", "tailwindcss": "^3.4.1", "typescript": "^5.3.3"}}