"use server";

import { api } from "@/lib/api";
import { revalidatePath } from "next/cache";

export const createProduct = async (data: FormData) => {
  await api.post("products", data);

  revalidatePath("/products");
};

export const updateProduct = async (product: Product) => {
  await api.put(`products/${product.id}`, product);

  revalidatePath("/products");
};

export const deleteProduct = async (product: FormData) => {
  await api.destroy(product.get("id") as string, "products");

  revalidatePath("/products");
};

export const loadVendors = async (s: string) => {
  if (s.length > 3) {
    const res = await api.get<PaginatedData<Vendor>>(`vendors?s=${s}`);

    return res?.data ?? [];
  }

  return [];
};
