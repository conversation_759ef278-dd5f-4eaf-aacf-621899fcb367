"use server";

import { api } from "@/lib/api"
import { revalidatePath } from "next/cache";

export const removeLot=async ({itemToDelete, sectionRevalidatePath}:{itemToDelete: string, sectionRevalidatePath: string}) =>{
   try {
    const res = await api.destroy(itemToDelete, 'lots')
    revalidatePath(sectionRevalidatePath);
    return res
   } catch (error) {
    return error
   }
}



export const removeSection=async ({itemToDelete, sectionRevalidatePath}:{itemToDelete: string, sectionRevalidatePath: string}) =>{
    try {
     const res = await api.destroy(itemToDelete, 'sections')
     revalidatePath(sectionRevalidatePath);
     return res
    } catch (error) {
     return error
    }
 }

