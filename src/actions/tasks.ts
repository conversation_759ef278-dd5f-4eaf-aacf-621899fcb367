"use server";

import { api } from "@/lib/api";
import { revalidatePath } from "next/cache";
import { cache } from "react";

export const fetchTasks = cache(
  (vendorId?: string, searchParams?: Record<string, string>) =>
    vendorId
      ? api.get<PaginatedData<Task>>(`vendors/${vendorId}/tasks`, searchParams)
      : api.get<PaginatedData<Task>>(`tasks`, searchParams),
);

export const toggleTaskStatus = async (task: Task) => {
  "use server";

  await api.put(`tasks/${task.id}`, { active: !task.active });
};

export const createTask = async (formData: FormData) => {
  "use server";

  await api.post(`tasks`, formData);

  revalidatePath(`/tasks`);
};

export const deleteTask = async (formData: FormData) => {
  "use server";

  await api.destroy(formData.get("id")?.toString()!, `tasks`);

  revalidatePath(`/tasks`);
};
