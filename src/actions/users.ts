"use server";

import { auth } from "@/auth";
import { api } from "@/lib/api";
import { revalidatePath } from "next/cache";
import { cache } from "react";

export const storeUser = async (data: User) => {
  const user = await api.post<User>("users", data);

  return user || null;
};


export const deleteStaff = async (data: FormData) => {
  "use server";
  return await api.destroy(data.get("id") as string, "users");
};

export const fetchStaff = async ({ branchId, vendorId, searchParams }: { vendorId?: string, branchId?: string, searchParams?: Record<string, string> }) => {

  switch (true) {
    case branchId !== undefined:
      const branchStaffRes = await api.get<PaginatedData<User>>(`branches/${branchId}/staff`, searchParams)
      return branchStaffRes;
    case vendorId !== undefined:
      const vendorStaffRes = await api.get<PaginatedData<User>>(`branches/${branchId}/staff`, searchParams)
      return vendorStaffRes;
    default:
      const defaultRes = api.get<PaginatedData<User>>(`users`, searchParams)
      return defaultRes;
  }
};


export const updateActive = async (active: boolean, user: User) => {
  "use server";

  try {
    return await api.put<User>(`users/${user.id}`, {
      ...user,
      status: active? "Active":"Inactive",
    });
  } catch (error) {
    console.log("Error updating user status")
  }
};

export const getFilteredUsers = async (searchString: string): Promise<User[]> => {
  const res = await api.get<PaginatedData<User>>(`/search?search=${searchString}`)
  // console.log("res", res)
  const users: User[] = res?.data as User[]
  return users
};

export const assignRoleToUser = async ({ user_id, role }: { user_id: string, role: string }) => {
  try {
    const res = await api.post<any>("/auth/addroletouser", { user_id: user_id, role: role })
    console.log("RESPONSE: ", res)
    return "success"
  } catch (error) {
    return "error"
  }
}

export const fetchUserRoles = async (id: string): Promise<Role[]> => {
  const res = await api.get<any>(`users/${id}`)
  return res.roles as Role[]
}

export const fetchBaseUrl = async (): Promise<string> => process.env.API_BASE_URL as string


export const registerNewStaff = async (branchId: string, data: NewStaff) => {
  const res = await api.post(`branches/${branchId}/staff`, data);
  return res
}

export const getSession = async () => await auth();

export const getAllRoles = async () => await api.get<PaginatedData<Role>>('roles') as PaginatedData<Role>