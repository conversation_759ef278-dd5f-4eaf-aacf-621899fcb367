"use server";

import { api } from "@/lib/api";
import { revalidatePath } from "next/cache";

export const searchUsers = async (s: string) => {
  return await api.get<PaginatedData<User>>("users", { s });
};

export const fetchVendorTypes = async () => {
  return await api.get<PaginatedData<VendorType>>("vendor-types", {
    with: "vendorCategories",
    per: 100,
  });
};

export const storeVendor = async (data: FormData) => {
  const vendor = await api.post<Vendor>("vendors", data);

  return vendor || null;
};

export const storeVendorGrouping = async (data: {
  tasks: string[];
  services: string[];
  vendorId: string;
}) => {
  const { vendorId, tasks, services } = data;

  await api.post(`vendors/${vendorId}/tasks`, {
    tasks,
  });

  await api.post(`vendors/${vendorId}/services`, {
    services,
  });
};

export const storeVendorSpecialization = async (data: {
  vendorId: string;
  specialization: string[];
  serviceId: string;
}) => {
  const { vendorId, specialization, serviceId } = data;

  await api.put<Vendor>(`vendors/${vendorId}`, { serviceId });

  await api.post<PaginatedData<Speciality>>(
    `vendors/${vendorId}/specialities`,
    { specialization },
  );
};

export const updateActive = async (active: boolean, vendor: Vendor) => {
  "use server";

  return await api.put<Vendor>(`vendors/${vendor.id}`, {
    ...vendor,
    active,
  });
};

export const updateFeatured = async (featured: boolean, vendor: Vendor) => {
  "use server";

  return await api.put<Vendor>(`vendors/${vendor.id}`, {
    ...vendor,
    featured,
  });
};

export const deleteVendor = async (data: FormData) => {
  "use server";

  await api.destroy(data.get("id") as string, "vendors");

  revalidatePath("/vendors");
};
