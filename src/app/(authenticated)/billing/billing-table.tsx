"use client";

import { CustomTable } from "@/components/custom-table";
import { Icon } from "@/components/icon";
import { imagePath } from "@/lib/api";
import { Chip, User } from "@nextui-org/react";
import { format as formatDate } from "date-fns";
import Link from "next/link";
import toast from "react-hot-toast";

export default function BillsTable({
  data,
  meta,
  deleteOrder,
}: {
  data: Order[];
  meta?: PaginationMeta;
  deleteOrder: (data: FormData) => Promise<void>;
}) {
  const removeOrder = async (id: string) => {
    const data = new FormData();
    data.append("id", id);

    toast.promise(deleteOrder(data), {
      loading: "Deleting order...",
      success: "Order deleted!",
      error: "Failed to delete order",
    });
  };

  return (
    <CustomTable
      title="Bills"
      columns={[
        {
          name: "Customer",
          uid: "customer",
          renderCell: (order: Order) => (
            <Link
              href={`/customers/${order.customer?.id}`}
              className="flex items-center space-x-2"
            >
              <User
                name={order.customer?.name}
                description={order.customer?.email}
                avatarProps={{
                  src: `${imagePath(order.customer?.avatar?.url, order.customer?.avatarUrl)}`,
                }}
              />
            </Link>
          ),
        },
        {
          name: "Amount",
          uid: "amount",
          renderCell: (order: Order) => <>KES {order.total}</>,
        },

        {
          name: "Status",
          uid: "status",
          renderCell: (order) => (
            <Chip
              color={
                order.status === "Completed"
                  ? "success"
                  : order.status === "Pending"
                    ? "warning"
                    : order.status === "Placed"
                      ? "secondary"
                      : "default"
              }
              variant="flat"
              className="text-xs"
            >
              {order.status}
            </Chip>
          ),
        },
        // Vendor Name
        {
          name: "Vendor",
          uid: "vendor",
          renderCell: (order: Order) => (
            <Link
              href={`/vendors/${order.vendor?.id}`}
              className="flex items-center space-x-2"
            >
              <User
                name={order.vendor?.name}
                description={order.vendor?.email}
                avatarProps={{
                  src: `${imagePath(order.branch?.vendor?.logoUrl)}`,
                }}
              />
            </Link>
          ),
        },
        {
          name: "Date",
          uid: "createdAt",
          sortable: true,
          renderCell: (order: Order) => (
            <>
              {formatDate(
                new Date(order.createdAt),
                "EEE, MMM dd, yyyy hh:mm a",
              )}
            </>
          ),
        },
        {
          name: "Actions",
          uid: "actions",
          renderCell: (order: Order) => (
            <div className="flex w-fit items-center gap-5">
              <Link href={`/orders/${order.id}`}>
                <Icon
                  name="icon-[mingcute--eye-line]"
                  classNames="text-primary"
                />
              </Link>

              {/* <Link href={`/orders/${order.id}/edit`}>
                <Icon
                  name="icon-[mage--edit-pen-fill]"
                  classNames="text-primary"
                />
              </Link> */}

              {/* <button
                name="id"
                onClick={() => removeOrder(order.id)}
                type="submit"
                className="text-red-500"
              >
                <Icon name="icon-[mingcute--delete-2-line]" />
              </button> */}
            </div>
          ),
        },
      ]}
      data={data}
      meta={meta}
      filter={{
        column: "status",
        values: [
          { name: "Pending", value: "Pending" },
          { name: "Placed", value: "Placed" },
          { name: "Processing", value: "Processing" },
          { name: "Delivering", value: "Delivering" },
          { name: "Delivered", value: "Delivered" },
          { name: "Completed", value: "Completed" },
          { name: "Cancelled", value: "Cancelled" },
        ],
      }}
    />
  );
}
