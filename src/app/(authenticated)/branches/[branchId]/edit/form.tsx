"use client";

import { useState, useEffect, ChangeEvent } from "react";
import { Submit<PERSON><PERSON><PERSON>, Controller, useForm } from "react-hook-form";
import { toast } from "react-hot-toast";
import Image from "next/image";
import makeAnimated from "react-select/animated";
import { imagePath } from "@/lib/api";
import PhoneInput from "react-phone-number-input";

export default function BranchEditForm({
  updateBranch,
  defaultValues,
}: {
  updateBranch: (data: FormData) => Promise<void>;
  defaultValues: Branch;
}) {
  const [creating, setCreating] = useState(false);
  const [preview, setPreview] = useState<string>(
    imagePath(defaultValues.image?.url),
  );
  const [previewc, setPreviewC] = useState<string>(
    imagePath(defaultValues.cover?.url),
  );

  const animatedComponents = makeAnimated();

  const {
    watch,
    handleSubmit,
    register,
    reset,
    control,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<
    Branch & {
      upload: File;
      uploadc: File;
      taskId: string;
      serviceId?: string;
      branchTypeId?: string;
      branchId?: string;
    }
  >({
    defaultValues,
  });

  const branch = watch();

  const handleUploadedFile = (event: ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;

    if (files) {
      const file = files[0];

      if (file) {
        setValue("upload", file);
      }

      const urlImage = URL.createObjectURL(file);

      setPreview(urlImage);
    }
  };

  const editBranch: SubmitHandler<
    Branch & { upload: File; uploadc: File; taskId: string }
  > = (branch: Branch & { upload: File; uploadc: File; taskId: string }) => {
    const data = new FormData();

    data.append("name", branch.name);
    data.append("details", branch.details);
    data.append("email", branch.email);
    data.append("phone", branch.phone.slice(1, branch.phone.length));

    if (branch.upload) {
      data.append("image", branch.upload);
    }

    toast
      .promise(
        updateBranch(data),
        {
          loading: "Updating branch...",
          success: "Branch has been updated 👌",
          error: "Could not update branch 🤯",
        },
        {
          position: "bottom-center",
        },
      )
      .then(() => {
        reset();

        setCreating(false);
      })
      .catch((e) => {
        console.error(e);
      });
  };

  useEffect(() => {
    // imagePath(branch.image?.url).then(setPreview);
  }, []);

  return (
    <form
      action="#"
      className="space-y-4 overflow-y-auto"
      onSubmit={handleSubmit(editBranch)}
    >
      <div className="grid grid-cols-12">
        <label
          htmlFor="name"
          className="col-span-5 mb-2 block text-sm font-medium text-default-900 dark:text-white"
        >
          Branch name
        </label>
        <input
          type="text"
          id="name"
          {...register("name")}
          className="col-span-7 block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
          placeholder="Type branch name"
          required
        />
      </div>

      <div className="grid grid-cols-12">
        <label
          htmlFor="email"
          className="col-span-5 mb-2 block text-sm font-medium text-default-900 dark:text-white"
        >
          Email address
        </label>
        <input
          type="text"
          id="email"
          {...register("email")}
          className="col-span-7 block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
          placeholder="Email"
          required
        />
      </div>
      <div className="grid grid-cols-12">
        <label
          htmlFor="name"
          className="col-span-5 mb-2 block text-sm font-medium text-default-900 dark:text-white"
        >
          Phone number
        </label>

        <div className="form-input col-span-7 relative w-full max-w-full rounded-lg border border-default-200 bg-white px-4 py-3 dark:bg-default-50">
          <Controller
            name="phone"
            control={control}
            render={({ field: { onChange, value } }) => (
              <PhoneInput
                placeholder="Enter phone number"
                value={value}
                onChange={onChange}
                defaultCountry="KE"
                className="w-full"
              />
            )}
          />
        </div>
      </div>

      <div className="grid grid-cols-12">
        <label
          htmlFor="description"
          className="col-span-5 mb-2 block text-sm font-medium text-default-900 dark:text-white"
        >
          Description
        </label>
        <textarea
          id="description"
          rows={4}
          {...register("details")}
          className=".5 col-span-7 block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
          placeholder="Enter event description here"
        />
      </div>

      <div className="grid grid-cols-12">
        <div className="col-span-5 block ">
          <label
            htmlFor="logo"
            className="mb-2 text-sm font-medium text-default-900 dark:text-white"
          >
            Branch image
          </label>
          <p className="py-5 text-xs text-default-500 dark:text-default-400">
            Click image to upload a branch image
          </p>
        </div>
        <div className="col-span-7 flex space-x-4">
          <label className="flex w-full items-center justify-center rounded-lg border border-dashed border-default-300 bg-default-50 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600">
            <input
              type="file"
              name="logo"
              id="logo"
              className="hidden"
              onChange={handleUploadedFile}
            />
            {preview ? (
              <Image
                src={preview}
                alt="preview"
                width={100}
                height={50}
                className="h-auto w-full"
              />
            ) : (
              <p>Click to select file</p>
            )}
          </label>
        </div>
      </div>

      <div className="flex w-full justify-end">
        <button
          type="submit"
          className="w-42 justify-center rounded-lg bg-primary px-5 py-3 text-center text-sm font-medium text-white hover:bg-default-800 focus:outline-none focus:ring-4 focus:ring-default-300 dark:bg-primary dark:hover:bg-primary dark:focus:ring-default-800"
        >
          Save branch details
        </button>
      </div>
    </form>
  );
}
