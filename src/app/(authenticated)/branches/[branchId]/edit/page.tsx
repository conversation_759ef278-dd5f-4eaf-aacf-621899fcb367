import { api } from "@/lib/api";
import { cache } from "react";
import BranchEditForm from "./form";
import { revalidatePath } from "next/cache";
import Link from "next/link";

const fetchBranch = cache((id: string) => api.get<Branch>(`branches/${id}`));

export const generateMetadata = async ({
  params: { branchId },
}: {
  params: { branchId: string };
}) => {
  const branch = await fetchBranch(branchId);

  return {
    title: `Edit ${branch?.name}`,
  };
};

export default async function page({
  params: { branchId },
}: {
  params: { branchId: string };
}) {
  const branch = await fetchBranch(branchId);

  const updateBranch = async (data: FormData) => {
    "use server";

    await api.put(`branches/${branchId}`, data);

    revalidatePath(`/branches/${branchId}/edit`);
  };

  return (
    <div className="p-4">
      <div className="mb-4 flex justify-between">
        <div></div>
        <Link
          href={`/branches/${branchId}`}
          className="rounded-lg bg-primary px-5 py-2 text-white"
        >
          Back to branch
        </Link>
      </div>
      {branch && (
        <BranchEditForm updateBranch={updateBranch} defaultValues={branch} />
      )}
    </div>
  );
}
