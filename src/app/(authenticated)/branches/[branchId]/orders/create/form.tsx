"use client";

import { SubmitHand<PERSON>, useForm } from "react-hook-form";
import toast from "react-hot-toast";

export default function OrderForm({
	processOrder,
}: {
	processOrder: (data: Order) => Promise<Order | undefined>;
}) {
	const { register, handleSubmit } = useForm<Order>();

	const onSubmit: SubmitHandler<Order> = async data => {
		toast.promise(processOrder(data), {
			loading: "Creating order...",
			success: "Order created!",
			error: "Error creating order",
		});
	};

	return (
		<form onSubmit={handleSubmit(onSubmit)} className="space-y-4 lg:space-x-9 lg:grid lg:grid-cols-2 xl:grid-cols-3">
			<div>
				<label>Status</label>

				<input
					className="w-full px-5 py-3 border border-primary rounded-lg"
					{...register("status")}
				/>
			</div>
            <div>
				<label>Status</label>

				<input
					className="w-full px-5 py-3 border border-primary rounded-lg"
					{...register("status")}
				/>
			</div><div>
				<label>Status</label>

				<input
					className="w-full px-5 py-3 border border-primary rounded-lg"
					{...register("status")}
				/>
			</div><div>
				<label>Status</label>

				<input
					className="w-full px-5 py-3 border border-primary rounded-lg"
					{...register("status")}
				/>
			</div>

            <div className="flex justify-end">
                <button type="submit" className="bg-primary px-5 py-3 rounded-lg text-white">
                    Submit order
                </button>
            </div>
		</form>
	);
}
