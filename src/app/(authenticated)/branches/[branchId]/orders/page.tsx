import PaginatedTable from "@/components/table";
import { api } from "@/lib/api";
import Link from "next/link";

export const generateMetadata = async ({
	params: { branchId },
}: {
	params: { branchId: string };
}) => {
	const branch = await api.get<Branch>(`branches/${branchId}`);
	return {
		title: `Orders In ${branch?.name}`,
	};
};

export default async function page({
	params: { branchId },
}: {
	params: { branchId: string };
}) {
	const orders = await api.get<PaginatedData<Order>>(
		`branches/${branchId}/orders`
	);

	return (
		<div>
			{orders && (
				<PaginatedTable<Order>
					records={orders}
					columns={[
						{
							id: "customer",
							title: "Customer",
							render: order => (
								<Link href={`/customers/${order.customer.id}`}>
									{order.customer.name}
								</Link>
							),
						},
						{
							id: "total",
							title: "Total",
							render: order => <>KES ${order.total}</>,
						},

						{
							id: "status",
							title: "Status",
						},
					]}
					path={`/branches/${branchId}/orders`}
					tools={<div className="w-full flex justify-between items-center px-4 py-4">
						<div>
							<input type="text" className="px-5 py-3 border border-primary rounded-lg" />
						</div>

						<div>
							<Link href={`/branches/${branchId}/orders/create`} className="bg-primary text-white px-5 py-2 rounded-lg">
								Add order
							</Link>
						</div>
					</div>}
				/>
			)}
		</div>
	);
}
