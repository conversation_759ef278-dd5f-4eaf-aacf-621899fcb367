import { api, imagePath } from "@/lib/api";
import Image from "next/image";
import Link from "next/link";
import { cache } from "react";

const fetchBranch = cache((branchId: string) =>
  api.get<Branch>(`branches/${branchId}`),
);

export const generateMetadata = async ({
  params,
}: {
  params: {
    branchId: string;
  };
}): Promise<Record<string, any>> => {
  const branch = await fetchBranch(params.branchId);

  return {
    title: `${branch?.vendor?.name} (${branch?.name})`,
    description: "Branch",
    keywords: ["branch"],
  };
};

export default async function page({
  params,
}: {
  params: {
    branchId: string;
  };
}) {
  const branch = await fetchBranch(params.branchId);

  const products = await api.get<PaginatedData<Product>>(
    `branches/${params.branchId}/products`,
  );

  return (
    <div className="page-content space-y-6 p-6">
      <div className="mb-6 rounded-lg border border-default-200 p-6">
        <div
          className={`h-40 rounded-t-lg bg-default-50 bg-[url('${imagePath(
            branch?.cover?.url || branch?.vendor?.cover?.url,
            null,
          )}')] bg-cover bg-center bg-no-repeat`}
        />

        <div className="flex items-center gap-3 md:-mt-14 md:items-end">
          <Image
            src={imagePath(branch?.image?.url, null)}
            className="w-32 bg-default-50"
            alt={branch?.name || "Branch"}
            width={112}
            height={112}
          />

          <div>
            <h4 className="mb-1 text-base font-medium text-default-800">
              {branch?.name}
            </h4>
            <p className="text-sm text-default-600">
              {branch?.location?.address}
            </p>
          </div>
        </div>
      </div>
      <div className="grid grid-cols-1 gap-6 xl:grid-cols-3">
        <div className="xl:col-span-2">
          <div className="mb-6 grid grid-cols-3 gap-4">
            <Link
              href={`/branches/${params.branchId}/staff`}
              className="flex flex-col items-center justify-center rounded-lg border border-default-200 py-4"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="h-6 w-6"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"
                />
              </svg>

              <h1> {branch?.staff?.length} Staff</h1>
            </Link>

            <Link
              href={`/branches/${params.branchId}/orders`}
              className="flex flex-col items-center justify-center rounded-lg border border-default-200 py-4"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="h-6 w-6"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M2.25 3h1.386c.51 0 .955.343 1.087.835l.383 1.437M7.5 14.25a3 3 0 0 0-3 3h15.75m-12.75-3h11.218c1.121-2.3 2.1-4.684 2.924-7.138a60.114 60.114 0 0 0-16.536-1.84M7.5 14.25 5.106 5.272M6 20.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0Zm12.75 0a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0Z"
                />
              </svg>

              <h1> {branch?.orderCount} Orders</h1>
            </Link>

            <Link
              href={`/branches/${params.branchId}/sections`}
              className="flex flex-col items-center justify-center rounded-lg border border-default-200 py-4"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="h-6 w-6"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M2.25 7.125C2.25 6.504 2.754 6 3.375 6h6c.621 0 1.125.504 1.125 1.125v3.75c0 .621-.504 1.125-1.125 1.125h-6a1.125 1.125 0 0 1-1.125-1.125v-3.75ZM14.25 8.625c0-.621.504-1.125 1.125-1.125h5.25c.621 0 1.125.504 1.125 1.125v8.25c0 .621-.504 1.125-1.125 1.125h-5.25a1.125 1.125 0 0 1-1.125-1.125v-8.25ZM3.75 16.125c0-.621.504-1.125 1.125-1.125h5.25c.621 0 1.125.504 1.125 1.125v2.25c0 .621-.504 1.125-1.125 1.125h-5.25a1.125 1.125 0 0 1-1.125-1.125v-2.25Z"
                />
              </svg>

              <h1> {branch?.sectionCount} Sections</h1>
            </Link>
          </div>
          <div className="overflow-hidden rounded-lg border border-default-200">
            <div className="border-b border-b-default-200 p-6">
              <h4 className="mb-4 text-xl font-medium text-default-900">
                Menu
              </h4>
              <div className="flex flex-wrap items-center gap-4">
                <div className="hs-dropdown relative inline-flex">
                  <button
                    type="button"
                    className="hs-dropdown-toggle flex items-center gap-2 rounded-md bg-default-100 px-4 py-3 text-sm font-medium text-default-700 transition-all xl:px-5"
                  >
                    Popular : Best Seller{" "}
                    <svg
                      stroke="currentColor"
                      fill="none"
                      strokeWidth="2"
                      viewBox="0 0 24 24"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      height="16"
                      width="16"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="m6 9 6 6 6-6"></path>
                    </svg>
                  </button>
                  <div className="hs-dropdown-menu hs-dropdown-open:opacity-100 z-20 mt-4 hidden min-w-[200px] rounded-lg border border-default-100 bg-white p-1.5 opacity-0 shadow-[rgba(17,_17,_26,_0.1)_0px_0px_16px] transition-[opacity,margin] dark:bg-default-50">
                    <ul className="flex flex-col gap-1">
                      <li>
                        <span className="flex items-center gap-3 rounded bg-default-100 px-3 py-2 font-normal text-default-700 transition-all hover:bg-default-100 hover:text-default-700">
                          Best Seller
                        </span>
                      </li>
                      <li>
                        <span className="flex items-center gap-3 rounded px-3 py-2 font-normal text-default-600 transition-all hover:bg-default-100 hover:text-default-700">
                          High to Low
                        </span>
                      </li>
                      <li>
                        <span className="flex items-center gap-3 rounded px-3 py-2 font-normal text-default-600 transition-all hover:bg-default-100 hover:text-default-700">
                          Low to High
                        </span>
                      </li>
                    </ul>
                  </div>
                </div>
                <div className="hs-dropdown relative inline-flex">
                  <button
                    type="button"
                    className="hs-dropdown-toggle flex items-center gap-2 rounded-md bg-default-100 px-4 py-3 text-sm font-medium text-default-700 transition-all xl:px-5"
                  >
                    Upload Date : Newest{" "}
                    <svg
                      stroke="currentColor"
                      fill="none"
                      strokeWidth="2"
                      viewBox="0 0 24 24"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      height="16"
                      width="16"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="m6 9 6 6 6-6"></path>
                    </svg>
                  </button>
                  <div className="hs-dropdown-menu hs-dropdown-open:opacity-100 z-20 mt-4 hidden min-w-[200px] rounded-lg border border-default-100 bg-white p-1.5 opacity-0 shadow-[rgba(17,_17,_26,_0.1)_0px_0px_16px] transition-[opacity,margin] dark:bg-default-50">
                    <ul className="flex flex-col gap-1">
                      <li>
                        <span className="flex items-center gap-3 rounded bg-default-100 px-3 py-2 font-normal text-default-700 transition-all hover:bg-default-100 hover:text-default-700">
                          Newest
                        </span>
                      </li>
                      <li>
                        <span className="flex items-center gap-3 rounded px-3 py-2 font-normal text-default-600 transition-all hover:bg-default-100 hover:text-default-700">
                          Oldest
                        </span>
                      </li>
                    </ul>
                  </div>
                </div>
                <div className="hs-dropdown relative inline-flex">
                  <button
                    type="button"
                    className="hs-dropdown-toggle flex items-center gap-2 rounded-md bg-default-100 px-4 py-3 text-sm font-medium text-default-700 transition-all xl:px-5"
                  >
                    Rating : Average{" "}
                    <svg
                      stroke="currentColor"
                      fill="none"
                      strokeWidth="2"
                      viewBox="0 0 24 24"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      height="16"
                      width="16"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="m6 9 6 6 6-6"></path>
                    </svg>
                  </button>
                  <div className="hs-dropdown-menu hs-dropdown-open:opacity-100 z-20 mt-4 hidden min-w-[200px] rounded-lg border border-default-100 bg-white p-1.5 opacity-0 shadow-[rgba(17,_17,_26,_0.1)_0px_0px_16px] transition-[opacity,margin] dark:bg-default-50">
                    <ul className="flex flex-col gap-1">
                      <li>
                        <span className="flex items-center gap-3 rounded bg-default-100 px-3 py-2 font-normal text-default-700 transition-all hover:bg-default-100 hover:text-default-700">
                          Average
                        </span>
                      </li>
                      <li>
                        <span className="flex items-center gap-3 rounded px-3 py-2 font-normal text-default-600 transition-all hover:bg-default-100 hover:text-default-700">
                          Good
                        </span>
                      </li>
                      <li>
                        <span className="flex items-center gap-3 rounded px-3 py-2 font-normal text-default-600 transition-all hover:bg-default-100 hover:text-default-700">
                          Best
                        </span>
                      </li>
                    </ul>
                  </div>
                </div>
                <div className="ms-auto">
                  <a
                    className="inline-flex rounded-md bg-primary px-6 py-2.5 text-sm text-white hover:bg-default-500 "
                    href="/yum_r/admin/add-dish"
                  >
                    <svg
                      stroke="currentColor"
                      fill="none"
                      strokeWidth="2"
                      viewBox="0 0 24 24"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="me-2 inline-flex align-middle"
                      height="20"
                      width="20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="M5 12h14"></path>
                      <path d="M12 5v14"></path>
                    </svg>
                    Add Dish
                  </a>
                </div>
              </div>
            </div>
            <div className="overflow-x-auto">
              <div className="inline-block min-w-full align-middle">
                <div className="divide-y divide-default-200 rounded-lg">
                  <div className="overflow-hidden">
                    <table className="min-w-full divide-y divide-default-200">
                      <thead className="bg-default-100/75">
                        <tr>
                          <th scope="col" className="px-4 py-3 pr-0">
                            <div className="flex h-5 items-center">
                              <input
                                id="hs-table-search-checkbox-all"
                                type="checkbox"
                                className="form-checkbox h-5 w-5 rounded border border-default-300 bg-transparent text-primary focus:border-default-300 focus:ring focus:ring-default-200 focus:ring-opacity-50 focus:ring-offset-0"
                              />
                              <label
                                htmlFor="hs-table-search-checkbox-all"
                                className="sr-only"
                              >
                                Checkbox
                              </label>
                            </div>
                          </th>
                          <th
                            scope="col"
                            className="whitespace-nowrap px-6 py-3 text-start text-base font-medium text-default-500"
                          >
                            Dish
                          </th>
                          <th
                            scope="col"
                            className="whitespace-nowrap px-6 py-3 text-start text-base font-medium text-default-500"
                          >
                            Category
                          </th>
                          <th
                            scope="col"
                            className="whitespace-nowrap px-6 py-3 text-start text-base font-medium text-default-500"
                          >
                            Price
                          </th>
                          <th
                            scope="col"
                            className="whitespace-nowrap px-6 py-3 text-start text-base font-medium text-default-500"
                          >
                            Order ID
                          </th>
                          <th
                            scope="col"
                            className="whitespace-nowrap px-6 py-3 text-start text-base font-medium text-default-500"
                          >
                            Rating
                          </th>
                          <th
                            scope="col"
                            className="whitespace-nowrap px-6 py-3 text-start text-base font-medium text-default-500"
                          >
                            Actions
                          </th>
                        </tr>
                      </thead>
                      <tbody className="divide-y divide-default-200">
                        {products?.data.map((product) => (
                          <tr key={product.id}>
                            <td className="py-3 pl-4">
                              <div className="flex h-5 items-center">
                                <input
                                  id={product.id}
                                  type="checkbox"
                                  className="form-checkbox h-5 w-5 rounded border border-default-300 bg-transparent text-primary focus:border-default-300 focus:ring focus:ring-default-200 focus:ring-opacity-50 focus:ring-offset-0"
                                />
                                <label htmlFor={product.id} className="sr-only">
                                  Checkbox
                                </label>
                              </div>
                            </td>
                            <td className="whitespace-nowrap px-6 py-4">
                              <div className="flex items-center gap-3">
                                <Image
                                  src={imagePath(product.image?.url, null)}
                                  width="48"
                                  height="48"
                                  className="size-12 rounded-full"
                                  alt="Italian Pizza"
                                />
                                <h5 className="text-base font-medium text-default-700">
                                  {product.name}
                                </h5>
                              </div>
                            </td>
                            <td className="whitespace-nowrap px-6 py-4 text-base text-default-800">
                              {" "}
                              Pizza
                            </td>
                            <td className="whitespace-nowrap px-6 py-4 text-base text-default-800">
                              {product.price}
                            </td>
                            <td className="whitespace-nowrap px-6 py-4 text-base text-default-800">
                              {" "}
                              1001
                            </td>
                            <td className="whitespace-nowrap px-6 py-4 text-base text-default-800">
                              <div className="flex items-center justify-start gap-1">
                                <svg
                                  stroke="currentColor"
                                  fill="currentColor"
                                  strokeWidth="0"
                                  viewBox="0 0 576 512"
                                  className="fill-yellow-400 text-yellow-400"
                                  height="18"
                                  width="18"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"></path>
                                </svg>
                                <svg
                                  stroke="currentColor"
                                  fill="currentColor"
                                  strokeWidth="0"
                                  viewBox="0 0 576 512"
                                  className="fill-yellow-400 text-yellow-400"
                                  height="18"
                                  width="18"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"></path>
                                </svg>
                                <svg
                                  stroke="currentColor"
                                  fill="currentColor"
                                  strokeWidth="0"
                                  viewBox="0 0 576 512"
                                  className="fill-yellow-400 text-yellow-400"
                                  height="18"
                                  width="18"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"></path>
                                </svg>
                                <svg
                                  stroke="currentColor"
                                  fill="currentColor"
                                  strokeWidth="0"
                                  viewBox="0 0 576 512"
                                  className="fill-yellow-400 text-yellow-400"
                                  height="18"
                                  width="18"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"></path>
                                </svg>
                                <svg
                                  stroke="currentColor"
                                  fill="currentColor"
                                  strokeWidth="0"
                                  viewBox="0 0 640 512"
                                  className="text-yellow-400"
                                  height="18"
                                  width="18"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <path d="M320 376.4l.1-.1 26.4 14.1 85.2 45.5-16.5-97.6-4.8-28.7 20.7-20.5 70.1-69.3-96.1-14.2-29.3-4.3-12.9-26.6L320.1 86.9l-.1 .3V376.4zm175.1 98.3c2 12-3 24.2-12.9 31.3s-23 8-33.8 2.3L320.1 439.8 191.8 508.3C181 514 167.9 513.1 158 506s-14.9-19.3-12.9-31.3L169.8 329 65.6 225.9c-8.6-8.5-11.7-21.2-7.9-32.7s13.7-19.9 25.7-21.7L227 150.3 291.4 18c5.4-11 16.5-18 28.8-18s23.4 7 28.8 18l64.3 132.3 143.6 21.2c12 1.8 22 10.2 25.7 21.7s.7 24.2-7.9 32.7L470.5 329l24.6 145.7z"></path>
                                </svg>
                              </div>
                            </td>
                            <td className="whitespace-nowrap px-6 py-4 text-center">
                              <div className="hs-dropdown relative inline-flex [--placement:bottom-right]">
                                <button
                                  type="button"
                                  className="hs-dropdown-toggle inline-flex font-medium text-default-600 transition-all"
                                >
                                  <svg
                                    stroke="currentColor"
                                    fill="none"
                                    strokeWidth="2"
                                    viewBox="0 0 24 24"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    height="22"
                                    width="22"
                                    xmlns="http://www.w3.org/2000/svg"
                                  >
                                    <circle cx="12" cy="12" r="1"></circle>
                                    <circle cx="12" cy="5" r="1"></circle>
                                    <circle cx="12" cy="19" r="1"></circle>
                                  </svg>
                                </button>
                                <div className="hs-dropdown-menu hs-dropdown-open:opacity-100 z-20 mt-4 hidden min-w-[150px] rounded-lg border border-default-100 bg-white p-1.5 opacity-0 shadow-[rgba(17,_17,_26,_0.1)_0px_0px_16px] transition-[opacity,margin] dark:bg-default-50">
                                  <ul className="flex flex-col gap-1">
                                    <li>
                                      <button className="flex w-full items-center gap-3 rounded px-3 py-2 font-normal text-default-600 transition-all hover:bg-default-100 hover:text-default-700">
                                        Edit
                                      </button>
                                    </li>
                                    <li>
                                      <button className="flex w-full items-center gap-3 rounded px-3 py-2 font-normal text-default-600 transition-all hover:bg-default-100 hover:text-default-700">
                                        View
                                      </button>
                                    </li>
                                    <li>
                                      <button className="flex w-full items-center gap-3 rounded px-3 py-2 font-normal text-default-600 transition-all hover:bg-default-100 hover:text-default-700">
                                        Delete
                                      </button>
                                    </li>
                                  </ul>
                                </div>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="xl:col-span-1">
          <div className="mb-6 rounded-lg border border-default-200">
            <div className="border-b border-b-default-300 p-6">
              <h4 className="text-xl font-medium text-default-900">
                Seller Personal Detail
              </h4>
            </div>
            <div className="px-6 py-5">
              <table cellPadding={10}>
                <tbody>
                  <tr>
                    <td className="text-start text-base font-medium">
                      Owner Name:
                    </td>
                    <td className="text-start">Hollie Bruggen</td>
                  </tr>
                  <tr>
                    <td className="text-start text-base font-medium">
                      Status:
                    </td>
                    <td className="text-start">active</td>
                  </tr>
                  <tr>
                    <td className="text-start text-base font-medium">Email:</td>
                    <td className="text-start"><EMAIL></td>
                  </tr>
                  <tr>
                    <td className="text-start text-base font-medium">
                      Contact No:
                    </td>
                    <td className="text-start">1078832848</td>
                  </tr>
                  <tr>
                    <td className="text-start text-base font-medium">
                      Orders:
                    </td>
                    <td className="text-start">17</td>
                  </tr>
                  <tr>
                    <td className="text-start text-base font-medium">
                      Location:
                    </td>
                    <td className="text-start">Mae Lan</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          <div className="mb-6 rounded-lg border border-default-200">
            <div className="border-b border-b-default-300 p-6">
              <h4 className="text-xl font-medium text-default-900">
                Customer Reviews
              </h4>
            </div>
            <div className="p-6">
              <div className="mb-6">
                <div className="flex items-center gap-2">
                  <h5 className="text-sm">5</h5>
                  <div className="flex h-2 w-full overflow-hidden rounded-lg bg-default-100">
                    <div className="w-full rounded-lg bg-yellow-400"></div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <h5 className="text-sm">4</h5>
                  <div className="flex h-2 w-full overflow-hidden rounded-lg bg-default-100">
                    <div className="w-4/5 rounded-lg bg-yellow-400"></div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <h5 className="text-sm">3</h5>
                  <div className="flex h-2 w-full overflow-hidden rounded-lg bg-default-100">
                    <div className="w-3/5 rounded-lg bg-yellow-400"></div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <h5 className="text-sm">2</h5>
                  <div className="flex h-2 w-full overflow-hidden rounded-lg bg-default-100">
                    <div className="w-2/5 rounded-lg bg-yellow-400"></div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <h5 className="text-sm">1</h5>
                  <div className="flex h-2 w-full overflow-hidden rounded-lg bg-default-100">
                    <div className="w-1/5 rounded-lg bg-yellow-400"></div>
                  </div>
                </div>
              </div>
              <div className="mb-6 flex justify-around">
                <div className="text-center">
                  <h2 className="mb-1 text-2xl font-medium text-default-900">
                    4.5{" "}
                    <svg
                      stroke="currentColor"
                      fill="currentColor"
                      strokeWidth="0"
                      viewBox="0 0 576 512"
                      className="inline-flex fill-yellow-400 align-middle text-yellow-400"
                      height="20"
                      width="20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"></path>
                    </svg>
                  </h2>
                  <p className="block text-xs text-default-600">452 Reviews</p>
                </div>
                <div className="text-center">
                  <h2 className="mb-1 text-2xl font-medium text-default-900">
                    91%
                  </h2>
                  <p className="block text-xs text-default-600">Recommended</p>
                </div>
              </div>
              <div className="mb-4">
                <div className="mb-4 flex items-center gap-3">
                  <Image
                    src="/yum_r/assets/avatar1-lkSFncXM.png"
                    className="h-11 w-11 rounded-full"
                    alt="avatar"
                  />
                  <div className="flex-grow">
                    <h4 className="mb-1 text-xs text-default-700">
                      Kianna Stanton{" "}
                      <span className="text-default-600">🇺🇸US</span>
                    </h4>
                    <h4 className="text-xs text-green-400">Verified Buyer</h4>
                  </div>
                  <div>
                    <svg
                      stroke="currentColor"
                      fill="currentColor"
                      strokeWidth="0"
                      viewBox="0 0 576 512"
                      className="inline-flex fill-yellow-400 align-middle text-yellow-400"
                      height="20"
                      width="20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"></path>
                    </svg>
                    <svg
                      stroke="currentColor"
                      fill="currentColor"
                      strokeWidth="0"
                      viewBox="0 0 576 512"
                      className="inline-flex fill-yellow-400 align-middle text-yellow-400"
                      height="20"
                      width="20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"></path>
                    </svg>
                    <svg
                      stroke="currentColor"
                      fill="currentColor"
                      strokeWidth="0"
                      viewBox="0 0 576 512"
                      className="inline-flex fill-yellow-400 align-middle text-yellow-400"
                      height="20"
                      width="20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"></path>
                    </svg>
                    <svg
                      stroke="currentColor"
                      fill="currentColor"
                      strokeWidth="0"
                      viewBox="0 0 576 512"
                      className="inline-flex fill-yellow-400 align-middle text-yellow-400"
                      height="20"
                      width="20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"></path>
                    </svg>
                    <svg
                      stroke="currentColor"
                      fill="currentColor"
                      strokeWidth="0"
                      viewBox="0 0 576 512"
                      className="inline-flex fill-yellow-400 align-middle text-yellow-400"
                      height="20"
                      width="20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"></path>
                    </svg>
                  </div>
                </div>
                <h5 className="mb-2 text-sm text-default-600">
                  SO DELICIOUS 🍯💯
                </h5>
                <p className="text-sm text-default-600">
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed
                  do eiusmod tempor incididunt ut labore et dolore magna aliqua.
                  Ut enim ad minim veniam, quis nostrud exercitation.
                </p>
              </div>
              <div className="mb-4">
                <div className="mb-4 flex items-center gap-3">
                  <Image
                    src="/yum_r/assets/avatar2-e3ZdIYj6.png"
                    className="h-11 w-11 rounded-full"
                    alt="avatar"
                  />
                  <div className="flex-grow">
                    <h4 className="mb-1 text-xs text-default-700">
                      Ryan Rhiel Madsen{" "}
                      <span className="text-default-600">🇺🇸US</span>
                    </h4>
                    <h4 className="text-xs text-green-400">Verified Buyer</h4>
                  </div>
                  <div>
                    <svg
                      stroke="currentColor"
                      fill="currentColor"
                      strokeWidth="0"
                      viewBox="0 0 576 512"
                      className="inline-flex fill-yellow-400 align-middle text-yellow-400"
                      height="20"
                      width="20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"></path>
                    </svg>
                    <svg
                      stroke="currentColor"
                      fill="currentColor"
                      strokeWidth="0"
                      viewBox="0 0 576 512"
                      className="inline-flex fill-yellow-400 align-middle text-yellow-400"
                      height="20"
                      width="20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"></path>
                    </svg>
                    <svg
                      stroke="currentColor"
                      fill="currentColor"
                      strokeWidth="0"
                      viewBox="0 0 576 512"
                      className="inline-flex fill-yellow-400 align-middle text-yellow-400"
                      height="20"
                      width="20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"></path>
                    </svg>
                    <svg
                      stroke="currentColor"
                      fill="currentColor"
                      strokeWidth="0"
                      viewBox="0 0 576 512"
                      className="inline-flex fill-yellow-400 align-middle text-yellow-400"
                      height="20"
                      width="20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"></path>
                    </svg>
                    <svg
                      stroke="currentColor"
                      fill="currentColor"
                      strokeWidth="0"
                      viewBox="0 0 576 512"
                      className="inline-flex fill-yellow-400 align-middle text-yellow-400"
                      height="20"
                      width="20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"></path>
                    </svg>
                  </div>
                </div>
                <h5 className="mb-2 text-sm text-default-600">
                  SO DELICIOUS 🍯💯
                </h5>
                <p className="text-sm text-default-600">
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed
                  do eiusmod tempor incididunt ut labore et dolore magna aliqua.
                  Ut enim ad minim veniam, quis nostrud exercitation.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
