import PaginatedTable from "@/components/table";
import { api } from "@/lib/api";
import Link from "next/link";
import { cache } from "react";

const fetchBranch = cache((id: string) => api.get<Branch>(`branches/${id}`));

export const generateMetadata = async ({
  params,
}: {
  params: {
    branchId: string;
  };
}): Promise<Record<string, any>> => {
  const branch = await fetchBranch(params.branchId);

  return {
    title: `Products In ${branch?.name}`,
    description: "Branch",
    keywords: ["branch"],
  };
};

export default async function page({
  params,
}: {
  params: {
    branchId: string;
  };
}) {
  const products = await api.get<PaginatedData<Product>>(
    `branches/${params.branchId}/products`,
  );

  return (
    <div className="py-4">
      {products && (
        <PaginatedTable<Product>
          records={products}
          columns={[
            {
              id: "name",
              title: "Name",
              render: (record) => (
                <Link href={`/products/${record.id}`}>{record.name}</Link>
              ),
            },
            { id: "price", title: "Price" },
            { id: "stock", title: "Stock" },
            {
              id: "actions",
              title: "Actions",
              render: (record) => (
                <Link href={`/products/${record.id}`}>Edit</Link>
              ),
            },
          ]}
          tools={
            <div className="mb-4 flex items-center justify-between px-4">
              <p></p>

              <Link
                href={`/products/create?branchId=${params.branchId}`}
                className="rounded-lg bg-primary px-5 py-2 text-white transition-colors duration-200 ease-in-out hover:bg-default-700"
              >
                Add Product
              </Link>
            </div>
          }
          title={"Products"}
          path="/products"
        />
      )}
    </div>
  );
}
