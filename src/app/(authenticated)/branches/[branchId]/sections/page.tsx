import PaginatedTable from "@/components/table";
import { api } from "@/lib/api";
import Link from "next/link";
import React from "react";
import { cache } from "react";

const fetchBranch = cache((id: string) => api.get<Branch>(`branches/${id}`));

export const generateMetadata = async ({
  params,
}: {
  params: {
    branchId: string;
  };
}): Promise<Record<string, any>> => {
  const branch = await fetchBranch(params.branchId);

  return {
    title: branch?.name + " Sections",
    description: "Branch",
    keywords: ["branch"],
  };
};

export default async function page({
  params,
}: {
  params: {
    branchId: string;
  };
}) {
  const sections = await api.get<PaginatedData<Section>>(
    `branches/${params.branchId}/sections`,
  );

  return (
    <div className="py-4">
      {sections && (
        <PaginatedTable<Section>
          records={sections}
          columns={[
            {
              id: "name",
              title: "Name",
              render: (record) => (
                <Link href={`/${params.branchId}/sections/${record.id}`}>
                  {record.name}
                </Link>
              ),
            },
            {
              id: "details",
              title: "Description",
            },
          ]}
          title="Sections"
          path={`/branches/${params.branchId}/sections`}
          tools={<></>}
        />
      )}
    </div>
  );
}
