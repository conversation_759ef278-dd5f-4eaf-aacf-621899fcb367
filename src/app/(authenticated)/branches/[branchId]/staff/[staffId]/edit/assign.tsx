"use client";

import React from "react";
import { useState } from "react";
import toast from "react-hot-toast";
import Select from "react-select";

export default function StaffUpdateRolesAssignForm({
  roles,
  user,
  onAssign,
}: {
  roles: Role[];
  user: User & { roles: Role[] };
  onAssign: (selectedRoles: string[], userId: string) => Promise<void>;
}) {
  const [selectedRoles, setSelectedRoles] = useState<string[]>(user.roles.map((r) => r.id));

  const onSubmit = () => {
    toast.promise(onAssign(selectedRoles, user.id), {
      loading: "Assigning roles...",
      success: "Roles assigned!",
      error: "Error assigning roles"
    });
  };

  return (
    <form
      id={`assign-roles-${user.id}`}
      onSubmit={(e) => {
        e.preventDefault();
        onSubmit();
      }}
      className="flex space-x-2"
    >
      <Select
        options={roles}
        defaultValue={user.roles}
        getOptionLabel={(role) => role.name}
        getOptionValue={(role) => role.id}
        isMulti
        isSearchable
        classNames={{
          container: () => "w-2/3 ml-2 rounded-lg",
          control: () => "!rounded-lg !border-default-600",
        }}
        onChange={(pickedRoles) => setSelectedRoles(pickedRoles.map((role) => role.id))}
      />

      <button
        type="submit"
        className="rounded-lg bg-primary px-4 py-2 text-white"
      >
        Assign
      </button>
    </form>
  );
}
