import { api } from "@/lib/api";
import { revalidatePath } from "next/cache";
import { Metadata } from "next";
import { cache } from "react";
import Link from "next/link";
import StaffUpdateForm from "./form";

const fetchStaff = cache((branchId: string, staffId: string) =>
  api.get<User & { identifier: string; vendorId: string }>(
    `/branches/${branchId}/staff/${staffId}`,
  )
);


const fetchStaffRoles = cache((staffId: string) => api.get<User>(`/users/${staffId}`))
export const generateMetadata = async ({
  params,
}: {
  params: {
    branchId: string;
    staffId: string;
  };
}): Promise<Metadata> => {
  const staff = await fetchStaff(params.branchId, params.staffId);
  const staffWithRoles = await fetchStaffRoles(params.staffId)
  let staffRoles: Role[] = []
  if (staffWithRoles?.roles) {
    staffRoles = staffWithRoles.roles
  }
  return {
    title: staff?.name,
    description: `Edit Staff ${staff?.details}`,
  };
};

export default async function page({
  params,
}: {
  params: {
    branchId: string;
    staffId: string;
  };
}) {
  const staff = await fetchStaff(params.branchId, params.staffId);
  let staffToUpdate: User = { ...staff } as User
  const staffWithRoles = await fetchStaffRoles(params.staffId)
  let staffRoles: Role[] = []
  if (staffWithRoles?.roles) {
    staffRoles = staffWithRoles.roles
    staffToUpdate = { ...staff, roles: staffRoles } as User
  }

  const updateStaff = async (formData: FormData, newRoles: Role[]) => {
    "use server";

    if (staff) {
      formData.append("vendorId", staff.vendorId);
      formData.append("userId", staff.id);
    }

    const dataToProcess: StaffUpdateData = {
      email: formData.get('email') as string,
      firstName: formData.get('firstName') as string,
      lastName: formData.get('lastName') as string,
      identifier: formData.get('identifier') as string,
      idpass: formData.get('idpass') as string,
      phone: formData.get('phone') as string,
      userId: formData.get('userId') as string,
      vendorId: formData.get('vendorId') as string,
      roles: newRoles
    }

    // Users table update
    const { identifier, vendorId, userId, roles, ...rest } = dataToProcess
    const updatedUserInfo = rest

    // Roles
    const updatedStaffRoleInfo = {
      user_id: params.staffId,
      roleIds: (roles as Role[]).map(role => role.id)
    }

    try {
      await api.put(`users/${params.staffId}`, updatedUserInfo)
      await api.put(`/branches/${params.branchId}/staff/${params.staffId}/replace-role`, updatedStaffRoleInfo);
      revalidatePath(`/branches/${params.branchId}/staff/${params.staffId}/edit`);
    } catch (error) {
      console.log("ERROR UPDATING USER:", error)
    }
  };

  return (
    <div className="p-4">
      <div className="mb-4 flex items-center justify-between">
        <h1 className="text-2xl text-primary">{staff?.details}</h1>
        <Link
          href={`/branches/${params.branchId}/staff`}
          className="flex items-center space-x-2 text-default-500"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
            className="h-6 w-6"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M6.75 15.75 3 12m0 0 3.75-3.75M3 12h18"
            />
          </svg>

          <span>Back to Staff</span>
        </Link>
      </div>

      {staff && (
        <StaffUpdateForm updateStaff={updateStaff} defaultValues={staffToUpdate} />
      )}
    </div>
  );
}
