import { api } from "@/lib/api";
import Link from "next/link";
import { cache } from "react";

const fetchStaff = cache((branchId: string, staffId: string) => api.get<User>(`/branches/${branchId}/staff/${staffId}`));

export const generateMetadata = async ({
  params,
}: {
  params: {
    branchId: string;
    staffId: string;
  };
}) => {
  const staff = await fetchStaff(params.branchId, params.staffId);

  return {
    title: staff?.name,
    description: `Edit Staff ${staff?.details}`,
  };
};

export default async function page({
  params,
}: {
  params: {
    branchId: string;
    staffId: string;
  };
}) {
  const staff = await fetchStaff(params.branchId, params.staffId);

  return (
    <div className="p-4">
      <h1>{staff?.name}</h1>

      <div>{staff?.phone}</div>

      <Link
        href={`/branches/${params.branchId}/staff/${params.staffId}/edit`}
        className="font-medium text-default-600 hover:underline dark:text-blue-500"
      >
        Edit
      </Link>
    </div>
  );
}
