"use client";

import AsyncSelect from "react-select/async";
import makeAnimated from "react-select/animated";
import { useEffect, useState } from "react";
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import { toast } from "react-hot-toast";
import { useClickOutside } from "@/hooks/useClickOutside";
import PhoneInput from "react-phone-number-input";
import { api } from "@/lib/api";
import { assignRoleToUser, fetchBaseUrl, getFilteredUsers } from "@/actions/users";

// const BASEURL: string = await fetchBaseUrl()

export default function BranchStaffCreateForm({
  branch,
  storeStaff,
  session,
  roles
}: {
  branch: Branch;
  storeStaff: (data: FormData) => Promise<void>;
  session: any;
  roles: Role[]
}) {

  const [BASEURL, setBaseUrl] = useState<string>()
  useEffect(() => { fetchBaseUrl().then(res => setBaseUrl(res)) }, [])

  const filterUsers = async (searchString: string): Promise<{ label: string, value: string }[]> => {
    if (searchString.length < 3) {
      return [];
    }

    try {
      const users: User[] = await getFilteredUsers(searchString);
      const formattedUsers = users.map(user => {
        return ({
          label: `${user.first_name} ${user.last_name}`,
          value: user.id,
        })
      });
      return formattedUsers;
    } catch (error) {
      return [];
    }
  };

  const [selectedUser, setSelectedUser] = useState<any>(null)
  const [creating, setCreating] = useState(false);
  const ref = useClickOutside(() => setCreating(false));
  const animatedComponents = makeAnimated();
  const {
    watch,
    handleSubmit,
    register,
    reset,
    setValue,
    control,
    formState: { errors, isSubmitting },
  } = useForm<User & { identifier: string; userId?: string; vendorId: string; role: string }>({
    defaultValues: {
      identifier: "",
      vendorId: branch.vendorId,
      role: "employee",
    },
  });


  // const userId = watch("userId");

  // const createStaff: SubmitHandler<User & { identifier: string; userId?: string; vendorId: string; role: string }> = async (
  //   employee
  // ) => {
  //   const data = new FormData();


  //   data.append("identifier", employee.identifier);
  //   data.append("role", employee.role);

  //   if (employee.userId) {
  //     data.append("userId", employee.userId);
  //   } else {
  //     data.append("firstName", employee.firstName);
  //     data.append("lastName", employee.lastName);
  //     data.append("email", employee.email);
  //     data.append("phone", employee.phone);
  //     data.append("idpass", employee.idpass);
  //   }



  //   // Convert `data2` FormData to a JSON object for `addRoleToUser`
  //   const data2Object: { [key: string]: string } = {};
  //   if (employee.userId) {
  //     data2Object["user_id"] = employee.userId;
  //   }
  //   data2Object["role"] = employee.role;

  //   // Retrieve token from session



  //   // Function to send JSON data for user role
  //   // const addRoleToUser = async (data: { [key: string]: string }) => {
  //   //   const e = {
  //   //     method: "POST",
  //   //     body: JSON.stringify(data), // Convert to JSON string
  //   //     headers: {
  //   //       "Content-Type": "application/json",
  //   //       "Authorization": `Bearer ${session?.accessToken}`, // Attach token to request
  //   //     },
  //   //   };

  //   //   // Log debug version of request
  //   //   const response = await fetch(`${BASEURL}/v1/auth/addroletouser`, e);
  //   //   if (!response.ok) {
  //   //     console.error("Failed to add role:", response.status, response.statusText);
  //   //     throw new Error("Failed to add role");
  //   //   }
  //   //   return response.json();
  //   // };

  //   const addRoleToUser = async (data: { [key: string]: string }) => {
  //     console.log("DATA TO BE ADDED:", data)
  //     };

  //   // Register user if they don't exist already

  //   // Attempt to add role to user
  //   try {
  //     await addRoleToUser(data2Object);

  //     // console.log("Data collected:", data)
  //     await toast.promise(storeStaff(data), {
  //       loading: "Creating staff...",
  //       success: "Staff created and role assigned successfully 👌",
  //       error: "Could not create staff or assign role 🤯",
  //     });
  //   } catch (error) {
  //     console.error("Error:", error);
  //     toast.error("Error creating staff or assigning role");
  //   }

  //   // Reset form and UI state
  //   reset();
  //   setCreating(false);
  // };


  const createStaff: SubmitHandler<User & { identifier: string; userId?: string; vendorId: string; role: string }> = async (
    employee
  ) => {
    const data = new FormData();


    data.append("identifier", employee.identifier);
    data.append("role", employee.role);

    if (employee.userId) {
      data.append("userId", employee.userId);
    } else {
      data.append("firstName", employee.firstName);
      data.append("lastName", employee.lastName);
      data.append("email", employee.email);
      data.append("phone", employee.phone.slice(1, employee.phone.length));
      data.append("idpass", employee.idpass);
    }


    const userRoleData: { [key: string]: string } = {};
    if (employee.userId) {
      userRoleData["user_id"] = employee.userId;
    }
    userRoleData["role"] = employee.role;

    try {
      switch (true) {
        case employee.userId !== undefined:
          await toast.promise(assignRoleToUser({ user_id: employee.userId as string, role: employee.role }), {
            loading: "Assigning role to staff...",
            success: "Role assigned successfully 👌",
            error: "Could not assign role 🤯",
          });
          break;
        case employee.userId === undefined && employee.identifier !== undefined:
          await toast.promise(storeStaff(data), {
            loading: "Creating staff...",
            success: "Staff created and role assigned successfully 👌",
            error: "Could not create staff or assign role 🤯",
          });
        break;
      }
    } catch (error) {
      console.error("Error:", error);
    }

    // Reset form and UI state
    reset();
    setCreating(false);
  };

  /*

  With existing user:
    - user id
    - identifier
    - role

  Without existing user:
    - first name
    - last name
    - email
    - phone
    - id pass
    - role
    - identifier
  */


  const logValues = async (data: any) => {

    if (selectedUser !== null) {
      const payload = {
        identifier: data.identifier,
        vendorId: data.vendorId,
        role: data.role,
        firstName: selectedUser.first_name,
        lastName: selectedUser.last_name,
        email: selectedUser.email,
        idpass: selectedUser.idpass
      }



      // try {
      //   assignRoleToUser( selectedUser.id, data.role).then(res=>{
      //     if(res === "success"){
      //       toast.success("Role assigned successfully 👌")
      //     } else {
      //       toast.error("Failed to assign the role(else)")
      //     }  
      //   })

      // } catch (error) {
      //   console.log("Error",error)
      //   toast.error("Failed to assign the role(catch)")
      // }
    }
  }



  return (
    <form onSubmit={handleSubmit(createStaff)}>
      <div className="space-y-4">
        <div className="mb-6 grid grid-cols-12">
          <label htmlFor="userId" className="col-span-5 mb-2 block text-sm font-medium text-default-900 dark:text-white">
            Select user
          </label>
          <Controller
            name="userId"
            control={control}
            rules={{ required: false }}
            render={({ field }) => (
              <AsyncSelect
                isClearable
                isSearchable
                loadOptions={async (s: string) => filterUsers(s)}
                value={selectedUser ?? undefined}
                getOptionLabel={(user: { label: string, value: string | number }) => user.label}
                getOptionValue={(user: { label: string, value: string }) => user.value}
                onChange={(user: any) => {
                  if (user) {
                    setValue("userId", user.value);
                    setSelectedUser(user);
                  } else {
                    setValue("userId", undefined);
                    setSelectedUser(null);
                  }
                }}
                components={animatedComponents}
                placeholder="Select user"
                className="col-span-7"
                classNames={{
                  control: () => "!py-[1px] !rounded-lg",
                  menu: () => "py-1",
                }}
              />
            )}
          />
        </div>

        {selectedUser === null && (
          <>
            <div className="grid grid-cols-12">
              <label htmlFor="name" className="col-span-5 mb-2 block text-sm font-medium text-default-900 dark:text-white">
                Staff name
              </label>
              <div className="col-span-7 flex">
                <input
                  type="text"
                  {...register("firstName")}
                  id="name"
                  className="block w-full rounded-l-lg border border-r-0 border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
                  placeholder="First name"
                  required
                />
                <input
                  type="text"
                  {...register("lastName")}
                  id="name"
                  className="block w-full rounded-r-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
                  placeholder="Last name"
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-12">
              <label htmlFor="email" className="col-span-5 mb-2 block text-sm font-medium text-default-900 dark:text-white">
                Staff email
              </label>
              <input
                type="email"
                {...register("email")}
                id="email"
                className="col-span-7 block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
                placeholder="Type staff email"
                required
              />
            </div>


            <div className="grid grid-cols-12">
              <label htmlFor="phone" className="col-span-5 mb-2 block text-sm font-medium text-default-900 dark:text-white">
                Staff phone
              </label>
              <div className="col-span-7 form-input relative w-full max-w-full rounded-lg border border-default-200 bg-white px-4 py-3 dark:bg-default-50">
                <Controller
                  name="phone"
                  control={control}
                  render={({ field: { onChange, value } }) => (
                    <PhoneInput
                      placeholder="Enter phone number"
                      value={value}
                      onChange={onChange}
                      defaultCountry="KE"
                      className="w-full"
                    />
                  )}
                />
              </div>
            </div>


            <div className="grid grid-cols-12">
              <label htmlFor="idpass" className="col-span-5 mb-2 block text-sm font-medium text-default-900 dark:text-white">
                Staff Passport/National ID
              </label>
              <input
                type="text"
                {...register("idpass")}
                id="idpass"
                className="col-span-7 block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
                placeholder="Type national ID/Passport number"
                required
              />
            </div>
          </>
        )}

        <div className="grid grid-cols-12">
          <label htmlFor="role" className="col-span-5 mb-2 block text-sm font-medium text-default-900 dark:text-white">
            Select Role
          </label>
          <select
            {...register("role")}
            id="role"
            className="col-span-7 block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
            defaultValue="employee"
          >
            <option value='' disabled>Select a role</option>
            {roles.length > 0 && roles.map(role => <option key={role.id} value={role.name}>{role.name}</option>)}
          </select>
        </div>

        <div className="grid grid-cols-12">
          <label htmlFor="identifier" className="col-span-5 mb-2 block text-sm font-medium text-default-900 dark:text-white">
            Staff ID
          </label>
          <input
            type="text"
            {...register("identifier")}
            id="identifier"
            className="col-span-7 block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
            placeholder="Type staff ID"
            required
          />
        </div>

        <div className="flex w-full justify-end">
          <button
            type="submit"
            disabled={isSubmitting}
            className="w-60 justify-center rounded-lg bg-primary px-5 py-3 text-center text-sm font-medium text-white hover:bg-default-800 focus:outline-none focus:ring-4 focus:ring-default-300 dark:bg-primary dark:hover:bg-primary dark:focus:ring-default-800"
          >
            Save staff details
          </button>
        </div>
      </div>
    </form>
  );
}
