import { api } from "@/lib/api";
import { auth } from "@/auth";
import { revalidatePath } from "next/cache";
import BranchStaffCreateForm from "./form";
import { Metadata } from "next";
import { cache } from "react";
import Link from "next/link";
import { toast } from "react-hot-toast";
import { registerNewStaff } from "@/actions/users";

const fetchBranch = cache((branchId: string) => 
  api.get<Branch>(`/branches/${branchId}`)
);

const fetchRoles = async ():Promise<Role[]> => {
  const res = await api.get<PaginatedData<Role>>(`/roles`)
  return (res && res.data)? res.data: []
};

// export const getFilteredUsers = async (searchString:string):Promise<User[]> => {
//   const res = await api.get<any>(`/search?search=${searchString}`)
//   const users:User[] = res?.data?res.data:[]
//   console.log("FILTERED USERS RESULT:(server)", users)
//   return users
// };

export const metadata: Metadata = {
  title: "Create Staff",
  description: "Create Staff",
  keywords: ["staff"],
};

export default async function Page({
  params,
}: {
  params: {
    branchId: string;
  };
}) {
  const session = await auth(); // Retrieve the session
  const branch = await fetchBranch(params.branchId);
  const roles = await fetchRoles()


  const createStaff = async (formData: FormData) => {
    "use server";
    
    // console.log("Data collected:", formData)
    // Check for session and append necessary data to formData
    if (session && branch) {
      formData.append("branchId", params.branchId);
      formData.append("vendorId", branch.vendorId);
      formData.append("createdBy", session.user?.id || ""); // Optionally, add user ID to track creator
    }


      const payload: NewStaff = {
        email: formData.get('email') as string,
        firstName: formData.get('firstName') as string,
        lastName: formData.get('lastName') as string,
        identifier: formData.get('identifier') as string,
        idpass: formData.get('idpass') as string,
        phone: (formData.get('phone') as string).slice(1, (formData.get('phone') as string).length),
        vendorId: formData.get('vendorId') as string,
        details: formData.get('details') as string,
        dob: formData.get('dob') as string,
        gender: formData.get('gender') as string,
        password: formData.get('password') as string,
        role: formData.get('role') as string,
      }
  
      try {
        await registerNewStaff(params.branchId, payload)
      } catch (error) {
        console.log("Error occured",error)
      }
  };

  return (
    <div className="p-4">
      <div className="mb-4 flex items-center justify-between">
        <h1 className="text-2xl text-primary">
          {branch ? `Create Staff for ${branch.name}` : "Create Staff"}
        </h1>

        <Link
          href={`/branches/${params.branchId}/staff`}
          className="flex items-center space-x-2 text-default-500"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
            className="h-6 w-6"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M6.75 15.75 3 12m0 0 3.75-3.75M3 12h18"
            />
          </svg>
          <span>Back to Staff</span>
        </Link>
      </div>
      {branch && (
        <BranchStaffCreateForm session={session} storeStaff={createStaff} branch={branch} roles={roles}/>
      )}
    </div>
  );
}
