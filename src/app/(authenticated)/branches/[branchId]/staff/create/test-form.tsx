import React from 'react'
import { SubmitH<PERSON><PERSON>, useForm } from 'react-hook-form'

type Inputs = {
  name: string
  email: string
}

export default function TestForm() {
  const { register, handleSubmit, formState: { errors }, watch } = useForm<Inputs>()
  const onSubmit: SubmitHandler<Inputs> = (data: Inputs) => {
    console.log("Inputs", data)
  }

  // For real-time form data monitoring
  const user = watch()

  return (
    <div>
      <form onSubmit={handleSubmit(onSubmit)}>
        <label>
          Name
          <input {...register('name', { required: true })} type='text'/>
          {errors.name && <span>Name is required</span>}
        </label>

        <label>
          Email 
          <input {...register("email", { required: true })} type='email'/>
          {errors.email && <span>Email is required</span>}
        </label>

        <input type="submit" value="Log" />
      </form>
    </div>
  )
}
