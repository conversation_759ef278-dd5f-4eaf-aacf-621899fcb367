import { deleteStaff, fetchStaff } from "@/actions/users";
import StaffTable from "@/components/tables/staff-table";
import { revalidatePath } from "next/cache";
import { useEffect, useState } from "react";

export default async function page({
  searchParams,
  params
}: {
  searchParams?: Record<string, string>;
  params: { branchId: string }
}) {

  const staff = await fetchStaff({ branchId: params.branchId, searchParams: searchParams })

  const deleteUser = async(data:FormData) =>{
    "use server"
    
    try {
      await deleteStaff(data)
      revalidatePath(`/branches/${params.branchId}/staff`)
      return true
    } catch (error) {
      console.log("ERROR DELETING USER:(branch/staff/details)", error)
      return false
    }
  }
  return (
    <div className="page-content p-5">
      <StaffTable
        data={staff?.data as User[]}
        meta={staff?.meta as PaginationMeta}
        params={{branchId: params.branchId as string}}
        deleteStaff={deleteUser}
      />
    </div>
  );
}