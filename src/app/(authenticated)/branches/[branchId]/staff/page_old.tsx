import PaginatedTable from "@/components/table";
import { api, imagePath } from "@/lib/api";
import Image from "next/image";
import Link from "next/link";

export const generateMetadata = async ({
  params,
}: {
  params: { branchId: string };
}) => {
  const branch = await api.get<Branch>(`branches/${params.branchId}`);

  return {
    title: `${branch?.name} Staff`,
    description: "Staff",
  };
};

export default async function page({
  params,
}: {
  params: { branchId: string };
}) {
  const path = `branches/${params.branchId}/staff`;
  const staff = await api.get<PaginatedData<User>>(path);

  return (
    <PaginatedTable<User>
      records={staff!}
      columns={[
        {
          id: "name",
          title: "Name",
          class:
            "flex items-center px-6 py-4 text-default-900 whitespace-nowrap dark:text-white",
          render: (user: User) => (
            <>
              <Image
                width={50}
                height={50}
                className="size-10 rounded-full"
                src={imagePath(user.avatar?.url)}
                alt={user.name}
              />
              <Link
                className="pl-3"
                href={`/branches/${params.branchId}/staff/${user.id}`}
              >
                <div className="text-base font-semibold">{user.name}</div>

                <div className="font-normal text-default-500">{user.email}</div>
              </Link>
            </>
          ),
        },
        {
          id: "phone",
          title: "Phone number",
        },
        {
          id: "identifier",
          title: "Staff ID",
        },

        {
          id: "actions",
          title: "Actions",
          render: (user: User) => (
            <Link
              href={`/branches/${params.branchId}/staff/${user.id}/edit`}
              className="font-medium text-default-600 hover:underline dark:text-blue-500"
            >
              Edit
            </Link>
          ),
        },
      ]}
      path="staff"
      title="Staff"
      tools={
        <div className="flex items-center justify-between bg-white px-4 pb-4 pt-6 dark:bg-default-900">
          <div className="flex space-x-2">
            <button
              id="dropdownActionButton"
              data-dropdown-toggle="dropdownAction"
              className="inline-flex items-center rounded-lg border border-default-300 bg-white px-3 py-1.5 text-sm font-medium text-default-500 hover:bg-default-100 focus:outline-none focus:ring-4 focus:ring-default-200 dark:border-default-600 dark:bg-default-800 dark:text-default-400 dark:hover:border-default-600 dark:hover:bg-default-700 dark:focus:ring-default-700"
              type="button"
            >
              <span className="sr-only">Action button</span>
              Action
              <svg
                className="ml-2 h-3 w-3"
                aria-hidden="true"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M19 9l-7 7-7-7"
                ></path>
              </svg>
            </button>
            <div
              id="dropdownAction"
              className="z-10 hidden w-44 divide-y divide-default-100 rounded-lg bg-white shadow dark:divide-default-600 dark:bg-default-700"
            >
              <ul
                className="py-1 text-sm text-default-700 dark:text-default-200"
                aria-labelledby="dropdownActionButton"
              >
                <li>
                  <a
                    href="#"
                    className="block px-4 py-2 hover:bg-default-100 dark:hover:bg-default-600 dark:hover:text-white"
                  >
                    Reward
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="block px-4 py-2 hover:bg-default-100 dark:hover:bg-default-600 dark:hover:text-white"
                  >
                    Promote
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="block px-4 py-2 hover:bg-default-100 dark:hover:bg-default-600 dark:hover:text-white"
                  >
                    Activate account
                  </a>
                </li>
              </ul>
              <div className="py-1">
                <a
                  href="#"
                  className="block px-4 py-2 text-sm text-default-700 hover:bg-default-100 dark:text-default-200 dark:hover:bg-default-600 dark:hover:text-white"
                >
                  Delete Vendor
                </a>
              </div>
            </div>
          </div>
          <div className="flex space-x-2">
            <label htmlFor="table-search" className="sr-only">
              Search
            </label>
            <div className="relative">
              <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                <svg
                  className="h-5 w-5 text-default-500 dark:text-default-400"
                  aria-hidden="true"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fillRule="evenodd"
                    d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                    clipRule="evenodd"
                  ></path>
                </svg>
              </div>
              <input
                type="text"
                id="table-search-branches"
                className="block w-80  rounded-lg border border-default-300 bg-default-50 p-3 pl-10 text-sm text-default-900 focus:border-blue-500 focus:ring-blue-500 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
                placeholder="Search for branches"
              />
            </div>

            <Link
              href={`/branches/${params.branchId}/staff/create`}
              className="rounded-lg bg-primary px-6 py-2 text-white dark:bg-default-500"
            >
              Add staff
            </Link>
          </div>
        </div>
      }
    />
  );
}
