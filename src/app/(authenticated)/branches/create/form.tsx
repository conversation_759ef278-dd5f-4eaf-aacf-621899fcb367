"use client";

import { useState, ChangeEvent } from "react";
import { Submit<PERSON><PERSON><PERSON>, useForm, Controller } from "react-hook-form";
import { toast } from "react-hot-toast";
import Image from "next/image";
import { usePara<PERSON>, useRouter } from "next/navigation";
import GooglePlacesAutocomplete, {
  geocodeByPlaceId,
} from "react-google-places-autocomplete";
import PhoneInput from "react-phone-number-input";

export default function BranchCreateForm({
  storeBranch,
}: {
  storeBranch: (data: FormData) => Promise<Branch | undefined>;
}) {
  const router = useRouter();
  const [preview, setPreview] = useState<string>();
  const [results, setResults] = useState<any>();

  const { vendorId } = useParams();

  const {
    setValue,
    watch,
    handleSubmit,
    register,
    reset,
    control,
    formState: { errors, isSubmitting },
  } = useForm<Partial<Branch & { upload: File }>>();

  const branch = watch();

  const { ref: registerRef, ...rest } = register("image");

  const handleUploadedFile = (event: ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;

    if (files) {
      const file = files[0];
      setValue("upload", file);
      const urlImage = URL.createObjectURL(file);

      setPreview(urlImage);
    }
  };

  const setPlace = ({ value }: any) => {
    geocodeByPlaceId(value.place_id)
      .then((places) => {
        const place = places[0];
        setValue("location", {
          // name: place.label,
          address: place.formatted_address,
          coordinates: {
            lat: place.geometry?.location?.lat(),
            lng: place.geometry?.location?.lng(),
          },
          regions: place.address_components?.reduce((acc: any, curr: any) => {
            acc[curr.types[0]] = curr.long_name;
            return acc;
          }, {}),
          place_id: value.place_id,
        });
      })
      .catch((error) => console.error(error));
  };

  const createBranch: SubmitHandler<Partial<Branch & { upload: File }>> = (
    branch: Partial<Branch & { upload: File }>,
  ) => {
    const data = new FormData();

    data.append("name", branch.name!);
    data.append("details", branch.details!);
    data.append("image", branch.upload!);
    data.append("vendorId", vendorId as string);

    data.append("location[name]", branch.location?.name as string);
    data.append("location[address]", branch.location?.address as string);
    data.append("location[place_id]", branch.location?.place_id as string);
    data.append(
      "location[coordinates][lat]",
      branch.location?.coordinates?.lat as string,
    );
    data.append(
      "location[coordinates][lng]",
      branch.location?.coordinates?.lng as string,
    );

    const regions = branch.location?.regions || {};
    Object.keys(regions).forEach((key: string) => {
      data.append(`location[regions][${key}]`, regions[key] as string);
    });

    toast
      .promise(
        storeBranch(data),
        {
          loading: "Creating branch...",
          success: "Branch has been saved 👌",
          error: "Could not save branch 🤯",
        },
        {
          position: "bottom-center",
        },
      )
      .then((res: Branch | undefined) => {
        if (res) {
          reset();
          router.push(`/branches/${res.id}`);
        }
      });
  };

  return (
    <form onSubmit={handleSubmit(createBranch)}>
      <div className="space-y-4">
        <div>
          <label
            htmlFor="name"
            className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
          >
            Name
          </label>
          <input
            type="text"
            {...register("name")}
            id="name"
            className="block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
            placeholder="Type branch name"
            required
          />
        </div>

        <div>
          <label
            htmlFor="image"
            className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
          >
            Image or icon
          </label>
          <label className="flex h-40 w-full items-center justify-center rounded-lg border border-dashed border-default-300 bg-default-50 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600">
            <input
              type="file"
              name="image"
              id="image"
              className="hidden"
              onChange={handleUploadedFile}
            />
            {preview ? (
              <Image
                src={preview}
                alt="preview"
                width={100}
                height={100}
                className="w-1/2"
              />
            ) : (
              <p>Click to select file</p>
            )}
          </label>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="col-span-2 lg:col-span-1">
            <label
              htmlFor="email"
              className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
            >
              Email
            </label>
            <input
              type="email"
              id="email"
              {...register("email")}
              className="block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
              placeholder="Type branch email"
            />
          </div>

          <div className="col-span-2 lg:col-span-1">
            <label
              htmlFor="phone"
              className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
            >
              Phone
            </label>
            <div className="form-input relative w-full max-w-full rounded-lg border border-default-200 bg-white px-4 py-3 dark:bg-default-50 [&>input]:focus:ring-0">
              <Controller
                name="phone"
                control={control}
                render={({ field: { onChange, value } }) => (
                  <PhoneInput
                    placeholder="Enter phone number"
                    value={value}
                    onChange={onChange}
                    defaultCountry="KE"
                  />
                )}
              />
            </div>
          </div>
        </div>

        <div>
          <label
            htmlFor="description"
            className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
          >
            Description
          </label>
          <textarea
            id="description"
            rows={4}
            {...register("details")}
            className=".5 block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
            placeholder="Enter branch description here"
          />
        </div>

        <div>
          <label
            htmlFor="name"
            className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
          >
            Location
          </label>
          <GooglePlacesAutocomplete
            apiKey="AIzaSyB5XjUloDmNIRd88muXWdfztbX0sBdI3qQ"
            selectProps={{
              value: results,
              onChange: setPlace,
              classNames: {
                control: () =>
                  "!rounded-lg border border-default-300 focus:ring-default-600 focus:border-default-600 dark:bg-default-700 dark:border-default-600 dark:placeholder-default-400 dark:text-white dark:focus:ring-default-600 dark:focus:border-default-500 py-[1px]",
              },
              components: {
                IndicatorSeparator: () => null,
              },
              placeholder: "Start typing location",
            }}
          />
        </div>

        <div className="flex w-full justify-end">
          <button
            type="submit"
            disabled={isSubmitting}
            className="w-60 justify-center rounded-lg bg-primary px-5 py-3 text-center text-sm font-medium text-white hover:bg-default-800 focus:outline-none focus:ring-4 focus:ring-default-300 dark:bg-primary dark:hover:bg-primary dark:focus:ring-default-800"
          >
            Save branch details
          </button>
        </div>
      </div>
    </form>
  );
}
