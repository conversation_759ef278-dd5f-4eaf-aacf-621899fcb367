import PaginatedTable from "@/components/table";
import { api } from "@/lib/api";
import Link from "next/link";
import { cache } from "react";

export const dynamic = "force-dynamic";
export const fetchCache = "force-no-store";

const fetchVendor = cache((id: string) => api.get<Vendor>(`vendors/${id}`));

export const generateMetadata = async ({
  params,
}: {
  params: {
    vendorId: string;
  };
}): Promise<Record<string, any>> => {
  const vendor = await fetchVendor(params.vendorId);

  return {
    title: `Branches In ${vendor?.name}`,
    description: "Vendor",
    keywords: ["vendor"],
  };
};

export default async function page({
  params,
}: {
  params: {
    vendorId: string;
  };
}) {
  const branches = await api.get<PaginatedData<Branch>>(
    `vendors/${params.vendorId}/branches`,
  );

  return (
    <div>
      {branches && (
        <PaginatedTable<Branch>
          records={branches}
          columns={[
            {
              id: "name",
              title: "Name",
              render: (record) => (
                <Link
                  href={`/vendors/${params.vendorId}/branches/${record.id}`}
                >
                  {record.name}
                </Link>
              ),
            },
            { id: "address", title: "Address" },
            { id: "phone", title: "Phone" },
            { id: "email", title: "Email" },
            {
              id: "actions",
              title: "Actions",
              render: (record) => (
                <Link href={`/branches/${record.id}/edit`}>Edit</Link>
              ),
            },
          ]}
          tools={<></>}
          title={"Branches"}
          path={`/vendors/${params.vendorId}/branches`}
        />
      )}
    </div>
  );
}
