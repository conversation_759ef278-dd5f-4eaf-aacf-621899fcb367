import { api, imagePath } from "@/lib/api";
import { revalidatePath } from "next/cache";
import AdminBranchCampaignsEditForm from "../edit";
import { auth } from "@/auth";
import Link from "next/link";
import Image from "next/image";

const getCampaign = async (campaignId: string) => {
  const campaign = await api.get<Campaign>(`campaigns/${campaignId}`);

  return campaign;
};

export const generateMetadata = async ({
  params,
}: {
  params: {
    campaignId: string;
  };
}) => {
  const campaign = await getCampaign(params.campaignId);

  return {
    title: campaign?.name,
  };
};

export default async function CampainDetailsPage({
  params,
}: {
  params: {
    campaignId: string;
  };
}) {
  const session = await auth();
  const campaign = await getCampaign(params.campaignId);

  const storeCampaign = async (data: FormData) => {
    "use server";

    data.append("vendorId", session?.vendor?.id as string);
    data.append("branchId", session?.branch?.id as string);

    await api.post("campaigns", data);

    revalidatePath("/campaigns");
  };

  const fetchTasks = async (s?: string) => {
    "use server";

    const res = await api.get<PaginatedData<Task>>("tasks", {
      ...{ per: 20, ...(s && { s }) },
    });

    return res?.data ?? [];
  };

  const fetchBranches = async (s?: string) => {
    "use server";

    const res = await api.get<PaginatedData<Branch>>("branches", {
      ...{ per: 20, ...(s && { s }) },
    });

    return res?.data ?? [];
  };

  const fetchVendors = async (s?: string) => {
    "use server";

    const res = await api.get<PaginatedData<Vendor>>("vendors", {
      ...{ per: 20, ...(s && { s }) },
    });

    return res?.data ?? [];
  };

  const fetchSpecialities = async (s?: string) => {
    "use server";

    const res = await api.get<PaginatedData<Speciality>>("specialities", {
      ...{ per: 20, ...(s && { s }) },
    });

    return res?.data ?? [];
  };

  const fetchProductTypes = async (s?: string) => {
    "use server";

    const res = await api.get<PaginatedData<ProductType>>("product-types", {
      ...{ per: 20, ...(s && { s }) },
    });

    return res?.data ?? [];
  };

  const fetchProductCategories = async (s?: string) => {
    "use server";

    const res = await api.get<PaginatedData<ProductCategory>>(
      "product-categories",
      {
        ...{ per: 20, ...(s && { s }) },
      },
    );

    return res?.data ?? [];
  };

  const fetchLots = async (s?: string) => {
    "use server";

    const res = await api.get<PaginatedData<Lot>>("lots", {
      ...{ per: 20, ...(s && { s }) },
    });

    return res?.data ?? [];
  };

  const fetchProducts = async (s?: string) => {
    "use server";

    const res = await api.get<PaginatedData<Product>>("products", {
      ...{ per: 20, ...(s && { s }) },
    });

    return res?.data ?? [];
  };

  const updateCampaign = async (data: FormData) => {
    "use server";

    await api.put(`campaigns/${params.campaignId}`, data);

    revalidatePath(`/campaigns/${params.campaignId}`);
  };

  return (
    <div className="py-4">
      {campaign && (
        <div>
          <div className="mb-4 flex items-center justify-between px-4">
            <p>{campaign.details}</p>

            <div className="flex items-center space-x-2">
              <Link
                href={campaign.link || `/campaigns/${campaign.id}`}
                className="rounded-lg bg-primary px-6 py-2 text-white"
              >
                {campaign.link || "View details"}
              </Link>

              <AdminBranchCampaignsEditForm
                defaultValues={campaign}
                updateCampaign={updateCampaign}
                fetchTasks={fetchTasks}
                fetchBranches={fetchBranches}
                fetchVendors={fetchVendors}
                fetchProducts={fetchProducts}
                fetchSpecialities={fetchSpecialities}
                fetchProductTypes={fetchProductTypes}
                fetchProductCategories={fetchProductCategories}
                storeCampaign={storeCampaign}
              />
            </div>
          </div>

          <div className="w-full">
            <Image
              src={imagePath(campaign.image?.url)}
              alt={campaign.name}
              className="w-full"
              // width={400}
              // height={200}
            />
          </div>
        </div>
      )}
    </div>
  );
}
