"use client";

import { useState, ChangeEvent } from "react";
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import { toast } from "react-hot-toast";
import Image from "next/image";
import { useClickOutside } from "@/hooks/useClickOutside";
import Datepicker from "react-tailwindcss-datepicker";
import Select from "react-select";
import AsyncSelect from "react-select/async";

export default function AdminBranchCampaignsCreateForm({
  branch,
  fetchTasks,
  fetchBranches,
  fetchProducts,
  fetchVendors,
  fetchSpecialities,
  fetchProductTypes,
  fetchProductCategories,
  storeCampaign,
}: {
  branch?: Branch;
  fetchTasks: (s?: string) => Promise<Task[]>;
  fetchBranches: (s?: string) => Promise<Branch[]>;
  fetchVendors: (s?: string) => Promise<Vendor[]>;
  fetchProducts: (s?: string) => Promise<Product[]>;
  fetchSpecialities: (s?: string) => Promise<Speciality[]>;
  fetchProductTypes: (s?: string) => Promise<ProductType[]>;
  fetchProductCategories: (s?: string) => Promise<ProductCategory[]>;
  storeCampaign: (data: FormData) => Promise<void>;
}) {
  const [creating, setCreating] = useState(false);
  const [preview, setPreview] = useState<string>();
  const ref = useClickOutside(() => setCreating(false));

  const {
    setValue,
    watch,
    handleSubmit,
    register,
    reset,
    control,
    formState: { errors, isSubmitting },
  } = useForm<
    Campaign & { upload: File; start: Date; end: Date; linkTo: string }
  >();

  const campaign = watch();

  const { ref: registerRef, ...rest } = register("image");

  const linkToOptions = [
    {
      value: "service",
      label: "Service",
    },
    {
      value: "task",
      label: "Task",
    },
    {
      value: "section",
      label: "Section",
    },
    {
      value: "lot",
      label: "Lot",
    },
    {
      value: "slot",
      label: "Slot",
    },
    {
      value: "product",
      label: "Product",
    },
  ];

  if (branch) {
    linkToOptions.push({
      value: "vendor",
      label: "Vendor",
    });

    linkToOptions.push({
      value: "branch",
      label: "Branch",
    });
  }

  const handleUploadedFile = (event: ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;

    if (files) {
      const file = files[0];
      setValue("upload", file);
      const urlImage = URL.createObjectURL(file);

      setPreview(urlImage);
    }
  };
  
  const loadTasks = async (s: string) => {
    return await fetchTasks(s);
  };

  const loadVendors = async (s: string) => {
    return await fetchVendors(s);
  };

  const loadSpecialities = async (s: string) => {
    return await fetchSpecialities(s);
  };

  const loadProductTypes = async (s: string) => {
    return await fetchProductTypes(s);
  };

  const loadProductCategories = async (s: string) => {
    return await fetchProductCategories(s);
  };

  const loadBranches = async (s: string, vendorId: string) => {
    return await fetchBranches(s);
  };

  const loadProducts = async (s: string) => {
    return await fetchProducts(s);
  };

  const createCampaign: SubmitHandler<
    Campaign & { upload: File; start: Date; end: Date; linkTo: string }
  > = (
    campaign: Campaign & {
      upload: File;
      start: Date;
      end: Date;
      linkTo: string;
    },
  ) => {
    const data = new FormData();

    data.append("name", campaign.name!);
    data.append("details", campaign.details!);
    data.append("link", campaign.link || "");
    data.append("startDate", campaign.start?.toISOString()!);
    data.append("endDate", campaign.end?.toISOString()!);

    if(campaign.upload) {
      data.append("image", campaign.upload);
    }

    toast.promise(
      storeCampaign(data),
      {
        loading: "Creating campaign...",
        success: "Campaign has been saved 👌",
        error: "Could not save campaign 🤯",
      },
      {
        position: "bottom-center",
      },
    );

    reset();

    setCreating(false);
  };

  return (
    <>
      <button
        onClick={() => setCreating(!creating)}
        id="createCampaignButton"
        className="flex items-center justify-center space-x-2 rounded-lg bg-primary px-6 py-2 text-white hover:bg-default-800 dark:bg-primary"
        type="button"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="currentColor"
          className="bi bi-plus-lg h-5 w-5"
          viewBox="0 0 16 16"
        >
          <path
            fillRule="evenodd"
            d="M8 2a.5.5 0 0 1 .5.5v5h5a.5.5 0 0 1 0 1h-5v5a.5.5 0 0 1-1 0v-5h-5a.5.5 0 0 1 0-1h5v-5A.5.5 0 0 1 8 2Z"
          />
        </svg>

        <span className="ml-2">Create Campaign</span>
      </button>

      {creating && (
        <div
          ref={ref}
          id="drawer-create-campaign-default"
          className="fixed right-0 top-0 z-40 h-screen w-full max-w-xs overflow-y-auto bg-white p-4 transition-transform dark:bg-default-800"
          tabIndex={-1}
          aria-labelledby="drawer-label"
          aria-hidden="true"
        >
          <h5
            id="drawer-label"
            className="mb-6 inline-flex items-center text-sm font-semibold uppercase text-default-500 dark:text-default-400"
          >
            New Campaign
          </h5>

          <button
            onClick={() => setCreating(!creating)}
            type="button"
            data-drawer-dismiss="drawer-create-campaign-default"
            aria-controls="drawer-create-campaign-default"
            className="absolute right-2.5 top-2.5 inline-flex items-center rounded-lg bg-transparent p-1.5 text-sm text-default-400 hover:bg-default-200 hover:text-default-900 dark:hover:bg-default-600 dark:hover:text-white"
          >
            <svg
              aria-hidden="true"
              className="h-5 w-5"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fillRule="evenodd"
                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                clipRule="evenodd"
              ></path>
            </svg>
            <span className="sr-only">Close menu</span>
          </button>

          <form onSubmit={handleSubmit(createCampaign)}>
            <div className="space-y-4">
              <div className="space-y-2">
                <label
                  htmlFor="name"
                  className="block text-sm font-medium text-default-900 dark:text-white"
                >
                  Name
                </label>
                <input
                  type="text"
                  {...register("name")}
                  id="name"
                  className="block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
                  placeholder="Type campaign name"
                  required
                />
              </div>

              <div className="space-y-2">
                <label
                  htmlFor="image"
                  className="block text-sm font-medium text-default-900 dark:text-white"
                >
                  Campaign image
                </label>
                <label className="flex h-40 w-full items-center justify-center rounded-lg border border-dashed border-default-300 bg-default-50 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600">
                  <input
                    type="file"
                    name="image"
                    id="image"
                    className="hidden"
                    onChange={handleUploadedFile}
                  />

                  {preview ? (
                    <Image
                      src={preview}
                      alt="preview"
                      width={100}
                      height={100}
                      className="w-1/2"
                    />
                  ) : (
                    <p>Click to select file</p>
                  )}
                </label>
              </div>

              <div className="space-y-2">
                <label
                  htmlFor="description"
                  className="block text-sm font-medium text-default-900 dark:text-white"
                >
                  Description
                </label>
                <textarea
                  id="description"
                  rows={4}
                  {...register("details")}
                  className=".5 block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
                  placeholder="Enter campaign description here"
                />
              </div>

              <div className="space-y-2">
                <label
                  htmlFor="dates"
                  className="block text-sm font-medium text-default-900 dark:text-white"
                >
                  Start and end date
                </label>
                <Datepicker
                  value={{
                    startDate: campaign.start || new Date(),
                    endDate: campaign.end || new Date(),
                  }}
                  startFrom={new Date()}
                  onChange={(v) => {
                    if (v) {
                      setValue("start", new Date(v.startDate!));
                      setValue("end", new Date(v.endDate!));
                    }
                  }}
                  showShortcuts={true}
                  containerClassName="border border-default-300 rounded-lg w-full flex"
                />
              </div>

              <div className="space-y-2">
                <label
                  htmlFor="linkTo"
                  className="block text-sm font-medium text-default-900 dark:text-white"
                >
                  Link to
                </label>

                <Controller
                  name="linkTo"
                  control={control}
                  render={({ field: { onChange, value } }) => (
                    <Select
                      isClearable
                      isSearchable
                      classNames={{
                        control: () =>
                          "form-input !rounded-lg !border-default-200 py-1 dark:bg-default-50 w-full",
                        input: () => "border-none",
                        container: () => "!border-none border-initial !border",
                      }}
                      options={linkToOptions}
                      placeholder="Search in options"
                      onChange={(r) => setValue("linkTo", r?.value ?? "")}
                      components={{
                        IndicatorSeparator: () => null,
                      }}
                      className="w-full"
                    />
                  )}
                />
              </div>

              <div className="space-y-2">
                <label className="block">Select {campaign.linkTo}</label>

                <Controller
                  name="link"
                  control={control}
                  render={({ field: { onChange, value } }) => (
                    <>
                      {campaign.linkTo === "task" && (
                        <AsyncSelect
                          isClearable
                          isSearchable
                          cacheOptions
                          loadOptions={loadTasks}
                          getOptionLabel={(task: Task) => task.name}
                          getOptionValue={(task: Task) => task.id}
                          placeholder="Search in tasks"
                          onChange={(r) => onChange(`/tasks/${r?.id ?? ""}`)}
                          components={{
                            IndicatorSeparator: () => null,
                          }}
                          className="w-full"
                          classNames={{
                            control: () =>
                              "form-input !rounded-lg !border-default-200 py-1 dark:bg-default-50 w-full",
                            input: () => "border-none",
                            container: () =>
                              "!border-none border-initial !border",
                          }}
                        />
                      )}

                      {campaign.linkTo === "product" && (
                        <AsyncSelect
                          isClearable
                          isSearchable
                          cacheOptions
                          loadOptions={loadProducts}
                          getOptionLabel={(product: Product) => product.name}
                          getOptionValue={(product: Product) => product.id}
                          placeholder="Search in products"
                          onChange={(r) => onChange(`/products/${r?.id ?? ""}`)}
                          components={{
                            IndicatorSeparator: () => null,
                          }}
                          className="w-full"
                          classNames={{
                            control: () =>
                              "form-input !rounded-lg !border-default-200 py-1 dark:bg-default-50 w-full",
                            input: () => "border-none",
                            container: () =>
                              "!border-none border-initial !border",
                          }}
                        />
                      )}

                      {campaign.linkTo === "product-type" && (
                        <AsyncSelect
                          isClearable
                          isSearchable
                          cacheOptions
                          loadOptions={loadProductTypes}
                          getOptionLabel={(productType: ProductType) =>
                            productType.name
                          }
                          getOptionValue={(productType: ProductType) =>
                            productType.id
                          }
                          placeholder="Search in product types"
                          onChange={(r) =>
                            onChange(`/product-types/${r?.id ?? ""}`)
                          }
                          components={{
                            IndicatorSeparator: () => null,
                          }}
                          className="w-full"
                          classNames={{
                            control: () =>
                              "form-input !rounded-lg !border-default-200 py-1 dark:bg-default-50 w-full",
                            input: () => "border-none",
                            container: () =>
                              "!border-none border-initial !border",
                          }}
                        />
                      )}

                      {campaign.linkTo === "product-category" && (
                        <AsyncSelect
                          isClearable
                          isSearchable
                          cacheOptions
                          loadOptions={loadProductCategories}
                          getOptionLabel={(productCategory: ProductCategory) =>
                            productCategory.name
                          }
                          getOptionValue={(productCategory: ProductCategory) =>
                            productCategory.id
                          }
                          placeholder="Search in product categories"
                          onChange={(r) =>
                            onChange(`/product-categories/${r?.id ?? ""}`)
                          }
                          components={{
                            IndicatorSeparator: () => null,
                          }}
                          className="w-full"
                          classNames={{
                            control: () =>
                              "form-input !rounded-lg !border-default-200 py-1 dark:bg-default-50 w-full",
                            input: () => "border-none",
                            container: () =>
                              "!border-none border-initial !border",
                          }}
                        />
                      )}

                      {campaign.linkTo === "vendor" && (
                        <AsyncSelect
                          isClearable
                          isSearchable
                          cacheOptions
                          loadOptions={loadVendors}
                          getOptionLabel={(vendor: Vendor) => vendor.name}
                          getOptionValue={(vendor: Vendor) => vendor.id}
                          placeholder="Search in vendors"
                          onChange={(r) => onChange(`/vendors/${r?.id ?? ""}`)}
                          components={{
                            IndicatorSeparator: () => null,
                          }}
                          className="w-full"
                          classNames={{
                            control: () =>
                              "form-input !rounded-lg !border-default-200 py-1 dark:bg-default-50 w-full",
                            input: () => "border-none",
                            container: () =>
                              "!border-none border-initial !border",
                          }}
                        />
                      )}

                      {campaign.linkTo === "speciality" && (
                        <AsyncSelect
                          isClearable
                          isSearchable
                          cacheOptions
                          loadOptions={loadSpecialities}
                          getOptionLabel={(speciality: Speciality) =>
                            speciality.name
                          }
                          getOptionValue={(speciality: Speciality) =>
                            speciality.id
                          }
                          placeholder="Search in specialities"
                          onChange={(r) =>
                            onChange(`/specialities/${r?.id ?? ""}`)
                          }
                          components={{
                            IndicatorSeparator: () => null,
                          }}
                          className="w-full"
                          classNames={{
                            control: () =>
                              "form-input !rounded-lg !border-default-200 py-1 dark:bg-default-50 w-full",
                            input: () => "border-none",
                            container: () =>
                              "!border-none border-initial !border",
                          }}
                        />
                      )}
                    </>
                  )}
                />
              </div>

              <div className="bottom-0 left-0 flex w-full justify-center space-x-4 pb-4 md:absolute md:px-4">
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full justify-center rounded-lg bg-primary px-5 py-3 text-center text-sm font-medium text-white hover:bg-default-800 focus:outline-none focus:ring-4 focus:ring-default-300 dark:bg-primary dark:hover:bg-primary dark:focus:ring-default-800"
                >
                  Save campaign details
                </button>
              </div>
            </div>
          </form>
        </div>
      )}
    </>
  );
}
