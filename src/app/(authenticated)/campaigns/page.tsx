import PaginatedTable from "@/components/table";
import { api } from "@/lib/api";
import AdminBranchCampaignsCreateForm from "./create";
import { revalidatePath } from "next/cache";
import Link from "next/link";
import { format } from "date-fns";
import AdminBranchCampaignsEditForm from "./edit";
import { auth } from "@/auth";

export const metadata = {
  title: "Campaigns",
};

export default async function CampaignsIndexPage({
  searchParams,
}: {
  searchParams: Record<string, string>;
}) {
  const session = await auth();


  const campaigns = await api.get<PaginatedData<Campaign>>(
    `branches/${session?.branch?.id}/campaigns`,
    {
      page: 1,
      ...searchParams,
    },
  );


  const storeCampaign = async (data: FormData) => {
    "use server";

    data.append("vendorId", session?.vendor?.id as string);
    data.append("branchId", session?.branch?.id as string);

    await api.post("campaigns", data);

    revalidatePath("/campaigns");
  };

  const updateCampaign = async (data: FormData) => {
    "use server";

    await api.put(`campaigns/${data.get("id")}`, data);

    revalidatePath("/campaigns");
  };

  const deleteCampaign = async (data: FormData) => {
    "use server";

    await api.destroy(data.get("id") as string, "campaigns");

    revalidatePath("/campaigns");
  };

  const fetchTasks = async (s?: string) => {
    "use server";

    const res = await api.get<PaginatedData<Task>>("tasks", {
      ...{ per: 20, ...(s && { s }) },
    });

    return res?.data ?? [];
  };

  const fetchBranches = async (s?: string) => {
    "use server";

    const res = await api.get<PaginatedData<Branch>>("branches", {
      ...{ per: 20, ...(s && { s }) },
    });

    return res?.data ?? [];
  };

  const fetchVendors = async (s?: string) => {
    "use server";

    const res = await api.get<PaginatedData<Vendor>>("vendors", {
      ...{ per: 20, ...(s && { s }) },
    });

    return res?.data ?? [];
  };

  const fetchSpecialities = async (s?: string) => {
    "use server";

    const res = await api.get<PaginatedData<Speciality>>("specialities", {
      ...{ per: 20, ...(s && { s }) },
    });

    return res?.data ?? [];
  };

  const fetchProductTypes = async (s?: string) => {
    "use server";

    const res = await api.get<PaginatedData<ProductType>>("product-types", {
      ...{ per: 20, ...(s && { s }) },
    });

    return res?.data ?? [];
  };

  const fetchProductCategories = async (s?: string) => {
    "use server";

    const res = await api.get<PaginatedData<ProductCategory>>(
      "product-categories",
      {
        ...{ per: 20, ...(s && { s }) },
      },
    );

    return res?.data ?? [];
  };

  const fetchLots = async (s?: string) => {
    "use server";

    const res = await api.get<PaginatedData<Lot>>("lots", {
      ...{ per: 20, ...(s && { s }) },
    });

    return res?.data ?? [];
  };

  const fetchProducts = async (s?: string) => {
    "use server";

    const res = await api.get<PaginatedData<Product>>("products", {
      ...{ per: 20, ...(s && { s }) },
    });

    return res?.data ?? [];
  };

  return (
    <div className="py-4">
      {campaigns && (
        <PaginatedTable<Campaign>
          records={campaigns}
          columns={[
            {
              id: "name",
              title: "Name",
              render: (campaign: Campaign) => (
                <Link
                  href={`/campaigns/${campaign.id}`}
                  className="font-semibold text-default-600"
                >
                  {campaign.name}
                </Link>
              ),
            },
            {
              id: "startDate",
              title: "Start At",
              render: (campaign: Campaign) => (
                <>{format(new Date(campaign.startDate), "EEE MMM dd yyyy")}</>
              ),
            },
            {
              id: "endDate",
              title: "End At",
              render: (campaign: Campaign) => (
                <>{format(new Date(campaign.endDate), "EEE MMM dd yyyy")}</>
              ),
            },
            {
              id: "link",
              title: "Link",
              render: (campaign: Campaign) => (
                <Link
                  href={campaign.link || `/campaigns/${campaign.id}`}
                  className="font-semibold text-default-600"
                >
                  {campaign.link}
                </Link>
              ),
            },
            {
              id: "actions",
              title: "Actions",
              render: (campaign: Campaign) => (
                <div className="flex items-center space-x-2">
                  <Link
                    href={`/campaigns/${campaign.id}`}
                    className="text-default-600"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      strokeWidth={1.5}
                      stroke="currentColor"
                      className="h-6 w-6"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"
                      />
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"
                      />
                    </svg>
                  </Link>
                  <AdminBranchCampaignsEditForm
                    defaultValues={campaign}
                    updateCampaign={updateCampaign}
                    fetchTasks={fetchTasks}
                    fetchBranches={fetchBranches}
                    fetchVendors={fetchVendors}
                    fetchProducts={fetchProducts}
                    fetchSpecialities={fetchSpecialities}
                    fetchProductTypes={fetchProductTypes}
                    fetchProductCategories={fetchProductCategories}
                    storeCampaign={storeCampaign}
                  />

                  <form action={deleteCampaign} className="btn btn-danger">
                    <button
                      name="id"
                      value={campaign.id}
                      type="submit"
                      className="text-red-500"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        strokeWidth={1.5}
                        stroke="currentColor"
                        className="h-6 w-6"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"
                        />
                      </svg>
                    </button>
                  </form>
                </div>
              ),
            },
          ]}
          tools={
            <div className="mb-4 flex items-center justify-between px-4">
              <p></p>

              <AdminBranchCampaignsCreateForm
                storeCampaign={storeCampaign}
                fetchBranches={fetchBranches}
                fetchVendors={fetchVendors}
                fetchSpecialities={fetchSpecialities}
                fetchProductTypes={fetchProductTypes}
                fetchProductCategories={fetchProductCategories}
                fetchTasks={fetchTasks}
                fetchProducts={fetchProducts}
                branch={session?.branch}
              />
            </div>
          }
          path="/campaigns"
          title="Campaigns"
        />
      )}
    </div>
  );
}
