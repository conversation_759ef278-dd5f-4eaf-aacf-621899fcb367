import { Metadata } from "next";
import { api, imagePath } from "@/lib/api";
import { cache } from "react";
import EditCustomerForm from "./edit";
import { revalidatePath } from "next/cache";
import BackBtn from "@/components/back";

const getData = cache((id: string) => api.get<User>(`users/${id}`));

export const generateMetadata = async ({
  params,
}: {
  params: Record<string, string>;
}): Promise<Metadata> => {
  const user = await getData(params.customerId);

  return {
    title: user?.name,
  };
};

export default async function AdminShowUser({
  params,
}: {
  params: Record<string, string>;
}) {
  const user = await getData(params.customerId);

  const updateUser = async (data: FormData) => {
    "use server";

    await api.put(`users/${params.customerId}`, data);

    revalidatePath(`/customers/${params.customerId}`);
  };

  return (
    <div className="p-10">
      <BackBtn />
      {user && (
        <EditCustomerForm defaultValues={user} updateUser={updateUser} />
      )}
    </div>
  );
}
