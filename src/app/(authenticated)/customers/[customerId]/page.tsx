import { Metadata } from "next";
import { api, imagePath } from "@/lib/api";
import { cache } from "react";
import Image from "next/image";
import PaginatedTable from "@/components/table";
import Link from "next/link";
import { formatDate } from "date-fns";
import BackBtn from "@/components/back";

const getData = cache((id: string) => api.get<User>(`users/${id}`));

export const generateMetadata = async ({
  params,
}: {
  params: Record<string, string>;
}): Promise<Metadata> => {
  const user = await getData(params.customerId);

  return {
    title: user?.name,
  };
};

export default async function AdminShowUser({
  params,
}: {
  params: Record<string, string>;
}) {
  const user = await getData(params.customerId);

  const orders = await api.get<PaginatedData<Order>>(
    `users/${params.customerId}/orders`,
    { per: 20 },
  );

  return (
    <div className="w-full p-10">
      <BackBtn />

      <div className="page-content space-y-6">
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-4">
          <div className="lg:col-span-1">
            <div className="rounded-lg border border-default-200 bg-white p-6">
              <Image
                src={imagePath(user?.avatar?.url)}
                width="96"
                height="96"
                className="h-24 w-24 rounded-full border border-default-200 bg-default-100 p-1 dark:border-default-600 dark:bg-default-700"
                alt="avatar"
              />
              <h4 className="mb-1 mt-3 text-lg">{user?.name}</h4>
              <div className="mt-6 text-start">
                <h4 className="mb-2.5 text-sm uppercase">About:</h4>
                <p className="mb-6 text-default-400">{user?.details}</p>
                <p className="mb-3 text-zinc-400">
                  <b>Mobile :</b>
                  <span className="ms-2">{user?.phone}</span>
                </p>
                <p className="mb-3 text-zinc-400">
                  <b>Email :</b> <span className="ms-2 ">{user?.email}</span>
                </p>
                {/* <p className="mb-1.5 text-zinc-400">
									<b>Location :</b>{" "}
									<span className="ms-2">Mae Lan</span>
								</p> */}
              </div>
            </div>
          </div>
          
          <div className="lg:col-span-3">
            <div className="rounded-lg border border-default-200">
              <div className="overflow-hidden p-6 ">
                <div className="flex flex-wrap items-center gap-4 sm:justify-between lg:flex-nowrap">
                  <h2 className="text-xl font-semibold text-default-800">
                    Customer Order history
                  </h2>
                  <div className="flex flex-wrap items-center justify-end gap-2">
                    <div className="hs-dropdown relative inline-flex">
                      <button
                        type="button"
                        className="hs-dropdown-toggle flex items-center gap-2 rounded-md bg-default-100 px-4 py-3 text-sm font-medium text-default-700 transition-all xl:px-5"
                      >
                        Sort : Ascending{" "}
                        <svg
                          stroke="currentColor"
                          fill="none"
                          strokeWidth="2"
                          viewBox="0 0 24 24"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          height="16"
                          width="16"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path d="m6 9 6 6 6-6"></path>
                        </svg>
                      </button>
                      <div className="hs-dropdown-menu hs-dropdown-open:opacity-100 z-20 mt-4 hidden min-w-[200px] rounded-lg border border-default-100 bg-white p-1.5 opacity-0 shadow-[rgba(17,_17,_26,_0.1)_0px_0px_16px] transition-[opacity,margin] dark:bg-default-50">
                        <ul className="flex flex-col gap-1">
                          <li>
                            <span className="flex items-center gap-3 rounded bg-default-100 px-3 py-2 font-normal text-default-700 transition-all hover:bg-default-100 hover:text-default-700">
                              Ascending
                            </span>
                          </li>
                          <li>
                            <span className="flex items-center gap-3 rounded px-3 py-2 font-normal text-default-600 transition-all hover:bg-default-100 hover:text-default-700">
                              Descending
                            </span>
                          </li>
                        </ul>
                      </div>
                    </div>
                    <div className="hs-dropdown relative inline-flex">
                      <button
                        type="button"
                        className="hs-dropdown-toggle flex items-center gap-2 rounded-md bg-default-100 px-4 py-3 text-sm font-medium text-default-700 transition-all xl:px-5"
                      >
                        Status : All{" "}
                        <svg
                          stroke="currentColor"
                          fill="none"
                          strokeWidth="2"
                          viewBox="0 0 24 24"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          height="16"
                          width="16"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path d="m6 9 6 6 6-6"></path>
                        </svg>
                      </button>
                      <div className="hs-dropdown-menu hs-dropdown-open:opacity-100 z-20 mt-4 hidden min-w-[200px] rounded-lg border border-default-100 bg-white p-1.5 opacity-0 shadow-[rgba(17,_17,_26,_0.1)_0px_0px_16px] transition-[opacity,margin] dark:bg-default-50">
                        <ul className="flex flex-col gap-1">
                          <li>
                            <span className="flex items-center gap-3 rounded bg-default-100 px-3 py-2 font-normal text-default-700 transition-all hover:bg-default-100 hover:text-default-700">
                              All
                            </span>
                          </li>
                          <li>
                            <span className="flex items-center gap-3 rounded px-3 py-2 font-normal text-default-600 transition-all hover:bg-default-100 hover:text-default-700">
                              Paid
                            </span>
                          </li>
                          <li>
                            <span className="flex items-center gap-3 rounded px-3 py-2 font-normal text-default-600 transition-all hover:bg-default-100 hover:text-default-700">
                              Cancelled
                            </span>
                          </li>
                          <li>
                            <span className="flex items-center gap-3 rounded px-3 py-2 font-normal text-default-600 transition-all hover:bg-default-100 hover:text-default-700">
                              Refunded
                            </span>
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="relative overflow-x-auto">
                <div className="inline-block min-w-full align-middle">
                  <div className="overflow-hidden">
                    {orders && (
                      <PaginatedTable<Order>
                        columns={[
                          {
                            id: "id",
                            title: "Order",
                            render: (record) => (
                              <Link href={`/orders/${record.id}`} className="uppercase">
                                {record.id}
                              </Link>
                            ),
                          },
                          {
                            id: "total",
                            title: "Total",
                            render: (record) => <span>KES {record.total}</span>,
                          },
                          {
                            id: "status",
                            title: "Status",
                          },
                          {
                            id: "createdAt",
                            title: "Date",
                            render: (record) => (
                              <span>
                                {formatDate(
                                  new Date(record.createdAt),
                                  "EEE, MMM dd, yyyy hh:mm a",
                                )}
                              </span>
                            ),
                          },
                        ]}
                        records={orders}
                        path="/orders"
                        title="Orders"
                      />
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
