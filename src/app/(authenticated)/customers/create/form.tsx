"use client";

import { useForm } from "react-hook-form";
import toast from "react-hot-toast";

export default function CustomerCreateForm({
  storeCustomer,
}: {
  storeCustomer: (formData: FormData) => Promise<void>;
}) {
  const { register, handleSubmit, formState } = useForm<User>();

  const onSubmit = async (customer: User) => {
    const formData = new FormData();
    formData.append("firstName", customer.firstName);
    formData.append("lastName", customer.lastName);
    formData.append("email", customer.email);
    formData.append("phone", customer.phone);
    formData.append("idpass", customer.idpass);

    toast.promise(storeCustomer(formData), {
      loading: "Creating customer...",
      success: "Customer created!",
      error: "Failed to create customer",
    });
  };

  return (
    <div className="w-full">
      <div className="page-content space-y-6 p-6">
        <div className="flex w-full items-center justify-between">
          <h4 className="text-xl font-medium">Add Customer</h4>
          <ol
            aria-label="Breadcrumb"
            className="hidden min-w-0 items-center gap-2 whitespace-nowrap md:flex"
          >
            <li className="text-sm">
              <a
                className="flex items-center gap-2 align-middle text-default-800 transition-all hover:text-default-500"
                href="/yum_r/admin/customers"
              >
                Customers
                <svg
                  stroke="currentColor"
                  fill="none"
                  strokeWidth="2"
                  viewBox="0 0 24 24"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  height="16"
                  width="16"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path d="m9 18 6-6-6-6"></path>
                </svg>
              </a>
            </li>
            <li
              aria-current="page"
              className="truncate text-sm font-medium text-primary hover:text-default-500"
            >
              Add Customer
            </li>
          </ol>
        </div>
        <div className="rounded-lg border border-default-200">
          <form className="p-6" onSubmit={handleSubmit(onSubmit)}>
            <div className="mb-6 grid gap-6 lg:grid-cols-2">
              <div className="relative max-w-full">
                <label
                  className="mb-2 block text-sm font-medium text-default-900"
                  htmlFor="fname"
                >
                  First Name
                </label>
                <div className="relative max-w-full">
                  <input
                    type="text"
                    placeholder="Enter Your First Name"
                    {...register("firstName", { required: true })}
                    className="form-input w-full rounded-lg border border-default-200 px-4 py-3 dark:bg-default-50"
                  />
                </div>
              </div>
              <div className="relative max-w-full">
                <label
                  className="mb-2 block text-sm font-medium text-default-900"
                  htmlFor="lname"
                >
                  Last Name
                </label>
                <div className="relative max-w-full">
                  <input
                    type="text"
                    placeholder="Enter Your Last Name"
                    {...register("lastName", { required: true })}
                    className="form-input w-full rounded-lg border border-default-200 px-4 py-3 dark:bg-default-50"
                  />
                </div>
              </div>
              <div className="relative max-w-full">
                <label
                  className="mb-2 block text-sm font-medium text-default-900"
                  htmlFor="username"
                >
                  ID/Passport
                </label>
                <div className="relative max-w-full">
                  <input
                    type="text"
                    placeholder="Enter Your Last Name"
                    {...register("idpass", { required: true })}
                    className="form-input w-full rounded-lg border border-default-200 px-4 py-3 dark:bg-default-50"
                  />
                </div>
              </div>
              <div className="relative max-w-full">
                <label
                  className="mb-2 block text-sm font-medium text-default-900"
                  htmlFor="email"
                >
                  Email
                </label>
                <div className="relative max-w-full">
                  <input
                    type="email"
                    placeholder="<EMAIL>"
                    {...register("email", { required: true })}
                    className="form-input w-full rounded-lg border border-default-200 px-4 py-3 dark:bg-default-50"
                  />
                </div>
              </div>
              <div className="relative max-w-full">
                <label
                  className="mb-2 block text-sm font-medium text-default-900"
                  htmlFor="phoneNo"
                >
                  Phone Number
                </label>
                <div className="relative max-w-full">
                  <input
                    type="text"
                    placeholder="******-XXX-4567"
                    {...register("phone", { required: true })}
                    className="form-input w-full rounded-lg border border-default-200 px-4 py-3 dark:bg-default-50"
                  />
                </div>
              </div>
              <div>
                <label
                  htmlFor="billing-country1"
                  className="mb-2 block text-sm font-medium text-default-900"
                >
                  Country/Region
                </label>
                <div className="relative">
                  <div
                    className="css-b62m3t-container block w-full cursor-pointer rounded-lg border border-default-200 bg-transparent focus-within:border focus-within:border-primary dark:bg-default-50"
                    id="billing-country1"
                  >
                    <span
                      id="react-select-billing-country-live-region"
                      className="css-7pg0cj-a11yText"
                    ></span>
                    <span
                      aria-live="polite"
                      aria-atomic="false"
                      aria-relevant="additions text"
                      role="log"
                      className="css-7pg0cj-a11yText"
                    ></span>
                    <div className="react-select__control css-cp01gg-control">
                      <div className="react-select__value-container css-14oxtc6">
                        <div
                          className="react-select__placeholder css-1vlsb4t-placeholder"
                          id="react-select-billing-country-placeholder"
                        >
                          Select...
                        </div>
                        <div
                          className="react-select__input-container css-n9qnu9"
                          data-
                        >
                          <input
                            className="react-select__input"
                            id="react-select-billing-country-input"
                            type="text"
                            aria-autocomplete="list"
                            aria-expanded="false"
                            aria-haspopup="true"
                            role="combobox"
                            aria-activedescendant=""
                            aria-describedby="react-select-billing-country-placeholder"
                          />
                        </div>
                      </div>
                      <div className="react-select__indicators css-1wy0on6">
                        <span className="react-select__indicator-separator css-j4w2j1-indicatorSeparator"></span>
                        <div
                          className="react-select__indicator react-select__dropdown-indicator css-g56vrd-indicatorContainer"
                          aria-hidden="true"
                        >
                          <svg
                            height="20"
                            width="20"
                            viewBox="0 0 20 20"
                            aria-hidden="true"
                            focusable="false"
                            className="css-8mmkcg"
                          >
                            <path d="M4.516 7.548c0.436-0.446 1.043-0.481 1.576 0l3.908 3.747 3.908-3.747c0.533-0.481 1.141-0.446 1.574 0 0.436 0.445 0.408 1.197 0 1.615-0.406 0.418-4.695 4.502-4.695 4.502-0.217 0.223-0.502 0.335-0.787 0.335s-0.57-0.112-0.789-0.335c0 0-4.287-4.084-4.695-4.502s-0.436-1.17 0-1.615z"></path>
                          </svg>
                        </div>
                      </div>
                    </div>
                    <input name="country" type="hidden" />
                  </div>
                </div>
              </div>
              <div>
                <label
                  htmlFor="billing-state-province1"
                  className="mb-2 block text-sm font-medium text-default-900"
                >
                  State/Province
                </label>
                <div className="relative">
                  <div
                    className="css-b62m3t-container block w-full cursor-pointer rounded-lg border border-default-200 bg-transparent focus-within:border focus-within:border-primary dark:bg-default-50"
                    id="billing-state-province1"
                  >
                    <span
                      id="react-select-billing-state-province-live-region"
                      className="css-7pg0cj-a11yText"
                    ></span>
                    <span
                      aria-live="polite"
                      aria-atomic="false"
                      aria-relevant="additions text"
                      role="log"
                      className="css-7pg0cj-a11yText"
                    ></span>
                    <div className="react-select__control css-cp01gg-control">
                      <div className="react-select__value-container css-14oxtc6">
                        <div
                          className="react-select__placeholder css-1vlsb4t-placeholder"
                          id="react-select-billing-state-province-placeholder"
                        >
                          Select...
                        </div>
                        <div
                          className="react-select__input-container css-n9qnu9"
                          data-
                        >
                          <input
                            className="react-select__input"
                            type="text"
                            aria-autocomplete="list"
                            aria-expanded="false"
                            aria-haspopup="true"
                            role="combobox"
                            aria-activedescendant=""
                            aria-describedby="react-select-billing-state-province-placeholder"
                          />
                        </div>
                      </div>
                      <div className="react-select__indicators css-1wy0on6">
                        <span className="react-select__indicator-separator css-j4w2j1-indicatorSeparator"></span>
                        <div
                          className="react-select__indicator react-select__dropdown-indicator css-g56vrd-indicatorContainer"
                          aria-hidden="true"
                        >
                          <svg
                            height="20"
                            width="20"
                            viewBox="0 0 20 20"
                            aria-hidden="true"
                            focusable="false"
                            className="css-8mmkcg"
                          >
                            <path d="M4.516 7.548c0.436-0.446 1.043-0.481 1.576 0l3.908 3.747 3.908-3.747c0.533-0.481 1.141-0.446 1.574 0 0.436 0.445 0.408 1.197 0 1.615-0.406 0.418-4.695 4.502-4.695 4.502-0.217 0.223-0.502 0.335-0.787 0.335s-0.57-0.112-0.789-0.335c0 0-4.287-4.084-4.695-4.502s-0.436-1.17 0-1.615z"></path>
                          </svg>
                        </div>
                      </div>
                    </div>
                    <input name="state" type="hidden" />
                  </div>
                </div>
              </div>
              <div>
                <label
                  htmlFor="billing-zip-code1"
                  className="mb-2 block text-sm font-medium text-default-900"
                >
                  ZIP/Postal Code
                </label>
                <div className="relative">
                  <div
                    className="css-b62m3t-container block w-full cursor-pointer rounded-lg border border-default-200 bg-transparent focus-within:border focus-within:border-primary dark:bg-default-50"
                    id="billing-zip-code1"
                  >
                    <span
                      id="react-select-billing-zip-code-live-region"
                      className="css-7pg0cj-a11yText"
                    ></span>
                    <span
                      aria-live="polite"
                      aria-atomic="false"
                      aria-relevant="additions text"
                      role="log"
                      className="css-7pg0cj-a11yText"
                    ></span>
                    <div className="react-select__control css-cp01gg-control">
                      <div className="react-select__value-container css-14oxtc6">
                        <div
                          className="react-select__placeholder css-1vlsb4t-placeholder"
                          id="react-select-billing-zip-code-placeholder"
                        >
                          Select...
                        </div>
                        <div
                          className="react-select__input-container css-n9qnu9"
                          data-
                        >
                          <input className="react-select__input" type="text" />
                        </div>
                      </div>
                      <div className="react-select__indicators css-1wy0on6">
                        <span className="react-select__indicator-separator css-j4w2j1-indicatorSeparator"></span>
                        <div
                          className="react-select__indicator react-select__dropdown-indicator css-g56vrd-indicatorContainer"
                          aria-hidden="true"
                        >
                          <svg
                            height="20"
                            width="20"
                            viewBox="0 0 20 20"
                            aria-hidden="true"
                            focusable="false"
                            className="css-8mmkcg"
                          >
                            <path d="M4.516 7.548c0.436-0.446 1.043-0.481 1.576 0l3.908 3.747 3.908-3.747c0.533-0.481 1.141-0.446 1.574 0 0.436 0.445 0.408 1.197 0 1.615-0.406 0.418-4.695 4.502-4.695 4.502-0.217 0.223-0.502 0.335-0.787 0.335s-0.57-0.112-0.789-0.335c0 0-4.287-4.084-4.695-4.502s-0.436-1.17 0-1.615z"></path>
                          </svg>
                        </div>
                      </div>
                    </div>
                    <input name="zipCode" type="hidden" />
                  </div>
                </div>
              </div>
              <div className="relative w-full lg:col-span-2">
                <label
                  className="mb-2 block text-sm font-medium text-default-900"
                  htmlFor="description"
                >
                  Description
                </label>
                <div className="relative w-full">
                  <textarea
                    placeholder="Jot something down.."
                    name="description"
                    rows={5}
                    className="w-full rounded-lg border border-default-200 px-4 py-3 dark:bg-default-50"
                  ></textarea>
                </div>
              </div>
            </div>
            <div className="flex flex-wrap justify-end gap-4">
              <button
                type="reset"
                className="flex items-center justify-center gap-2 rounded-lg bg-red-500/10 px-6 py-3 text-center text-sm font-semibold text-red-500 shadow-sm transition-colors duration-200 hover:bg-red-500 hover:text-white"
              >
                <svg
                  stroke="currentColor"
                  fill="none"
                  strokeWidth="2"
                  viewBox="0 0 24 24"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  height="20"
                  width="20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path d="m7 21-4.3-4.3c-1-1-1-2.5 0-3.4l9.6-9.6c1-1 2.5-1 3.4 0l5.6 5.6c1 1 1 2.5 0 3.4L13 21"></path>
                  <path d="M22 21H7"></path>
                  <path d="m5 11 9 9"></path>
                </svg>
                Clear
              </button>
              <button
                type="submit"
                className="flex items-center justify-center gap-2 rounded-lg bg-primary px-6 py-3 text-center text-sm font-semibold text-white shadow-sm transition-all duration-200 hover:bg-default-500"
              >
                <svg
                  stroke="currentColor"
                  fill="none"
                  strokeWidth="2"
                  viewBox="0 0 24 24"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  height="20"
                  width="20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                  <polyline points="17 21 17 13 7 13 7 21"></polyline>
                  <polyline points="7 3 7 8 15 8"></polyline>
                </svg>
                Save
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
