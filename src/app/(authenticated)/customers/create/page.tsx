import { api } from "@/lib/api";
import CustomerCreateForm from "./form";

export const dynamic = "force-dynamic";
export const fetchCache = "force-no-store";

export default function page() {
  const createCustomer = async (formData: FormData) => {
    "use server";

    await api.post<User>(`users`, formData);
  };
  return (
    <div className="page-content space-y-6 p-6">
      <CustomerCreateForm storeCustomer={createCustomer} />
    </div>
  );
}
