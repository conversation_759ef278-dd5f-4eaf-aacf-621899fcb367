import PaginatedTable from "@/components/table";
import { api } from "@/lib/api";

export default async function page() {
  const devices = await api.get<PaginatedData<Device>>("devices");
  return (
    <div>
      {devices && (
        <PaginatedTable<Device>
          records={devices}
          columns={[
            {
              id: "name",
              title: "Name",
            },
            {
              id: "id",
              title: "Details",
            },
            {
              id: "token",
              title: "Token",
            },
          ]}
          path="/devices"
        />
      )}
    </div>
  );
}
