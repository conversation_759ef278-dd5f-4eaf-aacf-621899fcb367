"use client";

import { useClickOutside } from "@/hooks/useClickOutside";
import { usePathname, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

import { Icon } from "@/components/icon";
import LanguageSwitcher from "@/components/lang-switcher";
import { ThemeSwitcher } from "@/components/theme-switcher";
import logoImg from "@/images/logo.png";
import {
  Badge,
  Button,
  Dropdown,
  DropdownItem,
  DropdownMenu,
  DropdownSection,
  DropdownTrigger,
  Tooltip,
} from "@nextui-org/react";
import { formatDistanceToNow } from "date-fns";
import Image from "next/image";
import Link from "next/link";
import { imagePath } from "@/lib/api";
import React from "react";


interface User {
	id: string;
	title: string;
	firstName: string;
	lastName: string;
	gender: string | null;
	dob: string | null;
	email: string;
	phone: string;
	idpass: string;
	rememberMeToken: string | null;
	details: string | null;
	location: Record<string, any> | null;
	geom: string | null;
	avatar: AttachmentContract | null;
	createdAt: string;
	updatedAt: string;
	name: string;
	status: string;
	avatarUrl: string;
	initials: string;
	devices: Device[];
	roles: Role[];
	permissions: Permission[];
	notifications: DatabaseNotification[];

	// computed
	identifier: string;
	online: boolean;
	vendorId: string;
}


export default function PortalHeader({
  user,
  vendor,
  notifications,
  logoutUser,
}: {
  user: User | undefined;
  branch?: Branch;
  vendor?: Vendor;
  notifications?: PaginatedData<DatabaseNotification>;
  logoutUser: () => Promise<void>;
}) {


  
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const [title, setTitle] = useState("Home");
  const [subtitle, setSubTitle] = useState("Home");

  const [collapseSidebar, setCollapseSidebar] = useState<boolean>();
  const [darkMode, setDarkMode] = useState(false);


  const toggleTheme = () => {
    setDarkMode(!darkMode);
    document.documentElement?.classList.toggle("dark");
  };

  useEffect(() => {
    setTitle(() => {
      const t = document.title;

      let pageTitle = t.split(" :: ")[0];

      if (searchParams.has('page')) {
        pageTitle += ` (Page ${searchParams.get('page')})`;
      }

      return pageTitle;
    });

    setSubTitle(
      document
        .querySelector('meta[name="description"]')
        ?.getAttribute("content") || "All your needs in one place",
    );
  }, [pathname, searchParams]);

  useEffect(() => {
    document.getElementById("application-sidebar")?.classList.toggle("!hidden");
    document.getElementById("application-body")?.classList.toggle("lg:!ps-0");
    document.getElementById("application-header")?.classList.toggle("lg:!ps-0");
  }, [collapseSidebar]);

  return (
    <header
      className="hide-in-print sticky top-0 z-40 flex h-20 w-full border-b border-default-200 bg-white dark:bg-default-800 lg:ps-64 print:hidden"
      id="application-header"
    >
      <nav className="flex w-full items-center gap-4 px-6">
        <div className="flex lg:hidden">
          <button
            type="button"
            className="text-default-500 hover:text-default-600"
            data-hs-overlay="#application-sidebar"
            aria-controls="application-sidebar"
            aria-label="Toggle navigation"
          >
            <svg
              stroke="currentColor"
              fill="none"
              strokeWidth="2"
              viewBox="0 0 24 24"
              strokeLinecap="round"
              strokeLinejoin="round"
              height="24"
              width="24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <line x1="3" x2="21" y1="6" y2="6"></line>
              <line x1="3" x2="21" y1="12" y2="12"></line>
              <line x1="3" x2="21" y1="18" y2="18"></line>
            </svg>
          </button>
        </div>

        <div className="flex lg:hidden">
        <Link href="/">
          <Image
            src={vendor ? imagePath(vendor.logo?.url) : logoImg}
            height={60}
            width={100}
            alt="logo"
            className="flex w-20 dark:hidden"
          />

          <Image
            src={vendor ? imagePath(vendor.logo?.url) : logoImg}
            height={60}
            width={100}
            alt="logo"
            className="hidden w-20 dark:flex"
          />
        </Link>
        </div>

        <div className="hidden items-center gap-3 lg:flex">
          <button
            type="button"
            onClick={() => setCollapseSidebar(!collapseSidebar)}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="size-6"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"
              />
            </svg>
          </button>

          {collapseSidebar &&  <Link href="/">
          <Image
            src={vendor ? imagePath(vendor.logo?.url) : logoImg}
            height={60}
            width={100}
            alt="logo"
            className="flex w-20 dark:hidden"
          />

          <Image
            src={vendor ? imagePath(vendor.logo?.url) : logoImg}
            height={60}
            width={100}
            alt="logo"
            className="hidden w-20 dark:flex"
          />
        </Link>}

          <h1 className="text-xl font-semibold text-default-800 dark:text-white">{title}</h1>
        </div>

        <div className="ms-auto flex items-center gap-3">
          <ThemeSwitcher />

          <LanguageSwitcher />

          <div className="hidden md:flex">
            <Dropdown
              classNames={{
                base: "min-w-[25rem] max-w-[30rem] max-h-[500px]",
              }}
              aria-label="Notifications dropdown"
              placement="bottom-end"
              showArrow
            >
              <Badge
                content={notifications?.meta.total}
                shape="circle"
                color="danger"
                size="md"
                isInvisible={notifications?.meta.total === 0 ? true : false}
              >
                <DropdownTrigger aria-label="Notifications trigger">
                  <Button isIconOnly className="border" size="sm">
                    <Icon name="icon-[heroicons-outline--bell]" />
                  </Button>
                </DropdownTrigger>
              </Badge>

              <DropdownMenu aria-label="actions dropdown">
                <DropdownItem
                  key={"title"}
                  className="opacity-100"
                  textValue="notifications"
                >
                  <div className="flex items-center justify-between">
                    <h2 className="text-base font-bold">Notifications</h2>
                    <div className="flex items-center">
                      <Button
                        size="sm"
                        className="font-semibold hover:underline"
                      >
                        view all
                      </Button>
                      <Tooltip
                        content="Mark all as read"
                        showArrow
                        placement="bottom"
                      >
                        <Button
                          size="sm"
                          isIconOnly
                          color="danger"
                          variant="light"
                        >
                          <Icon
                            name="icon-[hugeicons--tick-double-01]"
                            classNames="h-5 w-5"
                          />
                        </Button>
                      </Tooltip>
                    </div>
                  </div>
                </DropdownItem>

                <DropdownSection
                  className="border-t border-dashed"
                  aria-label="notifications"
                  classNames={{
                    base: "lg:h-[400px] xl:h-[500px] overflow-y-scroll scrollbar-thin scrollbar-webkit",
                  }}
                >
                  {(notifications as any).data.map((note: any) => (
                    <DropdownItem
                      key={note.id}
                      textValue="notification item"
                      className="mb-4 flex items-center"
                      href={`/notifications/${note.id}`}
                      aria-label="profile and actions dropdown"
                    >
                      <div className="ms-2 flex-grow truncate">
                        <div className="flex items-center justify-between">
                          <h5 className="text-sm font-medium text-default-800">
                            {note.data.title}
                          </h5>
                          <small className="inline-flex text-xs text-default-500">
                            {formatDistanceToNow(new Date(note.created_at), {
                              addSuffix: true,
                            })}
                          </small>
                        </div>
                        <small className="text-default-400">
                          {note.data.body}
                        </small>
                      </div>
                    </DropdownItem>
                  ))}
                </DropdownSection>
              </DropdownMenu>
            </Dropdown>
          </div>
          <div className="ml-5 flex items-center gap-2">
            <div className="relative h-9 w-9 cursor-pointer overflow-hidden rounded-md">
              <Image
                src={`https://ui-avatars.com/api/?name=${user?.name}&background=D7F8E6&font-size=0.4&bold=true&color=5E9C8F`}
                alt="Profile avatar"
                fill
                className="object-contain"
              />
            </div>
            <div className="hidden text-start lg:block">
              <p className="text-xs font-semibold text-default-900">
                {user?.name}
                {/* - {user?.roles?.map((role) => role.name).join(", ")} */}
              </p>
              <p className="text-xs text-default-700">{user?.email}</p>
            </div>
          </div>
        </div>
      </nav>
    </header>
  );
}
