import Schedule from "./schedule";
import { api } from "@/lib/api";
import { Metadata } from "next";
import { auth } from "@/auth";
import { revalidatePath } from "next/cache";
import { cache } from "react";

export const dynamic = "force-dynamic";
export const fetchCache = "force-no-store";

const getBranch = cache((branchId: string) =>
  api.get<Branch>(`branches/${branchId}`),
);

export const generateMetadata = async (): Promise<Metadata> => {
  const session = await auth();

  return {
    title: `${session?.vendor?.name} ${session?.branch?.name} Working Hours`,
    description: "Working hours",
    keywords: ["working", "hours", "working hours"],
  };
};

export default async function Hours() {
  const session = await auth();

  let branchId = session?.branch?.id;

  const branch = await getBranch(branchId!);

  const updateSchedule = async (schedule: Record<string, any>) => {
    "use server";

    await api.put<Branch>(`branches/${branchId}`, {
      hours: { schedule },
    });

    revalidatePath("/hours");
  };

  return (
    <div className="p-4">
      {branch && (
        <Schedule
          key={JSON.stringify(branch.hours?.schedule)}
          hours={branch.hours?.schedule || null}
          updateSchedule={updateSchedule}
        />
      )}
    </div>
  );
}
