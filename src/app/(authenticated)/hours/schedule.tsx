"use client";

import React from "react";
import { useForm, useField<PERSON><PERSON><PERSON>, Controller } from "react-hook-form";
import toast from "react-hot-toast";
import Select from "react-select";

export default function Schedule({
  hours,
  updateSchedule,
}: {
  hours: ScheduleHours[] | null;
  updateSchedule: (hours: ScheduleHours[]) => Promise<void>;
}) {
  const { control, handleSubmit, reset } = useForm<ScheduleForm>({
    defaultValues: {
      hours: hours || [{ day: "", from: "", to: "" }],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: "hours",
  });

  const days = [
    {
      value: "Mon",
      label: "Monday",
    },
    {
      value: "Tue",
      label: "Tuesday",
    },
    {
      value: "Wed",
      label: "Wednesday",
    },
    {
      value: "Thu",
      label: "Thursday",
    },
    {
      value: "Fri",
      label: "Friday",
    },
    {
      value: "Sat",
      label: "Saturday",
    },
    {
      value: "Sun",
      label: "Sunday",
    },
  ];

  const hoursInADay = Array.from({ length: 48 }, (_, i) => {
    const hour = Math.floor(i / 2)
      .toString()
      .padStart(2, "0");
    const minute = i % 2 === 0 ? "00" : "30";
    const time = hour + minute;

    return {
      value: time,
      label: time,
    };
  });

  const onSubmit = ({ hours }: ScheduleForm) => {
    toast
      .promise(updateSchedule(hours), {
        loading: "Updating schedule...",
        success: "Schedule updated",
        error: "Failed to update schedule",
      })
      .then(() => {
        reset();
      });
  };

  const transformSchedule = (schedule: Record<string, string[]>[]) => {
    let result: Record<string, string[]> = {};

    schedule.forEach((item) => {
      const day = Object.keys(item)[0];
      const times = item[day];
      const intervals = [];

      for (let i = 0; i < times.length - 1; i++) {
        intervals.push(`${times[i]}-${times[i + 1]}`);
      }

      result[day] = intervals;
    });

    return result;
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <table className="w-full text-left text-sm text-default-500 dark:text-default-400">
        <thead>
          <tr className="mb-4 flex space-x-4">
            <th className="w-60">Day of week</th>
            <th className="w-80">From - To</th>
            <th className=""></th>
          </tr>
        </thead>
        <tbody className="dark:text-default-400">
          {fields.map((field, index) => (
            <tr key={field.id} className="mb-4 flex space-x-4">
              <td className="w-60">
                <Controller
                  control={control}
                  name={`hours.${index}.day`}
                  render={({ field: { onChange, value } }) => (
                    <Select
                      options={days}
                      placeholder="Select day"
                      onChange={(v) => onChange(v?.value!)}
                      defaultValue={{
                        value: value,
                        label: days.find((d) => d.value === value)?.label,
                      }}
                      classNames={{
                        control: () =>
                          "!rounded-lg !border !border-default-600 pl-4 w-full !py-1",
                      }}
                      components={{
                        IndicatorSeparator: () => null,
                      }}
                    />
                  )}
                />
              </td>

              <td className="flex w-80">
                <Controller
                  control={control}
                  name={`hours.${index}.from`}
                  defaultValue={field.from}
                  render={({ field: { onChange, value } }) => (
                    <Select
                      options={hoursInADay}
                      placeholder="From"
                      onChange={(v) => onChange(v?.value!)}
                      defaultValue={{
                        value: value,
                        label: hoursInADay.find((h) => h.value === value)
                          ?.label,
                      }}
                      classNames={{
                        control: () =>
                          "!rounded-l-lg !rounded-r-none !border !border-default-600 !border-r-none pl-4 !py-1",
                      }}
                      className="flex-1"
                      components={{
                        IndicatorSeparator: () => null,
                      }}
                    />
                  )}
                />

                <Controller
                  control={control}
                  name={`hours.${index}.to`}
                  defaultValue={field.to}
                  render={({ field: { onChange, value } }) => (
                    <Select
                      options={hoursInADay}
                      placeholder="To"
                      onChange={(v) => onChange(v?.value!)}
                      defaultValue={{
                        value: value,
                        label: hoursInADay.find((h) => h.value === value)
                          ?.label,
                      }}
                      classNames={{
                        control: () =>
                          "!rounded-r-lg !rounded-l-none !border !border-l-0 !border-default-600 pl-4 !py-1",
                      }}
                      className="flex-1"
                      components={{
                        IndicatorSeparator: () => null,
                      }}
                    />
                  )}
                />
              </td>

              <td className="flex items-center gap-4">
                <button
                  type="button"
                  onClick={() => remove(index)}
                  className="text-red-600 "
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={1.5}
                    stroke="currentColor"
                    className="size-6"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"
                    />
                  </svg>
                </button>

               {index === fields.length - 1 && <button
                  className="text-default-600"
                  type="button"
                  onClick={() => append({ day: "", from: "", to: "" })}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={1.5}
                    stroke="currentColor"
                    className="size-7"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                    />
                  </svg>
                </button>}
              </td>
            </tr>
          ))}
        </tbody>
      </table>

      <button
        className="rounded-lg bg-primary px-4 py-2 text-white"
        type="submit"
      >
        Update schedule
      </button>
    </form>
  );
}
