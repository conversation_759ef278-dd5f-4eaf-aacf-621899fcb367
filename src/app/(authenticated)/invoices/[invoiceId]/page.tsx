import { api } from "@/lib/api";
import Image from "next/image";

export default async function InvoicePage({
  params,
}: {
  params: { invoiceId: string };
}) {
  const invoice = await api.get<Invoice>(`invoices/${params.invoiceId}`);

  return (
    <div className="mx-auto rounded-lg bg-white px-4 py-10 shadow-lg">
      <div className="mb-8 flex items-center justify-between">
        <div className="flex items-center">
          <Image
            className="mr-2 h-8 w-8"
            src="https://tailwindflex.com/public/images/logos/favicon-32x32.png"
            alt="Logo"
          />
          <div className="text-lg font-semibold text-default-700">
            Your Company Name
          </div>
        </div>
        <div className="text-default-700">
          <div className="mb-2 text-xl font-bold">INVOICE</div>
          <div className="text-sm">Date: 01/05/2023</div>
          <div className="text-sm">Invoice #: {invoice?.number}</div>
        </div>
      </div>
      <div className="mb-8 border-b-2 border-default-300 pb-8">
        <h2 className="mb-4 text-2xl font-bold">Bill To:</h2>
        <div className="mb-2 text-default-700">John Doe</div>
        <div className="mb-2 text-default-700">123 Main St.</div>
        <div className="mb-2 text-default-700">Anytown, USA 12345</div>
        <div className="text-default-700"><EMAIL></div>
      </div>
      <table className="mb-8 w-full text-left">
        <thead>
          <tr>
            <th className="py-2 font-bold uppercase text-default-700">
              Description
            </th>
            <th className="py-2 font-bold uppercase text-default-700">
              Quantity
            </th>
            <th className="py-2 font-bold uppercase text-default-700">Price</th>
            <th className="py-2 font-bold uppercase text-default-700">Total</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td className="py-4 text-default-700">Product 1</td>
            <td className="py-4 text-default-700">1</td>
            <td className="py-4 text-default-700">$100.00</td>
            <td className="py-4 text-default-700">$100.00</td>
          </tr>
          <tr>
            <td className="py-4 text-default-700">Product 2</td>
            <td className="py-4 text-default-700">2</td>
            <td className="py-4 text-default-700">$50.00</td>
            <td className="py-4 text-default-700">$100.00</td>
          </tr>
          <tr>
            <td className="py-4 text-default-700">Product 3</td>
            <td className="py-4 text-default-700">3</td>
            <td className="py-4 text-default-700">$75.00</td>
            <td className="py-4 text-default-700">$225.00</td>
          </tr>
        </tbody>
      </table>
      <div className="mb-8 flex justify-end">
        <div className="mr-2 text-default-700">Subtotal:</div>
        <div className="text-default-700">$425.00</div>
      </div>
      <div className="mb-8 text-right">
        <div className="mr-2 text-default-700">Tax:</div>
        <div className="text-default-700">$25.50</div>
      </div>
      <div className="mb-8 flex justify-end">
        <div className="mr-2 text-default-700">Total:</div>
        <div className="text-xl font-bold text-default-700">$450.50</div>
      </div>
      <div className="mb-8 border-t-2 border-default-300 pt-8">
        <div className="mb-2 text-default-700">
          Payment is due within 30 days. Late payments are subject to fees.
        </div>
        <div className="mb-2 text-default-700">
          Please make checks payable to Your Company Name and mail to:
        </div>
        <div className="text-default-700">123 Main St., Anytown, USA 12345</div>
      </div>
    </div>
  );
}
