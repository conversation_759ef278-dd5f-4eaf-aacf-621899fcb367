import { api } from "@/lib/api";
import page from "../page";
import PaginatedTable from "@/components/table";
import Link from "next/link";
import { formatDate } from "date-fns";

export default async function index({
    searchParams,
}: {
    searchParams: Record<string, string>;
}) {
    const invoices = await api.get<PaginatedData<Invoice>>("invoices", {
        page: 1,
        ...searchParams,
    });

  return (
    <div>
        {invoices && <PaginatedTable<Invoice>
            records={invoices}
            columns={[
                {
                    id: "name",
                    title: "Name",
                    render: (invoice) => (
                        <Link href={`/invoices/${invoice.id}`}>{invoice.number}</Link>
                    ),
                },
                {
                    id: "amount",
                    title: "Amount",
                    render: (invoice) => <>KES {invoice.amount}</>,
                },
                {
                    id: "status",
                    title: "Status",
                },
                {
                    id: "createdAt",
                    title: "Date",
                    render: (invoice) => (
                        <>
                            {formatDate(
                                new Date(invoice.createdAt),
                                "EEE, MMM dd, yyyy hh:mm a",
                            )}
                        </>
                    ),
                },
                {
                    id: "actions",
                    title: "Actions",
                    render: (invoice) => (
                        <div className="flex items-center space-x-2">
                            <Link href={`/invoices/${invoice.id}/edit`}>
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    strokeWidth={1.5}
                                    stroke="currentColor"
                                    className="h-6 w-6"
                                >                                    
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        d="M16.862 4.487a1.875 1.875 0 1 1 2.65 1.445L9.142 8.582c.723.529.77 1.393.233 1.946l-2.857 5.803a2.25 2.25 0 0 1-2.244 2.077H6.75a2.25 2.25 0 0 1-2.244-2.077l-2.857-5.803c-.814-.633.233-1.946 1.145-1.946z"
                                    />
                                </svg>
                            </Link>
                            <Link href={`/invoices/${invoice.id}`}>
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    strokeWidth={1.5}
                                    stroke="currentColor"
                                    className="h-6 w-6"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        d="M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"
                                    />
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"
                                    />
                                </svg>
                            </Link>
                        </div>
                    ),
                },
            ]}
            title="Invoices"
            path="/invoices"
            tools={
                <div className="mb-4 flex items-center justify-between px-4">
                    <p></p>
                    <Link
                        href="/invoices/create"
                        className="rounded-lg bg-primary px-6 py-2 text-white"
                    >
                        Create invoice
                    </Link>
                </div>
            }
        />}         
    </div>
  )
}