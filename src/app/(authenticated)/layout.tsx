import { PropsWithChildren, cache } from "react";
import PortalHeader from "./header";
import PortalSidebar from "./sidebar";
import { auth, signOut } from "@/auth";
import { api } from "@/lib/api";
import BackToTop from "./top";
import Link from "next/link";

interface Role {
  id: number;
  name: string;
  createdAt: string;
  updatedAt: string;
}

interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  roles: Role[];
}


export const revalidate = 60 * 60 * 24;

export default async function layout({ children }: PropsWithChildren) {
  const session = await auth();

  // If no session, don't make API calls
  if (!session) {
    return (
      <div className="h-screen">
        <div className="flex h-full flex-col items-center justify-center">
          <p className="text-2xl font-bold text-default-900">
            You are not logged in.{" "}
          </p>
          <Link
            href="/logout"
            className="mt-4 rounded-md bg-primary px-6 py-2 text-white transition-colors duration-200 ease-in-out hover:text-default-700"
          >
            Please login again.
          </Link>
        </div>
      </div>
    );
  }

  const logoutUser = async () => {
    "use server";

    await signOut({
      redirect: false,
    });
  };

  // Safely fetch data with error handling
  let tasks: PaginatedData<Task> | undefined;
  let notifications: PaginatedData<DatabaseNotification> | undefined;
  let userDetail: User | undefined;

  try {
    const fetchTasks = cache(() =>
      session?.vendor
        ? api.get<PaginatedData<Task>>(`vendors/${session.vendor.id}/tasks`)
        : api.get<PaginatedData<Task>>("tasks"),
    );
    tasks = await fetchTasks();
  } catch (error) {
    console.error("Error fetching tasks:", error);
    tasks = undefined;
  }

  try {
    const fetchNotifications = cache(() =>
      api.get<PaginatedData<DatabaseNotification>>("notifications"),
    );
    notifications = await fetchNotifications();
  } catch (error) {
    console.error("Error fetching notifications:", error);
    notifications = undefined;
  }

  try {
    if (session?.user?.id) {
      userDetail = await api.get(`/users/${session.user.id}`) as User;
    }
  } catch (error) {
    console.error("Error fetching user details:", error);
    userDetail = undefined;
  }

  return (
    <>
      <PortalHeader
        user={userDetail}
        branch={session?.branch}
        vendor={session?.vendor}
        notifications={notifications}
        logoutUser={logoutUser}
      />

      <PortalSidebar
        user={userDetail}
        tasks={tasks}
        logoutUser={logoutUser}
        branch={session?.branch}
        vendor={session?.vendor}
      />

      <div className="w-full lg:ps-64" id="application-body">
        {children}
      </div>

      <footer className="hide-in-print fixed bottom-0 mt-8 hidden w-full border-t border-default-200 p-6 lg:ps-64">
        <div className="container ms-2">
          <div className="grid items-center gap-6 lg:grid-cols-2">
            <p className="text-default-600">
              &copy; {new Date().getFullYear()} AppInApp. All rights reserved.
            </p>
          </div>
        </div>
      </footer>

      <BackToTop />
    </>
  );
}
