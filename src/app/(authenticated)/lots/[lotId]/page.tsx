import { api } from "@/lib/api";
import { cache } from "react";

const fetchLot = cache((lotId: string) => api.get<Lot>(`lots/${lotId}`));

export const generateMetadata = async ({
  params,
}: {
  params: { lotId: string };
}) => {
  const lot = await fetchLot(params.lotId);

  return {
    title: lot?.name,
  };
};



export default async function page({
  params: { lotId },
}: {
  params: { lotId: string };
}) {
  const lot = await fetchLot(lotId);

  return (
    <div className="p-4">
      <h1>{lot?.name}</h1>
      <p>{lot?.details}</p>
    </div>
  );
}
