"use client";

import AsyncSelect from "react-select/async";
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import { toast } from "react-hot-toast";

export default function AdminMessagesCreateForm({
  userId,
  sendMessage,
}: {
  userId: string;
  sendMessage: (
    data: Message & { title: string; receipients: string[] },
  ) => Promise<void>;
}) {
  const {
    setValue,
    watch,
    handleSubmit,
    register,
    reset,
    control,
    formState: { errors, isSubmitting },
  } = useForm<Message & { title: string; receipients: string[] }>({
    defaultValues: {
      receipients: [userId],
    },
  });

  const loadTemplates = async (inputValue: string) => {
    const response = await fetch(`/api/messages/templates?s=${inputValue}`);
    const data = await response.json();

    return data;
  };

  const createMessage: SubmitHandler<
    Message & { title: string; receipients: string[] }
  > = (message: Message & { title: string; receipients: string[] }) => {
    toast.promise(
      sendMessage(message),
      {
        loading: "Creating message...",
        success: "Message has been saved 👌",
        error: "Could not save message 🤯",
      },
      {
        position: "bottom-center",
      },
    );

    reset();
  };

  return (
    <div
      id="drawer-create-message-default"
      className="h-screen w-full overflow-y-auto bg-white p-4 transition-transform dark:bg-default-800"
      tabIndex={-1}
      aria-labelledby="drawer-label"
      aria-hidden="true"
    >
      <form onSubmit={handleSubmit(createMessage)}>
        <div className="space-y-4">
          {/* title */}
          <div>
            <label
              htmlFor="title"
              className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
            >
              Message subject
            </label>
            <input
              id="title"
              type="text"
              {...register("title")}
              className=".5 block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
              placeholder="Enter message title here"
            />
          </div>

          <div>
            <label
              htmlFor="name"
              className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
            >
              Template
            </label>
            <Controller
              name="templateId"
              control={control}
              render={({ field }) => (
                <AsyncSelect
                  isClearable
                  isSearchable
                  // cacheOptions
                  // @ts-ignore
                  loadOptions={loadTemplates}
                  getOptionLabel={(option: MessageTemplate) => option.name}
                  getOptionValue={(option) => option.id}
                  onChange={(option) => {
                    setValue("templateId", option?.id!);
                    setValue("details", option?.content!);
                  }}
                  placeholder="Select template"
                  required
                  classNames={{
                    control: () => "!py-[1px] !rounded-lg",
                    menu: () => "py-1 z-50 mb-5",
                  }}
                />
              )}
            />
          </div>

          <div>
            <label
              htmlFor="description"
              className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
            >
              Description
            </label>
            <textarea
              id="description"
              rows={4}
              {...register("details")}
              className=".5 block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
              placeholder="Enter message description here"
            />
          </div>

          <div className="bottom-0 left-0 flex w-full justify-center space-x-4 pb-4">
            <button
              type="submit"
              disabled={isSubmitting}
              className="w-full justify-center rounded-lg bg-primary px-5 py-3 text-center text-sm font-medium text-white hover:bg-default-800 focus:outline-none focus:ring-4 focus:ring-default-300 dark:bg-primary dark:hover:bg-primary dark:focus:ring-default-800"
            >
              Save message details
            </button>
          </div>
        </div>
      </form>
    </div>
  );
}
