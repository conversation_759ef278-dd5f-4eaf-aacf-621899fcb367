import { api } from "@/lib/api";
import MessageUserForm from "./form";
import { revalidatePath } from "next/cache";
import { auth } from "@/auth";
import { cache } from "react";

const fetchUser = cache((userId: string) => api.get<User>(`users/${userId}`));

export const generateMetadata = async ({
  params,
}: {
  params: { userId: string };
}) => {
  const user = await fetchUser(params.userId);

  return {
    title: `Message ${user?.firstName}`,
    description: "Message user",
    keywords: ["message", "user", "message user"],
  };
};

export default async function MessageUser({
  params,
}: {
  params: { userId: string };
}) {
  const session = await auth();

  const sendMessage = async (data: Message & { receipients: string[] }) => {
    "use server";

    data.userId = session?.user?.id!;

    await api.post("messages", data);

    revalidatePath(`/users/${params.userId}/message`);
  };

  return (
    <div className="w-full p-4">
      <MessageUserForm userId={params.userId} sendMessage={sendMessage} />
    </div>
  );
}
