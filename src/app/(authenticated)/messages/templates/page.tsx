import { api } from "@/lib/api";
import PaginatedTable from "@/components/table";
import Broadcast from "./create";
import { revalidatePath } from "next/cache";
import { auth } from "@/auth";

export const metadata = {
  title: "Message templates",
};

export default async function TemplateChat() {
  const session = await auth();
  const templates =
    await api.get<PaginatedData<MessageTemplate>>("message-templates");

  const storeTemplate = async (data: FormData) => {
    "use server";

    data.append("vendorId", session?.vendor?.id as string);
    data.append("branchId", session?.branch?.id as string);

    await api.post("message-templates", data);

    revalidatePath("/messages/templates");
  };

  return (
    <div className="flex h-screen flex-col">
      {templates && (
        <PaginatedTable<MessageTemplate>
          records={templates}
          columns={[
            {
              id: "name",
              title: "Sent",
            },
            {
              id: "content",
              title: "Content",
            },
          ]}
          title="Templates"
          path="/path"
          tools={
            <div className="flex items-center justify-between p-4">
              <p>
                Create and manage your templates. You can use them to send
                messages to your customers.
              </p>
              <Broadcast storeTemplate={storeTemplate} />
            </div>
          }
        />
      )}
    </div>
  );
}
