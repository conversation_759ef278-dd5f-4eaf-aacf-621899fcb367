import { auth } from "@/auth";
import PaginatedTable from "@/components/table";
import { api } from "@/lib/api";
import Link from "next/link";

export const metadata = {
  title: "My notifications",
};

export default async function page() {
  const session = await auth();

  const notifications =
    await api.get<PaginatedData<DatabaseNotification>>("notifications");

  return (
    <div className="py-4">
      {notifications && (
        <PaginatedTable
          records={notifications}
          columns={[
            {
              id: "title",
              title: "Title",
              render: (notification) => (
                <Link href={`/notifications/${notification.id}`}>
                  {notification.data.title}
                </Link>
              ),
            },
            {
              id: "title",
              title: "Title",
              render: (notification) => (
                <Link href={`/notifications/${notification.id}`}>
                  {notification.data.body}
                </Link>
              ),
            },
            {
              id: "id",
              title: "Actions",
              render: (notification) => (
                <p>
                  {notification.data.actions?.map((action: any) => (
                    <Link
                      key={action.title}
                      href={
                        action.screen === "OrderDetails"
                          ? `/orders/${action.args.orderId}`
                          : `/${action.screen}/${action.args.orderId}`
                      }
                      className="rounded-lg bg-primary px-5 py-2 text-white"
                    >
                      {action.label}
                    </Link>
                  ))}
                </p>
              ),
            },
          ]}
          path="/notifications"
          tools={
            <div className="mb-4 flex justify-between px-4">
              <p></p>
              <button className="rounded-lg bg-primary px-5 py-2 text-white">
                Mark all as read
              </button>
            </div>
          }
        />
      )}
    </div>
  );
}
