import { Metadata } from "next";
import { api } from "@/lib/api";

import OrdersTable from "./orders-table";
import { auth } from "@/auth";
import { revalidatePath } from "next/cache";

export const dynamic = "force-dynamic";
export const fetchCache = "force-no-store";

export const metadata: Metadata = {
  title: "Orders",
  description: "Order listing",
};

export default async function page({
  searchParams,
}: {
  searchParams: Record<string, string>;
}) {
  const session = await auth();

  const orders = session?.branch
    ? await api.get<PaginatedData<Order>>(
        `branches/${session?.branch?.id}/orders`,
        { per: 30, ...searchParams },
      )
    : await api.get<PaginatedData<Order>>("orders", searchParams);


  const deleteOrder = async (data: FormData) => {
    "use server";

    await api.destroy(data.get("id") as string, "orders");

    revalidatePath(`/orders`);
  };

  return (
    <div className="page-content p-5">

        <div className="grid grid-cols-1">

          {orders && <OrdersTable data={orders.data} meta={orders.meta} deleteOrder={deleteOrder} />}
        </div>
    </div>
  );
}
