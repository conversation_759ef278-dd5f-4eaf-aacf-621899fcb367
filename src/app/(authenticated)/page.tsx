import { auth } from "@/auth";
import { api, imagePath } from "@/lib/api";
import { Metadata } from "next";
import Link from "next/link";
import { format as formatDate } from "date-fns";
import dynamic from "next/dynamic";
import { cache } from "react";
import Image from "next/image";

const getAuth = cache(() => auth());

export const generateMetadata = async (): Promise<Metadata> => {
  const session = await getAuth();

  return {
    title: `Hello ${session?.user?.firstName}`,
    description: "Dashboard",
  };
};

export default async function page() {
  const session = await getAuth();

  if (!session) {
    return (
      <div className="h-screen">
        <div className="flex h-full flex-col items-center justify-center">
          <p className="text-2xl font-bold text-default-900">
            You are not logged in.{" "}
          </p>
          <Link
            href="/logout"
            className="mt-4 rounded-md bg-primary px-6 py-2 text-white transition-colors duration-200 ease-in-out hover:text-default-700"
          >
            Please login again.
          </Link>
        </div>
      </div>
    );
  }

  // Safely fetch data with error handling
  let payments: PaginatedData<Payment> | undefined;
  let vendors: PaginatedData<Vendor> | undefined;
  let orders: PaginatedData<Order> | undefined;
  let staff: PaginatedData<User> | undefined;
  let users: PaginatedData<User> | undefined;
  let products: PaginatedData<Product> | undefined;

  try {
    payments = session?.branch
      ? await api.get<PaginatedData<Payment>>(
          `branches/${session?.branch?.id}/payments`,
          { per: 1 },
        )
      : await api.get<PaginatedData<Payment>>("payments", { per: 1 });
  } catch (error) {
    console.error("Error fetching payments:", error);
    payments = undefined;
  }

  try {
    vendors = await api.get<PaginatedData<Vendor>>("vendors", { per: 1 });
  } catch (error) {
    console.error("Error fetching vendors:", error);
    vendors = undefined;
  }

  try {
    orders = session?.branch
      ? await api.get<PaginatedData<Order>>(
          `branches/${session?.branch?.id}/orders`,
          { per: 5 },
        )
      : await api.get<PaginatedData<Order>>("orders", { per: 5 });
  } catch (error) {
    console.error("Error fetching orders:", error);
    orders = undefined;
  }

  try {
    staff = session?.branch
      ? await api.get<PaginatedData<Order>>(
          `branches/${session?.branch?.id}/staff`,
          { per: 5 },
        )
      : await api.get<PaginatedData<User>>("users", { per: 1 });
  } catch (error) {
    console.error("Error fetching staff:", error);
    staff = undefined;
  }

  try {
    users = session?.branch
      ? await api.get<PaginatedData<Order>>(
          `branches/${session?.branch?.id}/customers`,
          { per: 5 },
        )
      : await api.get<PaginatedData<User>>("users", { per: 1 });
  } catch (error) {
    console.error("Error fetching users:", error);
    users = undefined;
  }

  try {
    products = session?.branch
      ? await api.get<PaginatedData<Order>>(
          `branches/${session?.branch?.id}/products`,
          { per: 5 },
        )
      : await api.get<PaginatedData<Product>>("products", {
          per: 1,
        });
  } catch (error) {
    console.error("Error fetching products:", error);
    products = undefined;
  }

  const HomeChart = dynamic(() => import("./chart"), { ssr: false });

  return (
    <div className="page-content space-y-6 p-5">
      {session.branch && (
        <div className="flex w-full items-center justify-between">
          <h4 className="text-xl font-medium text-black">
            Welcome to {session.vendor.name} ({session?.branch?.name}),
          </h4>

          <ol
            aria-label="Breadcrumb"
            className="hidden min-w-0 items-center gap-2 whitespace-nowrap md:flex"
          >
            <li className="text-sm">
              <Link
                className="flex items-center gap-2 align-middle text-default-800 transition-all hover:text-default-500"
                href="/"
              >
                Admin
                <svg
                  stroke="currentColor"
                  fill="none"
                  strokeWidth="2"
                  viewBox="0 0 24 24"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  height="16"
                  width="16"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path d="m9 18 6-6-6-6"></path>
                </svg>
              </Link>
            </li>

            <li
              aria-current="page"
              className="truncate text-sm font-medium text-primary hover:text-default-500"
            >
              Dashboard
            </li>
          </ol>
        </div>
      )}

      <div className="grid grid-cols-2 gap-2 lg:grid-cols-5 lg:gap-5">
        <Link
          href="/payments"
          className="group flex flex-col justify-between overflow-hidden rounded-lg border border-default-200 bg-white p-4 text-center transition-all duration-300 hover:bg-primary"
        >
          <h4 className="mb-2 text-2xl font-semibold text-primary group-hover:text-white">
            {payments?.meta?.total}
          </h4>

          <h6 className="mb-4 text-lg font-medium text-default-950 group-hover:text-white">
            Total Revenue
          </h6>

          <p className="text-sm font-medium text-green-500">10% Increase</p>
        </Link>

        <Link
          href="/orders"
          className="group flex flex-col justify-between overflow-hidden rounded-lg border border-default-200 bg-white p-4 text-center transition-all duration-300 hover:bg-primary"
        >
          <h4 className="mb-2 text-2xl font-semibold text-primary group-hover:text-white">
            {orders?.meta?.total}
          </h4>
          <h6 className="mb-4 text-lg font-medium text-default-950 group-hover:text-white">
            New Orders
          </h6>
          <p className="text-sm font-medium text-green-500">50% Increase</p>
        </Link>

        {session.branch ? (
          <Link
            href={session.branch ? `/users?role=staff` : `/users`}
            className="group flex flex-col justify-between overflow-hidden rounded-lg border border-default-200 bg-white p-4 text-center transition-all duration-300 hover:bg-primary"
          >
            <h4 className="mb-2 text-2xl font-semibold text-primary group-hover:text-white">
              {staff?.meta?.total}
            </h4>
            <h6 className="mb-4 text-lg font-medium text-default-950 group-hover:text-white">
              Staff
            </h6>
            <p className="text-sm font-medium text-red-500">5% Decrease</p>
          </Link>
        ) : (
          <Link
            href="/vendors"
            className="group flex flex-col justify-between overflow-hidden rounded-lg border border-default-200 bg-white p-4 text-center transition-all duration-300 hover:bg-primary"
          >
            <h4 className="mb-2 text-2xl font-semibold text-primary group-hover:text-white">
              {vendors?.meta?.total}
            </h4>
            <h6 className="mb-4 text-lg font-medium text-default-950 group-hover:text-white">
              Vendors
            </h6>
            <p className="text-sm font-medium text-red-500">5% Decrease</p>
          </Link>
        )}

        <Link
          href={session.branch ? `/users?role=customer` : `/users`}
          className="group flex flex-col justify-between overflow-hidden rounded-lg border border-default-200 bg-white p-4 text-center transition-all duration-300 hover:bg-primary"
        >
          <h4 className="mb-2 text-2xl font-semibold text-primary group-hover:text-white">
            {users?.meta?.total}
          </h4>

          <h6 className="mb-4 text-lg font-medium text-default-950 group-hover:text-white">
            {session.branch ? "Customers" : "Users"}
          </h6>

          <p className="text-sm font-medium text-green-500">34% Increase</p>
        </Link>

        <Link
          href="/products"
          className="group flex flex-col justify-between overflow-hidden rounded-lg border border-default-200 bg-white p-4 text-center transition-all duration-300 hover:bg-primary"
        >
          <h4 className="mb-2 text-2xl font-semibold text-primary group-hover:text-white">
            {products?.meta?.total}
          </h4>

          <h6 className="mb-4 text-lg font-medium text-default-950 group-hover:text-white">
            Products
          </h6>

          <p className="text-sm font-medium text-green-500">48% Increase</p>
        </Link>
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-12">
        <div className="col-span-8 rounded-lg border border-default-200 bg-white p-5  shadow-sm">
          <HomeChart />
        </div>

        <div className="col-span-4 rounded-lg border border-default-200 bg-white shadow-sm">
          <div className="overflow-hidden p-6 ">
            <div className="flex flex-wrap items-center gap-4 sm:justify-between lg:flex-nowrap">
              <h2 className="text-xl font-semibold text-default-800">
                Recent Orders
              </h2>
            </div>
          </div>

          <div className="relative overflow-x-auto">
            <div className="inline-block min-w-full align-middle">
              <div className="overflow-hidden">
                <table className="w-full divide-y divide-default-200">
                  <thead className="bg-default-100">
                    <tr className="text-start">
                      <th className="whitespace-nowrap px-6 py-3 text-start text-sm font-medium text-default-800">
                        Customer
                      </th>
                      <th className="whitespace-nowrap px-6 py-3 text-start text-sm font-medium text-default-800">
                        Status
                      </th>
                      <th className="whitespace-nowrap px-6 py-3 text-start text-sm font-medium text-default-800">
                        Total
                      </th>
                      <th></th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-default-200">
                    {orders?.data?.map((order) => (
                      <tr key={order.id}>
                        <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-default-800">
                          <Link href={`/customers/${order.customer.id}`}
                           className="flex items-center gap-4">
                            <div className="shrink">
                              <div className="h-18 w-18">
                                <Image
                                  src={imagePath(order?.customer?.avatar?.url)}
                                  className="h-full max-w-full size-12 rounded-full"
                                  width="72"
                                  height="72"
                                  alt={order?.customer?.firstName}
                                />
                              </div>
                            </div>
                            <div className="grow">
                              <p className="mb-1 text-sm text-default-500">
                                {order?.customer?.name}
                              </p>
                              <div className="flex items-center gap-2">
                                <p>
                                  {formatDate(
                                    new Date(order.createdAt),
                                    "dd MMM yyyy",
                                  )}
                                </p>
                              </div>
                            </div>
                          </Link>
                        </td>
                        <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-default-500 hover:text-default-500">
                          <a href="/yum_r/admin/orders/c0e4f7">
                            {order.status}
                          </a>
                        </td>
                        <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-default-500">
                          {order.total}
                        </td>
                        <td>
                          <Link href={`/orders/${order.id}`}>
                          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="size-6">
  <path strokeLinecap="round" strokeLinejoin="round" d="M12 6.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5Z" />
</svg>

                          </Link> 
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
