import Image from "next/image";

export default function PaymentShow() {
  return (
    <div className="container-fluid">
      <div className="osen-content-inner">
        <div className="osen-content-body">
          <div className="block-head">
            <div className="block-between g-3">
              <div className="block-head-content">
                <h3 className="block-title page-title">
                  Invoice{" "}
                  <strong className="small text-default-600">#746F5K2</strong>
                </h3>
                <div className="block-des text-soft">
                  <ul className="list-inline">
                    <li>
                      Created At:{" "}
                      <span className="text-base">18 Dec, 2019 01:02 PM</span>
                    </li>
                  </ul>
                </div>
              </div>
              <div className="block-head-content">
                <a
                  href="/customer-invoice-list"
                  className="btn-outline-light d-none d-sm-inline-flex flex items-center justify-center bg-white px-4 py-3"
                >
                  <em className="icon ni ni-arrow-left" />
                  <span>Back</span>
                </a>
                <a
                  href="/customer-invoice-list"
                  className="btn-icon btn-outline-light d-inline-flex d-sm-none flex items-center justify-center bg-white px-4 py-3"
                >
                  <em className="icon ni ni-arrow-left" />
                </a>
              </div>
            </div>
          </div>
          <div className="block">
            <div className="invoice">
              <div className="invoice-action">
                <a
                  className="btn-icon btn-lg btn-white btn-dim btn-outline-primary flex items-center justify-center px-4 py-3"
                  href="/customer-invoice-print"
                  target="_blank"
                >
                  <em className="icon ni ni-printer-fill" />
                </a>
              </div>
              <div className="invoice-wrap">
                <div className="invoice-brand text-center">
                  <Image
                    src="/images/logo-dark.png"
                    alt="App In App"
                    width={100}
                    height={50}
                  />
                </div>
                <div className="invoice-head">
                  <div className="invoice-contact">
                    <span className="overline-title">Invoice To</span>
                    <div className="invoice-contact-info">
                      <h4 className="text-lg font-bold">Gregory Ander son</h4>
                      <ul className="list-plain">
                        <li>
                          <em className="icon ni ni-map-pin-fill" />
                          <span>
                            House #65, 4328 Marion Street,Newbury, VT 05051
                          </span>
                        </li>
                        <li>
                          <em className="icon ni ni-call-fill" />
                          <span>+012 8764 556</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                  <div className="invoice-desc">
                    <h3 className="text-lg font-bold">Invoice</h3>
                    <ul className="list-plain">
                      <li className="invoice-id">
                        <span>Invoice ID</span>:<span>66K5W3</span>
                      </li>
                      <li className="invoice-date">
                        <span>Date</span>:<span>26 Jan, 2020</span>
                      </li>
                    </ul>
                  </div>
                </div>
                <div className="invoice-bills">
                  <div className="table-responsive">
                    <table className="table-striped table">
                      <thead>
                        <tr>
                          <th>Item ID</th>
                          <th>Description</th>
                          <th>Price</th>
                          <th>Qty</th>
                          <th>Amount</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td>24108054</td>
                          <td>
                            App In App - Conceptual App Dashboard - Regular
                            License
                          </td>
                          <td>$40.00</td>
                          <td>5</td>
                          <td>$200.00</td>
                        </tr>
                      </tbody>
                      <tfoot>
                        <tr>
                          <td colSpan={2}></td>
                          <td colSpan={2}>Subtotal</td>
                          <td>$435.00</td>
                        </tr>
                        <tr>
                          <td colSpan={2}></td>
                          <td colSpan={2}>Processing fee</td>
                          <td>$10.00</td>
                        </tr>
                        <tr>
                          <td colSpan={2}></td>
                          <td colSpan={2}>TAX</td>
                          <td>$43.50</td>
                        </tr>
                        <tr>
                          <td colSpan={2}></td>
                          <td colSpan={2}>Grand Total</td>
                          <td>$478.50</td>
                        </tr>
                      </tfoot>
                    </table>
                    <div className="osen-notes ff-italic fs-12px text-soft">
                      {" "}
                      Invoice was created on a computer and is valid without the
                      signature and seals.{" "}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
