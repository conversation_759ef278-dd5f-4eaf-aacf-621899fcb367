"use client";

import { useForm } from "react-hook-form";
import toast from "react-hot-toast";

export default function PaymentForm({
  storePayment,
}: {
  storePayment: (data: FormData) => Promise<any>;
}) {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<Payment>({
    defaultValues: {
      amount: 0,
      userId: "",
    },
  });

  const onSubmit = async (payment: Payment) => {
    const data = new FormData();

    data.append("amount", payment.amount.toString());
    data.append("userId", payment.userId);

    toast.promise(storePayment(data), {
      loading: "Saving payment...",
      success: "Payment saved",
      error: "Failed to save payment",
    });
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <div>
        <label htmlFor="amount">Amount</label>
        <input
          type="number"
          id="amount"
          {...register("amount", { required: true })}
          className="w-full rounded-md border border-default-300 p-2 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        {errors.amount && <p>This field is required</p>}
      </div>
      <div>
        <label htmlFor="userId">Customer</label>
        <input
          type="text"
          id="userId"
          {...register("userId", { required: true })}
          className="w-full rounded-md border border-default-300 p-2 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        {errors.userId && <p>This field is required</p>}
      </div>

      <div className="flex justify-end">
        <button
          type="submit"
          className="w-60 rounded-lg border border-default-300 bg-primary p-2 text-white focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          Save
        </button>
      </div>
    </form>
  );
}
