import Link from "next/link";
import { api } from "@/lib/api";
import { Metadata } from "next";

import PaginatedTable from "@/components/table";
import { formatDate } from "@/lib/date";
import PaymentActions from "./actions";

export const metadata: Metadata = {
  title: "Payments",
};

export default async function PaymentIndex({
  searchParams,
}: {
  searchParams: Record<string, string>;
}) {
  const payments = await api.get<PaginatedData<Payment>>(
    "payments",
    searchParams,
  );

  const storePayment = async (data: Payment) => {
    "use server";

    await api.post("payments", data);
  };

  return (
    <div className="py-4">
      {payments && (
        <PaginatedTable<Payment>
          records={payments}
          columns={[
            {
              id: "name",
              title: "Customer",
              render: (payment: Payment) => (
                <Link href={`/users/${payment.userId}`}>
                  {payment.customer?.name}
                </Link>
              ),
            },
            {
              id: "amount",
              title: "Amount",
              render: (payment: Payment) => (
                <span className="amount">KES {payment.amount}</span>
              ),
            },
            { id: "ref", title: "Reference" },
            { id: "receipt", title: "Transaction ID" },
            { id: "status", title: "Status" },
            {
              id: "createdAt",
              title: "Date",
              render: (payment) => (
                <p>
                  {formatDate(payment.createdAt, "EEEE, MMM dd, yyyy hh:mma")}
                </p>
              ),
            },
            {
              id: "actions",
              title: "Actions",
              render: (payment) => <PaymentActions defaultValues={payment} />,
            },
          ]}
          title="Payments"
          path="payments"
          tools={
            <div className="dark:bg-default-9006 mb-4 flex items-center justify-between bg-white px-4">
              <div className="">
                <button
                  id="dropdownActionButton"
                  data-dropdown-toggle="dropdownAction"
                  className="inline-flex items-center rounded-lg border border-default-300 bg-white px-3 py-1.5 text-sm font-medium text-default-500 hover:bg-default-100 focus:outline-none focus:ring-4 focus:ring-default-200 dark:border-default-600 dark:bg-default-800 dark:text-default-400 dark:hover:border-default-600 dark:hover:bg-default-700 dark:focus:ring-default-700"
                  type="button"
                >
                  <span className="sr-only">Action button</span>
                  Action
                  <svg
                    className="ml-2 h-3 w-3"
                    aria-hidden="true"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M19 9l-7 7-7-7"
                    ></path>
                  </svg>
                </button>
                <div
                  id="dropdownAction"
                  className="z-10 hidden w-44 divide-y divide-default-100 rounded-lg bg-white shadow dark:divide-default-600 dark:bg-default-700"
                >
                  <ul
                    className="py-1 text-sm text-default-700 dark:text-default-200"
                    aria-labelledby="dropdownActionButton"
                  >
                    <li>
                      <a
                        href="#"
                        className="block px-4 py-2 hover:bg-default-100 dark:hover:bg-default-600 dark:hover:text-white"
                      >
                        Reward
                      </a>
                    </li>
                    <li>
                      <a
                        href="#"
                        className="block px-4 py-2 hover:bg-default-100 dark:hover:bg-default-600 dark:hover:text-white"
                      >
                        Promote
                      </a>
                    </li>
                    <li>
                      <a
                        href="#"
                        className="block px-4 py-2 hover:bg-default-100 dark:hover:bg-default-600 dark:hover:text-white"
                      >
                        Activate account
                      </a>
                    </li>
                  </ul>
                  <div className="py-1">
                    <a
                      href="#"
                      className="block px-4 py-2 text-sm text-default-700 hover:bg-default-100 dark:text-default-200 dark:hover:bg-default-600 dark:hover:text-white"
                    >
                      Delete User
                    </a>
                  </div>
                </div>
              </div>

              <div className="flex flex-row items-center justify-between space-x-2">
                <div className="flex flex-row justify-between">
                  <label htmlFor="table-search" className="sr-only">
                    Search
                  </label>

                  <div className="relative">
                    <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                      <svg
                        className="h-5 w-5 text-default-500 dark:text-default-400"
                        aria-hidden="true"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          fillRule="evenodd"
                          d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                          clipRule="evenodd"
                        ></path>
                      </svg>
                    </div>
                    <input
                      type="text"
                      id="table-search-users"
                      className="block w-80  rounded-lg border border-default-300 bg-default-50 p-3 pl-10 text-sm text-default-900 focus:border-blue-500 focus:ring-blue-500 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
                      placeholder="Search for users"
                    />
                  </div>

                  {/* {products && <CreateOrder
								products={products?.data}
								storeOrder={createRecord}
							/>} */}
                </div>
                <Link
                  href="/payments/create"
                  className="hover:bg-default-dark rounded-md bg-primary px-4 py-2 text-white"
                >
                  New Payment
                </Link>
              </div>
            </div>
          }
        />
      )}
    </div>
  );
}
