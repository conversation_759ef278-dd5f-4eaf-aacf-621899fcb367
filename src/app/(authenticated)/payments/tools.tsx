"use client";

import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { FormEvent, useEffect, useState } from "react";

export default function PaymentTools() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  let query: Record<string, string> = {};

  searchParams.forEach((value: string, key: string) => {
    query[key] = value;
  });

  const [search, setSearch] = useState<string | null>(searchParams.get("s"));
  const [searching, setSearching] = useState(searchParams.has("s"));

  const searchPayments = (e: FormEvent) => {
    e.preventDefault();

    router.push(pathname + "?" + new URLSearchParams(query).toString());
  };

  useEffect(() => {
    query = { ...query, s: search || "" };
  }, [search]);

  return (
    <>
      <div className="card-tools me-n1">
        <ul className="btn-toolbar">
          <li>
            <a
              href="#"
              onClick={() => setSearching(!searching)}
              className="btn-icon search-toggle toggle-search flex items-center justify-center px-4 py-3"
              data-target="search"
            >
              <em className="icon ni ni-search" />
            </a>
          </li>
          <li className="btn-toolbar-sep"></li>
          <li>
            <div className="dropdown">
              <a
                href="#"
                className="btn-icon dropdown-toggle flex items-center justify-center bg-default-500 px-4 py-3 text-white"
                data-bs-toggle="dropdown"
              >
                <em className="icon ni ni-setting" />
              </a>
              <div className="dropdown-menu dropdown-menu-end dropdown-menu-xs">
                <ul className="link-check">
                  <li>
                    <span>Show</span>
                  </li>
                  <li className="active">
                    <a href="#">10</a>
                  </li>
                  <li>
                    <a href="#">20</a>
                  </li>
                  <li>
                    <a href="#">50</a>
                  </li>
                </ul>
                <ul className="link-check">
                  <li>
                    <span>Order</span>
                  </li>
                  <li className="active">
                    <a href="#">DESC</a>
                  </li>
                  <li>
                    <a href="#">ASC</a>
                  </li>
                </ul>
                <ul className="link-check">
                  <li>
                    <span>Density</span>
                  </li>
                  <li className="active">
                    <a href="#">Regular</a>
                  </li>
                  <li>
                    <a href="#">Compact</a>
                  </li>
                </ul>
              </div>
            </div>
          </li>
        </ul>
      </div>

      <form
        className={"card-search search-wrap" + (searching ? " active" : "")}
        data-search="search"
        onSubmit={searchPayments}
      >
        <div className="search-content">
          <a
            href="#"
            onClick={() => setSearching(!searching)}
            className="search-back btn-icon toggle-search flex items-center justify-center px-4 py-3"
            data-target="search"
          >
            <em className="icon ni ni-arrow-left" />
          </a>
          <input
            type="text"
            onChange={(e) => setSearch(e.target.value)}
            className="form-control-sm form-focus-none block w-full rounded-lg border border-default-300 border-transparent bg-default-50 p-3 text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-600 dark:focus:ring-default-600 sm:text-sm"
            placeholder="Quick search by order id"
          />
          <button
            type="submit"
            className="search-submit btn-icon flex items-center justify-center px-4 py-3"
          >
            <em className="icon ni ni-search" />
          </button>
        </div>
      </form>
    </>
  );
}
