import { api } from "@/lib/api";
import BillingCredentials from "./form";
import { auth } from "@/auth";

export const metadata = {
  title: "Billing Settings",
  description: "Settings",
};

export default async function page() {
  const session = await auth();

  const settings = await api.get<PaginatedData<Setting>>(`settings`, {
    ...(session?.branch && { branch: session.branch.id }),
    name: "billing",
  });

  const defaultValues = settings?.data[0] || {
    id: "",
    branchId: session?.branch?.id,
    name: "billing",
    options: {
      defaultCurrency: {
        symbol: "Ksh",
        name: "Kenyan Shilling",
        symbol_native: "Ksh",
        decimal_digits: 2,
        rounding: 0,
        code: "KES",
        name_plural: "Kenyan shillings",
      },
    },
    createdAt: "",
    updatedAt: "",
    branch: null,
  };

  const updateSettings = async (payload: Setting) => {
    "use server";

    await api.post("settings", payload);
  };

  return (
    <>
      <div className="pt-4">
        <p className="font- text-slate-600">
          Billing & currency settings
        </p>
      </div>

      <hr className="mb-8 mt-4" />
      <p className="py-2 text-xl font-semibold">Select your currencies below</p>

      <BillingCredentials
        defaultValues={defaultValues}
        updateSettings={updateSettings}
      />
    </>
  );
}
