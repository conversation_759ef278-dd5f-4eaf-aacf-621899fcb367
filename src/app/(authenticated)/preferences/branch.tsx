"use client";

import { imagePath } from "@/lib/api";
import Image from "next/image";
import { useState } from "react";
import { useForm, Controller } from "react-hook-form";
import toast from "react-hot-toast";
import PhoneInput from "react-phone-number-input";

export default function BranchForm({
  defaultValues,
  storeBranch,
}: {
  defaultValues: Branch;
  storeBranch: (data: FormData) => Promise<any>;
}) {
  const [preview, setPreview] = useState<string | null>(
    imagePath(defaultValues.image?.url),
  );
  const {
    register,
    handleSubmit,
    watch,
    setValue,
    control,
    formState: { errors },
  } = useForm<Branch & { upload: File }>({
    defaultValues,
  });

  const branch = watch();

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files) {
      setValue("upload", files[0]);

      const reader = new FileReader();
      reader.onload = () => {
        setPreview(reader.result as string);
      };

      reader.readAsDataURL(files[0]);
    }
  };

  const onSubmit = async (branch: Branch & { upload: File }) => {
    const data = new FormData();

    data.append("name", branch.name);
    data.append("details", branch.details);
    data.append("phone", branch.phone);
    data.append("email", branch.email);

    if (branch.upload) {
      data.append("image", branch.upload);
    }

    toast.promise(storeBranch(data), {
      loading: "Saving branch...",
      success: "Branch saved",
      error: "Failed to save branch",
    });
  };

  return (
    <form
      encType="multipart/form-data"
      onSubmit={handleSubmit(onSubmit)}
      className="space-y-4"
    >
      <div>
        <label htmlFor="name" className="my-2 block">
          Name
        </label>
        <input
          type="text"
          id="name"
          {...register("name", { required: true })}
          className="w-full rounded-lg border border-default-300 p-3 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-default-500"
        />
        {errors.name && <p>This field is required</p>}
      </div>

      <div>
        <label htmlFor="phone" className="my-2 block">
          Phone
        </label>
        <div className="form-input relative w-full max-w-full rounded-lg border border-default-200 bg-white px-4 py-3 dark:bg-default-50 [&>div>input]:focus:ring-0">
          <Controller
            name="phone"
            control={control}
            render={({ field: { onChange, value } }) => (
              <PhoneInput
                placeholder="Enter phone number"
                value={value}
                onChange={onChange}
                defaultCountry="KE"
              />
            )}
          />
        </div>
      </div>

      <div>
        <label htmlFor="email" className="my-2 block">
          Email
        </label>
        <input
          type="email"
          id="email"
          {...register("email", { required: true })}
          className="w-full rounded-lg border border-default-300 p-3 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-default-500"
        />
      </div>

      <div className="flex gap-3">
      <div className="flex-1">
        <label htmlFor="details" className="mb-2 block">
          Details
        </label>
        <textarea
          id="details"
          {...register("details", { required: true })}
          className="h-72 w-full rounded-lg border border-default-300 p-3 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-default-500"
        />
        {errors.details && <p>This field is required</p>}
      </div>

      {/* Image upload */}
      <div className="w-full lg:w-1/3">
        <label htmlFor="image" className="mb-2 block">
          Image
        </label>

        <label className="flex w-full items-center justify-center rounded-lg border border-dashed border-primary">
          <input
            type="file"
            id="image"
            onChange={handleImageUpload}
            className="hidden"
          />

          {preview ? (
            <Image
              src={preview}
              alt="Branch"
              className="h-72 w-full rounded-lg object-cover"
            />
          ) : (
            <p className="text-center text-primary-600">Upload an image</p>
          )}
        </label>
      </div>
      </div>

      <div className="flex justify-end pb-5">
        <button
          type="submit"
          className="rounded-lg bg-primary px-5 py-3 text-white focus:border-transparent focus:outline-none focus:ring-2 focus:ring-default-500 w-full lg:w-96"
        >
          Save branch details
        </button>
      </div>
    </form>
  );
}
