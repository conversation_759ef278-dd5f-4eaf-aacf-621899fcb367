"use client";

import toast from "react-hot-toast";

export default function ColorPicker({
  setting,
  color,
  updateSettings,
}: {
  setting: string;
  color: string;
  updateSettings: (newColor: string, setting: string) => Promise<void>;
}) {
  const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    toast.promise(updateSettings(e.target.value, setting), {
      loading: "Updating color...",
      success: "Updated color!",
      error: "Failed to update color. Please try again.",
    });
  };

  return (
    <label
      htmlFor={setting}
      className="relative inline-flex cursor-pointer items-center"
    >
      <input
        type="color"
        id={setting}
        className={"peer size-10 rounded-full " + `bg-[${color}]`}
        onChange={onChange}
        defaultValue={color}
      />
    </label>
  );
}
