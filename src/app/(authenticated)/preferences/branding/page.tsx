import { auth } from "@/auth";
import ColorPicker from "./color";
import { api } from "@/lib/api";
import { revalidatePath } from "next/cache";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Branding settings",
};

export default async function page() {
  const session = await auth();

  const settings = await api.get<PaginatedData<Setting>>(`settings`, {
    ...(session?.branch && { branch: session.branch.id }),
    name: "branding",
  });

  const labels: Record<string, string> = {
    primaryColor: "Primary color",
    secondaryColor: "Secondary color",
    textColor: "Text color",
  };

  const defaultValues = settings?.data[0] || {
    id: "",
    branchId: session?.branch?.id,
    name: "branding",
    options: {
      primaryColor: "#FF0000",
      secondaryColor: "#00FF00",
      textColor: "#0000FF",
    },
    createdAt: "",
    updatedAt: "",
    branch: null,
  };

  const updateSettings = async (newColor: string, key: string) => {
    "use server";

    const payload = {
      ...defaultValues,
      options: {
        ...defaultValues.options,
        [key]: newColor,
      },
    };

    await api.post("settings", payload);

    revalidatePath("/preferences/branding");
  };

  return (
    <>
      {Object.keys(defaultValues.options).map((key) => (
        <div className="grid border-b py-6 sm:grid-cols-2" key={key}>
          <div className="">
            <h2 className="text-lg font-semibold leading-4 text-slate-700">
              {labels[key]}
            </h2>
            <p className="font- text-slate-600">
              Lorem ipsum dolor, Alias eligendi laboriosam magni reiciendis
              neque.
            </p>
          </div>
          <div className="mt-4 flex items-center sm:justify-end">
            <div className="flex flex-col gap-3">
              <ColorPicker
                setting={key}
                color={defaultValues.options[key]}
                updateSettings={updateSettings}
              />
            </div>
          </div>
        </div>
      ))}
    </>
  );
}
