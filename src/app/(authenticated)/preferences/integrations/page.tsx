import { Metadata } from "next";
import Switch from "./switch";
import { revalidatePath } from "next/cache";
import { api } from "@/lib/api";
import { auth } from "@/auth";

export const dynamic = "force-dynamic";
export const fetchCache = "force-no-store";

export const metadata: Metadata = {
  title: "Integration Settings",
};

export default async function page() {
  const session = await auth();
  const integrationSettings = await api.get<PaginatedData<Setting>>(
    `settings`,
    {
      ...(session?.branch && { branch: session.branch.id }),
      name: "integrations",
    },
  );

  const setting: Partial<Setting> = integrationSettings?.data[0] || {
    id: "",
    branchId: session?.branch?.id,
    name: "integrations",
    options: {},
  };

  const integrations: Record<string, string> = {
    hotelplus: "HotelPlus",
    kaa: "Kenya Airports Authority",
    kplc: "Kenya Power and Lighting Company",
    mpesa: "Lipa Na M-Pesa",
    pesaflow: "PesaFlow",
    paystack: "PayStack",
    cellulant: "Cellulant",
  };

  const storeIntegrationSettings = async (newValue: boolean, key: string) => {
    "use server";

    const payload = {
      branchId: session?.branch?.id,
      name: "integrations",
      options: { ...setting.options, [key]: newValue },
    };

    await api.post("settings", payload);

    revalidatePath("/preferences/integrations");
  };

  return (
    <div>
      <div className="border-b pb-8 pt-4">
        <p className="font- text-slate-600">
          Enable or disable external API integrations
        </p>
      </div>

      {Object.keys(integrations).map((integration) => (
        <div key={integration} className="grid border-b py-6 sm:grid-cols-2">
          <div className="">
            <h2 className="text-lg font-semibold capitalize leading-4 text-slate-700">
              {integrations[integration]}
            </h2>
            <p className="font- text-slate-600">
              Enable or disable {integration} integration
            </p>
          </div>

          <div className="mt-4 flex items-center sm:justify-end">
            <div className="flex flex-col gap-3">
              <Switch
                setting={integration}
                checked={
                  setting.options ? setting?.options[integration] : false
                }
                updateSettings={storeIntegrationSettings}
              />
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
