"use client";

import toast from "react-hot-toast";

export default function Switch({
  setting,
  checked,
  updateSettings,
}: {
  setting: string;
  checked: boolean;
  updateSettings: (value: boolean, setting: string) => Promise<void>;
}) {
  const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    toast.promise(updateSettings(e.target.checked, setting), {
      loading: "Updating settings...",
      error: "Could not update settings",
      success: "Saved settings succesfuly",
    });
  };

  return (
    <label
      htmlFor={setting}
      className="relative inline-flex cursor-pointer items-center"
    >
      <input
        type="checkbox"
        id={setting}
        name={setting}
        className="peer sr-only"
        onChange={onChange}
        defaultChecked={checked}
      />

      <div className="peer h-6 w-11 rounded-full bg-default-200 after:absolute after:left-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-default-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-primary peer-checked:after:translate-x-full peer-checked:after:border-white peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:border-default-600 dark:bg-default-700 dark:peer-focus:ring-primary" />
    </label>
  );
}
