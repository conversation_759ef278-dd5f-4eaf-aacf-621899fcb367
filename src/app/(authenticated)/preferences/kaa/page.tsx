import { api } from "@/lib/api";
import KaaCredentials from "./form";
import { auth } from "@/auth";

export const metadata = {
  title: "KAA Settings",
  description: "Settings",
};

export default async function page() {
  const session = await auth();

  const settings = await api.get<PaginatedData<Setting>>(`settings`, {
    ...(session?.branch && { branch: session.branch.id }),
    name: "kaa",
  });

  const defaultValues = settings?.data[0] || {
    id: "",
    branchId: session?.branch?.id,
    name: "kaa",
    options: {},
    createdAt: "",
    updatedAt: "",
    branch: null,
  };

  const updateSettings = async (payload: Setting) => {
    "use server";

    await api.post("settings", payload);
  };

  return (
    <>
      <div className="pt-4">
        <p className="font- text-slate-600">
          Kenya Airports Authority (KAA) settings
        </p>
      </div>

      <hr className="mb-8 mt-4" />
      <p className="py-2 text-xl font-semibold">Enter KAA Config</p>

      <KaaCredentials
        defaultValues={defaultValues}
        updateSettings={updateSettings}
      />
    </>
  );
}
