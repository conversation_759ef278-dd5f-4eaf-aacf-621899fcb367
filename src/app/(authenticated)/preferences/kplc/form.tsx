"use client";

import { useForm } from "react-hook-form";
import toast from "react-hot-toast";

export default function KplcCredentials({
  defaultValues,
  updateSettings,
}: {
  defaultValues: Setting;
  updateSettings: (data: Setting) => Promise<void>;
}) {
  const { register, handleSubmit } = useForm<Setting>({
    defaultValues,
  });

  const onSubmit = async (payload: Setting) => {
    toast.promise(updateSettings(payload), {
      loading: "Updating settings...",
      error: "Could not update settings",
      success: "Saved settings succesfuly",
    });
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <div className="flex items-center">
        <div className="flex flex-col space-y-2 sm:flex-row sm:space-x-3 sm:space-y-0">
          <label htmlFor="username">
            <span className="text-sm text-default-500">
              Username (email or phone)
            </span>
            <div className="relative flex overflow-hidden rounded-md border-2 transition focus-within:border-primary">
              <input
                type="text"
                id="username"
                {...register("options.username")}
                className="w-full flex-shrink appearance-none rounded-lg border-default-300 bg-white py-3 text-base text-default-700 placeholder-default-400 focus:outline-none"
                placeholder="Email or phone"
              />
            </div>
          </label>
          <label htmlFor="login-password">
            <span className="text-sm text-default-500">Account password</span>
            <div className="relative flex overflow-hidden rounded-md border-2 transition focus-within:border-primary">
              <input
                type="password"
                id="login-password"
                {...register("options.password")}
                className="w-full flex-shrink appearance-none rounded-lg border-default-300 bg-white py-3 text-base text-default-700 placeholder-default-400 focus:outline-none"
                placeholder="***********"
              />
            </div>
          </label>
        </div>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="ml-2 mt-5 h-6 w-6 cursor-pointer text-sm font-semibold text-default-600 underline decoration-2"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
          strokeWidth="2"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21"
          />
        </svg>
      </div>

      <div className="flex items-center space-x-5">
        <button
          type="submit"
          className="mt-4 rounded-lg bg-primary px-4 py-2 text-white"
        >
          Save Credentials
        </button>

        <div className="mt-5">
          No account created?
          <button
            type="button"
            className="ml-2 text-sm font-semibold text-primary underline decoration-2"
          >
            Create Account
          </button>
        </div>
      </div>
    </form>
  );
}
