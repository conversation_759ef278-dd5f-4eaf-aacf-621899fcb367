import { api } from "@/lib/api";
import KplcCredentials from "./form";
import { auth } from "@/auth";

export const metadata = {
  title: "Kplc Settings",
  description: "Settings",
};

export default async function page() {
  const session = await auth();

  const settings = await api.get<PaginatedData<Setting>>(`settings`, {
    ...(session?.branch && { branch: session.branch.id }),
    name: "kplc",
  });

  const defaultValues = settings?.data[0] || {
    id: "",
    branchId: session?.branch?.id,
    name: "kplc",
    options: {},
    createdAt: "",
    updatedAt: "",
    branch: null,
  };

  const updateSettings = async (payload: Setting) => {
    "use server";

    await api.post("settings", payload);
  };

  return (
    <>
      <div className="pt-4">
        <p className="font- text-slate-600">
          KPLC (Kenya Power) settings for API integration
        </p>
      </div>

      <hr className="mb-8 mt-4" />
      <p className="py-2 text-xl font-semibold">Enter KPLC Config</p>

      <KplcCredentials
        defaultValues={defaultValues}
        updateSettings={updateSettings}
      />
    </>
  );
}
