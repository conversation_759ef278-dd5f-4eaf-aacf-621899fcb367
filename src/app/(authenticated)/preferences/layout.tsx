import Link from "next/link";
import { PropsWithChildren } from "react";
import SettingsNav from "./nav";
import { auth } from "@/auth";
import { api } from "@/lib/api";

export const metadata = {
  title: "Preferences",
};

export default async function layout({ children }: PropsWithChildren) {
  const session = await auth();

  const integrations = await api.get<PaginatedData<Setting>>(`settings`, {
    ...(session?.branch && { branch: session.branch.id }),
    name: "integrations",
  });

  return (
    <div className="min-h-screen max-w-full pr-4">
      <div className="grid grid-cols-10 pt-3 sm:grid-cols-10">
        <div className="relative my-4 w-56 hidden">
          <input
            className="peer hidden"
            type="checkbox"
            name="select-1"
            id="select-1"
          />
          <label
            htmlFor="select-1"
            className="flex w-full cursor-pointer select-none rounded-lg border p-2 px-3 text-sm text-default-700 ring-primary peer-checked:ring"
          >
            Notifications{" "}
          </label>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="pointer-events-none absolute right-0 top-3 ml-auto mr-5 h-4 text-slate-700 transition peer-checked:rotate-180"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            strokeWidth="2"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M19 9l-7 7-7-7"
            />
          </svg>
          <ul className="max-h-0 select-none flex-col overflow-hidden rounded-b-lg shadow-md transition-all duration-300 peer-checked:max-h-56 peer-checked:py-3">
            <li className="cursor-pointer px-3 py-2 text-sm text-slate-600 hover:bg-primary hover:text-white">
              Notifications
            </li>
            <li className="cursor-pointer px-3 py-2 text-sm text-slate-600 hover:bg-primary hover:text-white">
              Team
            </li>
            <li className="cursor-pointer px-3 py-2 text-sm text-slate-600 hover:bg-primary hover:text-white">
              Others
            </li>
          </ul>
        </div>

        <div className="col-span-2 hidden pt-4 sm:block">
          <SettingsNav integrations={integrations?.data} />
        </div>

        <div className="col-span-10 lg:col-span-8 mt-4 overflow-hidden rounded-xl bg-white px-2 lg:px-8 shadow dark:bg-default-700">
          {children}
        </div>
      </div>
    </div>
  );
}
