"use client";

import { useForm } from "react-hook-form";
import toast from "react-hot-toast";

export default function MpesaCredentials({
  defaultValues,
  updateSettings,
}: {
  defaultValues: Setting;
  updateSettings: (data: Setting) => Promise<void>;
}) {
  const { register, watch, handleSubmit } = useForm<Setting>({
    defaultValues,
  });

  const mpesa = watch("options");

  const onSubmit = async (payload: Setting) => {
    toast.promise(updateSettings(payload), {
      loading: "Updating settings...",
      error: "Could not update settings",
      success: "Saved settings succesfuly",
    });
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="mt-5 space-y-4">
      <div className="flex justify-between">
        <div className="flex w-1/3 flex-col">
          <label className="mb-2">
            <span className="text-sm text-default-500">Environment</span>
          </label>
          <label
            htmlFor="live"
            className="relative inline-flex cursor-pointer items-center"
          >
            <input
              type="checkbox"
              id="live"
              className="peer sr-only"
              {...register("options.live")}
              defaultChecked={mpesa.live}
            />
            <div className="peer h-6 w-11 rounded-full bg-default-200 after:absolute after:left-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-default-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-primary peer-checked:after:translate-x-full peer-checked:after:border-white peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:border-default-600 dark:bg-default-700 dark:peer-focus:ring-primary"></div>
            <span className="ml-3 text-sm font-medium text-default-900 dark:text-default-300">
              {mpesa.live ? "Live" : "Sandbox"}
            </span>
          </label>
        </div>

        <div className="flex flex-1 flex-col">
          <label className="mb-2">
            <span className="text-sm text-default-500">Identifier type</span>
          </label>

          <div className="flex items-center space-x-6">
            <label
              htmlFor="type"
              className="relative inline-flex cursor-pointer items-center"
            >
              <input
                type="radio"
                id="type"
                value={"4"}
                className="peer sr-only"
                {...register("options.type")}
              />
              <div className="peer h-6 w-11 rounded-full bg-default-200 after:absolute after:left-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-default-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-primary peer-checked:after:translate-x-full peer-checked:after:border-white peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:border-default-600 dark:bg-default-700 dark:peer-focus:ring-primary"></div>
              <span className="ml-3 text-sm font-medium text-default-900 dark:text-default-300">
                Paybill
              </span>
            </label>
            <label
              htmlFor="till"
              className="relative inline-flex cursor-pointer items-center"
            >
              <input
                type="radio"
                id="till"
                value={"2"}
                className="peer sr-only"
                {...register("options.type")}
              />
              <div className="peer h-6 w-11 rounded-full bg-default-200 after:absolute after:left-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-default-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-primary peer-checked:after:translate-x-full peer-checked:after:border-white peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:border-default-600 dark:bg-default-700 dark:peer-focus:ring-primary"></div>
              <span className="ml-3 text-sm font-medium text-default-900 dark:text-default-300">
                Buy Goods
              </span>
            </label>
          </div>
        </div>
      </div>

      {Number(mpesa.type) === 4 && (
        <p className="text-sm font-bold italic text-default-500">
          Use Paybill for both the Store number and Business shortcode fields
          below
        </p>
      )}

      <div className="flex flex-col space-y-2 sm:space-x-3 sm:space-y-0 lg:flex-row">
        <label htmlFor="store" className="w-1/2 space-y-2">
          <span className="text-sm text-default-500">
            Store number (or Paybill)
          </span>
          <div className="relative flex overflow-hidden rounded-md border-2 transition focus-within:border-primary">
            <input
              type="number"
              id="store"
              {...register("options.store")}
              className="w-full flex-shrink appearance-none rounded-lg border-default-300 bg-white px-4 py-3 text-base text-default-700 placeholder-default-400 focus:outline-none"
              placeholder="174177"
            />
          </div>
        </label>

        <label htmlFor="mpesa-shortcode" className="w-1/2 space-y-2">
          <span className="text-sm text-default-500">
            Business shortcode (Till/Paybill)
          </span>

          <div className="relative flex overflow-hidden rounded-md border-2 transition focus-within:border-primary">
            <input
              type="number"
              id="mpesa-shortcode"
              {...register("options.shortcode")}
              className="w-full flex-shrink appearance-none rounded-lg border-default-300 bg-white px-4 py-3 text-base text-default-700 placeholder-default-400 focus:outline-none"
              placeholder="174177"
            />
          </div>
        </label>
      </div>

      <div className="flex flex-col space-y-2 sm:flex-row sm:space-x-3 sm:space-y-0">
        <label htmlFor="key" className="w-1/2 space-y-2">
          <span className="text-sm text-default-500">Consumer key</span>
          <div className="relative flex overflow-hidden rounded-md border-2 transition focus-within:border-primary">
            <input
              type="text"
              id="key"
              {...register("options.key")}
              className="w-full flex-shrink appearance-none rounded-lg border-default-300 bg-white px-4 py-3 text-base text-default-700 placeholder-default-400 focus:outline-none"
              placeholder="App consumer key"
            />
          </div>
        </label>
        <label htmlFor="mpesa-secret" className="w-1/2 space-y-2">
          <span className="text-sm text-default-500">Consumer secret</span>
          <div className="relative flex overflow-hidden rounded-md border-2 transition focus-within:border-primary">
            <input
              type="text"
              id="mpesa-secret"
              {...register("options.secret")}
              className="w-full flex-shrink appearance-none rounded-lg border-default-300 bg-white px-4 py-3 text-base text-default-700 placeholder-default-400 focus:outline-none"
              placeholder="App consumer secret"
            />
          </div>
        </label>
      </div>

      <div className="flex w-full flex-col space-y-2 sm:flex-row sm:space-x-3 sm:space-y-0">
        <label htmlFor="passkey" className="w-full space-y-2">
          <span className="text-sm text-default-500">Online passkey</span>
          <div className="relative flex overflow-hidden rounded-md border-2 transition focus-within:border-primary">
            <input
              type="text"
              id="passkey"
              {...register("options.passkey")}
              className="w-full appearance-none rounded-lg border-default-300 bg-white px-4 py-3 text-base text-default-700 placeholder-default-400 focus:outline-none"
              placeholder="Online passkey from Safaricom"
            />
          </div>
        </label>
      </div>

      <div className="flex flex-col space-y-2 sm:flex-row sm:space-x-3 sm:space-y-0">
        <label htmlFor="username" className="w-1/2 space-y-2">
          <span className="text-sm text-default-500">
            Initiator Username (optional)
          </span>
          <div className="relative flex overflow-hidden rounded-md border-2 transition focus-within:border-primary">
            <input
              type="text"
              id="username"
              {...register("options.username")}
              className="w-full flex-shrink appearance-none rounded-lg border-default-300 bg-white px-4 py-3 text-base text-default-700 placeholder-default-400 focus:outline-none"
              placeholder="Username"
            />
          </div>
        </label>

        <label htmlFor="mpesa-password" className="w-1/2 space-y-2">
          <span className="text-sm text-default-500">
            Initiator password (optional)
          </span>
          <div className="relative flex items-center overflow-hidden rounded-md border-2 transition focus-within:border-primary">
            <input
              type="password"
              id="mpesa-password"
              {...register("options.password")}
              className="w-full flex-shrink appearance-none rounded-lg border-default-300 bg-white px-4 py-3 text-base text-default-700 placeholder-default-400 focus:outline-none"
              placeholder="***********"
            />

            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="mx-2 h-6 w-6 cursor-pointer text-sm font-semibold text-default-600 underline decoration-2"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              strokeWidth="2"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21"
              />
            </svg>
          </div>
        </label>
      </div>

      <div className="flex w-full flex-col space-y-2 sm:flex-row sm:space-x-3 sm:space-y-0">
        <label htmlFor="webhook" className="w-full space-y-2">
          <span className="text-sm text-default-500">Webhook URL</span>
          <div className="relative flex overflow-hidden rounded-md border-2 transition focus-within:border-primary">
            <input
              type="url"
              id="webhook"
              {...register("options.webhook")}
              className="w-full appearance-none rounded-lg border-default-300 bg-white px-4 py-3 text-base text-default-700 placeholder-default-400 focus:outline-none"
              placeholder="E.g https://example.com/mpesa/webhook"
            />
          </div>
        </label>
      </div>

      <div className="flex items-center justify-end space-x-5 pb-5">
        <button
          type="submit"
          className="mt-4 rounded-lg bg-primary px-4 py-2 text-white"
        >
          Save Credentials
        </button>
      </div>
    </form>
  );
}
