import { api } from "@/lib/api";
import MpesaCredentials from "./form";
import { auth } from "@/auth";

export const metadata = {
  title: "Lipa Na M-Pesa Settings",
  description: "Settings",
};

export default async function page() {
  const session = await auth();

  const settings = await api.get<PaginatedData<Setting>>(`settings`, {
    ...(session?.branch && { branch: session.branch.id }),
    name: "mpesa",
  });

  const defaultValues = settings?.data[0] || {
    id: "",
    branchId: session?.branch?.id,
    name: "mpesa",
    options: {
      live: false,
      type: "4",
      key: "",
      secret: "",
      passkey: "",
      store: "",
      shortcode: "",
      username: "",
      password: "",
    },
    createdAt: "",
    updatedAt: "",
    branch: null,
  };

  const updateSettings = async (payload: Setting) => {
    "use server";

    await api.post("settings", payload);
  };

  return (
    <>
      <div className="pt-4">
        <p className="font- text-slate-600">
          Lipa na Mpesa settings for API integration
        </p>
      </div>

      <hr className="mb-8 mt-4" />
      <p className="py-2 text-xl font-semibold">Enter M-Pesa Config</p>

      <MpesaCredentials
        defaultValues={defaultValues}
        updateSettings={updateSettings}
      />
    </>
  );
}
