"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";

export default function SettingsNav({
  integrations,
}: {
  integrations?: Setting[];
}) {
  const path = usePathname();

  const screens = [
    "Branding",
    "Billing",
    "Notifications",
    "Products",
    "Integrations",
  ];

  if (integrations) {
    const setting = integrations[0];

    if (setting?.options) {
      Object.keys(setting.options).map((integration) => {
        screens.push(integration);
      });
    }
  }

  return (
    <ul>
      <li
        className={
          "cursor-pointer border-l-2 px-2 py-2 pl-5 font-semibold transition hover:border-l-primary hover:text-primary " +
          (path === "/preferences"
            ? "border-l-primary text-primary"
            : "border-transparent")
        }
      >
        <Link href={`/preferences`} className="">
          Business
        </Link>
      </li>

      {screens.map((screen) => (
        <li
          key={screen}
          className={
            "mt-5 cursor-pointer border-l-2 px-2 py-2 pl-5 font-semibold transition hover:border-l-primary hover:text-primary " +
            (path.includes(screen.toLowerCase())
              ? "border-l-primary text-primary"
              : "border-transparent")
          }
        >
          <Link
            href={`/preferences/${screen.toLowerCase()}`}
            className="capitalize"
          >
            {screen}
          </Link>
        </li>
      ))}
    </ul>
  );
}
