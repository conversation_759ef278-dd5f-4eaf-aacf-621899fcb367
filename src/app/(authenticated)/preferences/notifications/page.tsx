import Switch from "./switch";

export const dynamic = "force-dynamic";
export const fetchCache = "force-no-store";

export default function page() {
  const settings: Record<string, boolean> = {
    push: true,
    email: false,
    sms: true,
  };

  const updateSettings = async () => {
    "use server";

  };
  return (
    <>
      <div className="grid border-b py-6 sm:grid-cols-2">
        <div className="">
          <h2 className="text-lg font-semibold leading-4 text-slate-700">
            Comments
          </h2>
          <p className="font- text-slate-600">
            Lorem ipsum dolor, Alias eligendi laboriosam magni reiciendis neque.
          </p>
        </div>
        <div className="mt-4 flex items-center sm:justify-end">
          <div className="flex flex-col gap-3">
            {Object.keys(settings).map((key) => (
              <Switch
                key={key}
                setting={key}
                checked={settings[key]}
                updateSettings={updateSettings}
              />
            ))}
          </div>
        </div>
      </div>

      <div className="grid border-b py-6 sm:grid-cols-2">
        <div className="">
          <h2 className="text-lg font-semibold leading-4 text-slate-700">
            Reminders
          </h2>
          <p className="font- text-slate-600">
            Lorem ipsum dolor, Alias eligendi laboriosam magni reiciendis neque.
          </p>
        </div>
        <div className="mt-4 flex items-center sm:justify-end">
          <div className="flex flex-col gap-3">
            {Object.keys(settings).map((key) => (
              <Switch
                key={key}
                setting={key}
                checked={settings[key]}
                updateSettings={updateSettings}
              />
            ))}
          </div>
        </div>
      </div>

      <div className="grid border-b py-6 sm:grid-cols-2">
        <div className="">
          <h2 className="text-lg font-semibold leading-4 text-slate-700">
            Updates
          </h2>
          <p className="font- text-slate-600">
            Lorem ipsum dolor, Alias eligendi laboriosam magni reiciendis neque.
          </p>
        </div>
        <div className="mt-4 flex items-center sm:justify-end">
          <div className="flex flex-col gap-3">
            {Object.keys(settings).map((key) => (
              <Switch
                key={key}
                setting={key}
                checked={settings[key]}
                updateSettings={updateSettings}
              />
            ))}
          </div>
        </div>
      </div>
    </>
  );
}
