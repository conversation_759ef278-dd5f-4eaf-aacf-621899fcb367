"use client";

export default function Switch({
  setting,
  checked,
  updateSettings,
}: {
  setting: string;
  checked: boolean;
  updateSettings: () => Promise<void>;
}) {
  return (
    <label
      htmlFor={setting}
      className="relative inline-flex cursor-pointer items-center"
    >
      <input
        type="checkbox"
        id={setting}
        className="peer sr-only"
        onChange={updateSettings}
        defaultChecked={checked}
      />

      <div className="peer h-6 w-11 rounded-full bg-default-200 after:absolute after:left-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-default-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-primary peer-checked:after:translate-x-full peer-checked:after:border-white peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:border-default-600 dark:bg-default-700 dark:peer-focus:ring-primary"></div>
      <span className="ml-3 text-sm font-medium capitalize text-default-900 dark:text-default-300">
        {setting}
      </span>
    </label>
  );
}
