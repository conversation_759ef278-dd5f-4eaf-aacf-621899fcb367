import { auth } from "@/auth";
import { api } from "@/lib/api";
import { cache } from "react";
import BranchForm from "./branch";
import { revalidatePath } from "next/cache";

const fetchBranch = cache(async (branchId: string) =>
  api.get<Branch>(`branches/${branchId}`),
);

export const generateMetadata = async () => {
  const session = await auth();

  return {
    title: `${session?.vendor?.name} ${session?.branch?.name} Settings`,
    description: "Notification settings",
    keywords: ["notification", "settings"],
  };
};

export default async function page() {
  const session = await auth();

  const branch = await fetchBranch(session?.branch?.id!);

  const storeBranch = async (data: FormData) => {
    "use server";

    await api.put<Branch>(`branches/${branch?.id}`, data);

    revalidatePath("/preferences");
  };

  return (
    <div>
      <div className="border-b pb-8 pt-4">
        <p className="font- text-slate-600">{branch?.details}</p>
      </div>

      {branch && (
        <BranchForm defaultValues={branch} storeBranch={storeBranch} />
      )}
    </div>
  );
}
