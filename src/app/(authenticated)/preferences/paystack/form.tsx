"use client";

import { useForm } from "react-hook-form";
import toast from "react-hot-toast";

export default function PaystackCredentials({
  defaultValues,
  updateSettings,
}: {
  defaultValues: Setting;
  updateSettings: (data: Setting) => Promise<void>;
}) {
  const { register, handleSubmit } = useForm<Setting>({
    defaultValues,
  });

  const onSubmit = async (payload: Setting) => {
    toast.promise(updateSettings(payload), {
      loading: "Updating settings...",
      error: "Could not update settings",
      success: "Saved settings succesfuly",
    });
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <div className="flex items-center">
        <div className="flex w-full flex-col space-y-2">
          <label htmlFor="publicKey">
            <span className="mb-1 text-sm text-default-500">Public Key</span>

            <div className="relative flex overflow-hidden rounded-md border-2 transition focus-within:border-primary">
              <input
                type="text"
                id="publicKey"
                {...register("options.publicKey")}
                className="w-full flex-shrink appearance-none rounded-lg border-default-300 bg-white px-4 py-3 text-base text-default-700 placeholder-default-400 focus:outline-none"
                placeholder="Email or phone"
              />
            </div>
          </label>
          <label htmlFor="login-secretKey">
            <span className="mb-1 text-sm text-default-500">Secret Key</span>
            <div className="relative flex overflow-hidden rounded-md border-2 transition focus-within:border-primary">
              <input
                type="secretKey"
                id="login-secretKey"
                {...register("options.secretKey")}
                className="w-full flex-shrink appearance-none rounded-lg border-default-300 bg-white px-4 py-3 text-base text-default-700 placeholder-default-400 focus:outline-none"
                placeholder="***********"
              />
            </div>
          </label>
        </div>
      </div>

      <div className="mt-8 flex items-center justify-between">
        <button
          type="reset"
          className="ml-2 text-sm font-semibold text-primary underline decoration-2"
        >
          Reset
        </button>

        <button
          type="submit"
          className="rounded-lg bg-primary px-4 py-2 text-white"
        >
          Save Credentials
        </button>
      </div>
    </form>
  );
}
