import { auth } from "@/auth";
import PaystackCredentials from "./form";
import { api } from "@/lib/api";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Paystack Settings",
};

export default async function page() {
  const session = await auth();

  const settings = await api.get<PaginatedData<Setting>>(`settings`, {
    ...(session?.branch && { branch: session.branch.id }),
    name: "paystack",
  });

  const defaultValues = settings?.data[0] || {
    id: "",
    branchId: session?.branch?.id,
    name: "paystack",
    options: {
      publicKey: "",
      secretKey: "",
    },
    createdAt: "",
    updatedAt: "",
    branch: null,
  };

  const updateSettings = async (payload: Setting) => {
    "use server";

    await api.post("settings", payload);
  };

  return (
    <div>
      <div className="pt-4">
        <p className="font- text-slate-600">
          Paystack (Card Payments) settings for API integration
        </p>
      </div>

      <hr className="mb-8 mt-4" />
      <p className="py-2 text-xl font-semibold">Enter Paystack Config</p>

      <PaystackCredentials
        defaultValues={defaultValues}
        updateSettings={updateSettings}
      />
    </div>
  );
}
