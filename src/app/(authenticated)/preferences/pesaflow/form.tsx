"use client";

import { useForm } from "react-hook-form";
import toast from "react-hot-toast";

export default function PesaFlowCredentials({
  defaultValues,
  updateSettings,
}: {
  defaultValues: Setting;
  updateSettings: (data: Setting) => Promise<void>;
}) {
  const { register, watch, handleSubmit } = useForm<Setting>({
    defaultValues: {
      options: {
        live: false,
        clientId: "",
        clientKey: "",
        clientSecret: "",
        serviceId: "",
      },
    },
  });

  const pesaflow = watch("options");

  const onSubmit = async (payload: Setting) => {
    toast.promise(updateSettings(payload), {
      loading: "Updating settings...",
      error: "Could not update settings",
      success: "Saved settings succesfuly",
    });
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <div className="flex flex-col gap-3">
        <div className="flex flex-col">
          <label className="mb-2">
            <span className="text-sm text-default-500">Environment</span>
          </label>
          <label
            htmlFor="live"
            className="relative inline-flex cursor-pointer items-center"
          >
            <input
              type="checkbox"
              id="live"
              className="peer sr-only"
              {...register("options.live")}
              defaultChecked={pesaflow.live}
            />
            <div className="peer h-6 w-11 rounded-full bg-default-200 after:absolute after:left-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-default-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-primary peer-checked:after:translate-x-full peer-checked:after:border-white peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:border-default-600 dark:bg-default-700 dark:peer-focus:ring-primary"></div>
            <span className="ml-3 text-sm font-medium text-default-900 dark:text-default-300">
              {pesaflow.live ? "Live" : "Sandbox"}
            </span>
          </label>
        </div>

        <label htmlFor="clientId">
          <span className="text-sm text-default-500">Client Id</span>
          <div className="relative flex overflow-hidden rounded-md border-2 transition focus-within:border-primary">
            <input
              type="text"
              id="clientId"
              {...register("options.clientId")}
              className="w-full px-4 flex-shrink appearance-none rounded-lg border-default-300 bg-white py-3 text-base text-default-700 placeholder-default-400 focus:outline-none"
            />
          </div>
        </label>

        <label htmlFor="login-clientKey">
          <span className="text-sm text-default-500">Clent Key</span>
          <div className="relative flex overflow-hidden rounded-md border-2 transition focus-within:border-primary">
            <input
              type="clientKey"
              id="login-clientKey"
              {...register("options.clientKey")}
              className="w-full flex-shrink appearance-none rounded-lg border-default-300 bg-white px-4 py-3 text-base text-default-700 placeholder-default-400 focus:outline-none"
            />
          </div>
        </label>

        <label htmlFor="login-clientSecret">
          <span className="text-sm text-default-500">Clent Secret</span>
          <div className="relative flex overflow-hidden rounded-md border-2 transition focus-within:border-primary">
            <input
              type="clientSecret"
              id="login-clientSecret"
              {...register("options.clientSecret")}
              className="w-full flex-shrink appearance-none rounded-lg border-default-300 bg-white px-4 py-3 text-base text-default-700 placeholder-default-400 focus:outline-none"
            />
          </div>
        </label>

        <label htmlFor="login-serviceId">
          <span className="text-sm text-default-500">Service Id</span>
          <div className="relative flex overflow-hidden rounded-md border-2 transition focus-within:border-primary">
            <input
              type="serviceId"
              id="login-serviceId"
              {...register("options.serviceId")}
              className="w-full flex-shrink appearance-none rounded-lg border-default-300 bg-white px-4 py-3 text-base text-default-700 placeholder-default-400 focus:outline-none"
            />
          </div>
        </label>
      </div>

      <div className="mt-8 flex items-center justify-between">
        <button
          type="reset"
          className="ml-2 text-sm font-semibold text-primary underline decoration-2"
        >
          Reset
        </button>

        <button
          type="submit"
          className="rounded-lg bg-primary px-4 py-2 text-white"
        >
          Save Credentials
        </button>
      </div>
    </form>
  );
}
