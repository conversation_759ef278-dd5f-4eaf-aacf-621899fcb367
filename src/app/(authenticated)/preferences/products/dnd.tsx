"use client";

import { useCallback, useState, useEffect, useRef } from "react";
import update from "immutability-helper";
import type { Identifier, XYCoord } from "dnd-core";
import { useDrag, useDrop, DndProvider } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import {
  Card,
  CardBody,
  CardHeader,
  Accordion,
  AccordionItem,
} from "@nextui-org/react";
import { Switch } from "@nextui-org/switch";
import toast from "react-hot-toast";
import { useForm } from "react-hook-form";
export interface CardProps {
  id: any;
  text: string;
  index: number;
  enabled: boolean;
  moveCard: (dragIndex: number, hoverIndex: number) => void;
  onChange: (value: boolean) => void;
}

interface DragItem {
  index: number;
  id: string;
  type: string;
}

const CategoryCard = ({
  id,
  text,
  index,
  enabled,
  moveCard,
  onChange,
}: CardProps) => {
  const ref = useRef<HTMLDivElement>(null);
  const [{ handlerId }, drop] = useDrop<
    DragItem,
    void,
    { handlerId: Identifier | null }
  >({
    accept: "card",
    collect(monitor) {
      return {
        handlerId: monitor.getHandlerId(),
      };
    },
    hover(item: DragItem, monitor) {
      if (!ref.current) {
        return;
      }
      const dragIndex = item.index;
      const hoverIndex = index;

      // Don't replace items with themselves
      if (dragIndex === hoverIndex) {
        return;
      }

      // Determine rectangle on screen
      const hoverBoundingRect = ref.current?.getBoundingClientRect();

      // Get vertical middle
      const hoverMiddleY =
        (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;

      // Determine mouse position
      const clientOffset = monitor.getClientOffset();

      // Get pixels to the top
      const hoverClientY = (clientOffset as XYCoord).y - hoverBoundingRect.top;

      // Only perform the move when the mouse has crossed half of the items height
      // When dragging downwards, only move when the cursor is below 50%
      // When dragging upwards, only move when the cursor is above 50%

      // Dragging downwards
      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
        return;
      }

      // Dragging upwards
      if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
        return;
      }

      // Time to actually perform the action
      moveCard(dragIndex, hoverIndex);

      // Note: we're mutating the monitor item here!
      // Generally it's better to avoid mutations,
      // but it's good here for the sake of performance
      // to avoid expensive index searches.
      item.index = hoverIndex;
    },
  });

  const [{ isDragging }, drag] = useDrag({
    type: "card",
    item: () => {
      return { id, index };
    },
    collect: (monitor: any) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  drag(drop(ref));
  return (
    <div
      ref={ref}
      data-handler-id={handlerId}
      className={
        "m-2 flex cursor-move items-center justify-between rounded-lg border border-dashed border-gray-300 bg-white p-3 " +
        (isDragging ? "opacity-50" : "")
      }
    >
      <div className="flex gap-2">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          strokeWidth={1.5}
          stroke="currentColor"
          className="h-6 w-6 text-gray-400"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="M3 7.5 7.5 3m0 0L12 7.5M7.5 3v13.5m13.5 0L16.5 21m0 0L12 16.5m4.5 4.5V7.5"
          />
        </svg>
        <span>
          {index + 1}. {text}{" "}
        </span>
      </div>

      <Switch
        defaultSelected={enabled}
        aria-label="Enable"
        onChange={(e) => onChange(e.target.checked)}
      />
    </div>
  );
};

export default function DragAndDrop({
  services,
  categories,
  defaultOrder,
  updateCategoriesOrder,
}: {
  services: Service[];
  categories: Record<string, ProductCategory[]>;
  defaultOrder: Record<string, Record<string, boolean>>;
  updateCategoriesOrder: (
    order: Record<string, Record<string, boolean>>,
    serviceId: string,
  ) => Promise<void>;
}) {
  const { watch, setValue, handleSubmit, formState } = useForm({
    defaultValues:
      Object.keys(defaultOrder).length > 0
        ? defaultOrder
        : services.reduce(
            (acc, curr) => ({
              ...acc,
              [curr.id]: {},
            }),
            {} as Record<string, Record<string, boolean>>,
          ),
  });
  const orderedByService = watch();
  const [serviceId, setServiceId] = useState(services[0].id);
  const [finalOrder, setFinalOrder] = useState(orderedByService);
  const [cards, setCards] = useState<ProductCategory[]>([]);

  const mapOrder = <T extends Record<string, any>>(
    array: T[] = [],
    order: string[] = [],
    key: string = "id",
  ) => {
    if (order.length > 0) {
      array.sort((a, b) => {
        var A = a[key];
        var B = b[key];

        if (order.indexOf(A) > order.indexOf(B)) {
          return 1;
        } else {
          return -1;
        }
      });
    }

    return array;
  };

  const processOrder = () => {
    setFinalOrder(
      cards?.reduce(
        (acc, curr) => {
          if (!acc[serviceId]) {
            acc[serviceId] = finalOrder[serviceId];
          }

          acc[serviceId] = {
            ...acc[serviceId],
            [curr.id]: orderedByService[serviceId][curr.id],
          };
          return acc;
        },
        {} as Record<string, Record<string, boolean>>,
      ),
    );
  };

  const moveCard = useCallback((dragIndex: number, hoverIndex: number) => {
    setCards((prevCards: ProductCategory[]) =>
      update(prevCards, {
        $splice: [
          [dragIndex, 1],
          [hoverIndex, 0, prevCards[dragIndex] as ProductCategory],
        ],
      }),
    );

    processOrder();
  }, []);

  const renderCard = useCallback(
    (card: ProductCategory, index: number, serviceId: string) => {
      return (
        <CategoryCard
          key={card.id}
          index={index}
          enabled={orderedByService[serviceId]?.[card.id]}
          id={card.id}
          text={`${card.name} (${card.id})`}
          moveCard={moveCard}
          onChange={(selected) => {
            setValue(`${serviceId}.${card.id}`, selected);
          }}
        />
      );
    },
    [],
  );

  const updateCardOrder = async (serviceId: string) => {
    toast.promise(
      updateCategoriesOrder(
        cards?.reduce(
          (acc, curr) => {
            if (!acc[serviceId]) {
              acc[serviceId] = finalOrder[serviceId];
            }

            acc[serviceId] = {
              ...acc[serviceId],
              [curr.id]: orderedByService[serviceId][curr.id] || false,
            };
            return acc;
          },
          {} as Record<string, Record<string, boolean>>,
        ),
        serviceId,
      ),
      {
        loading: "Updating order...",
        success: "Order updated",
        error: "Error updating order",
      },
    );
  };

  useEffect(() => {
    setCards(categories[serviceId]);

    if (serviceId) {
      setCards(
        mapOrder(
          categories[serviceId] || [],
          Object.keys(defaultOrder[serviceId] || categories[serviceId] || []) ||
            [],
          "id",
        ),
      );
    }
  }, [serviceId]);

  useEffect(() => {
    processOrder();
  }, [cards]);

  return (
    <>
      <p className="mb-5 border-b">
        {JSON.stringify(orderedByService[serviceId], null, 4)}
      </p>

      <Accordion>
        {services.map((service) => (
          <AccordionItem
            key={service.id}
            aria-label={service.name}
            title={service.name}
            className="px-2"
          >
            <h2 className="my-5 text-2xl">{service.name} categories</h2>

            <DndProvider backend={HTML5Backend}>
              {cards?.map((card, i) => renderCard(card, i, service.id))}
            </DndProvider>

            <div className="my-5 flex justify-end">
              <button
                onClick={() => updateCardOrder(service.id)}
                className="rounded-lg bg-default-500 px-4 py-3 font-bold text-white hover:border-default-500 hover:text-default-500"
              >
                Update order
              </button>
            </div>
          </AccordionItem>
        ))}
      </Accordion>
    </>
  );
}
