import { api } from "@/lib/api";
import DragAndDrop from "./dnd";
import { auth } from "@/auth";
import { revalidatePath } from "next/cache";

export const dynamic = "force-dynamic";
export const fetchCache = "force-no-store";

export const metadata = {
  title: "Products",
};

export default async function page() {
  const services = await api.get<PaginatedData<Service>>("services", {
    per: 30,
  });
  const categories = await api.get<PaginatedData<ProductCategory>>(
    "product-categories",
    { per: 30, with: "productType" },
  );

  const session = await auth();

  const settings = await api.get<PaginatedData<Setting>>(`settings`, {
    ...(session?.branch && { branch: session.branch.id }),
    name: "products",
  });

  const defaultValues = settings?.data[0] || {
    id: "",
    branchId: session?.branch?.id,
    name: "products",
    options: {},
    createdAt: "",
    updatedAt: "",
    branch: null,
  };

  const updateSettings = async (
    newOrder: Record<string, Record<string, boolean>>,
    serviceId: string,
  ) => {
    "use server";

    const payload = {
      ...defaultValues,
      options: {
        ...defaultValues.options,
        [serviceId]: newOrder,
      },
    };

    await api.post("settings", payload);

    revalidatePath("/preferences/products");
  };

  return (
    <div>
      {categories && services && (
        <DragAndDrop
          services={services?.data}
          categories={categories.data.reduce(
            (acc, c) => ({
              ...acc,
              [c.productType.serviceId]: [...(acc[c.productType.serviceId] || []), c],
            }),
            {} as Record<string, ProductCategory[]>,
          )}
          defaultOrder={defaultValues?.options}
          updateCategoriesOrder={updateSettings}
        />
      )}
    </div>
  );
}
