import { api, imagePath } from "@/lib/api";
import ProductsEditForm from "./form";
import { revalidatePath } from "next/cache";
import { cache } from "react";
import { auth } from "@/auth";
import React from "react";

const fetchProduct = cache((productId: string) =>
  api.get<Product>(`products/${productId}`),
);

export const generateMetadata = async ({
  params: { productId },
}: {
  params: { productId: string };
}) => {
  const product = await fetchProduct(productId);

  return {
    title: product?.name,
    description: product?.details,
    keywords: ["product-templates", "list", "products list"],
  };
};

export default async function page({
  params: { productId },
}: {
  params: { productId: string };
}) {
  const product = await fetchProduct(productId);

  const storeTag = async (data: FormData) => {
    "use server";

    return await api.post<Tag>("tags", data);
  };

  const updateRecord = async (product: FormData) => {
    "use server";

    await api.put(`products/${productId}`, product);

    revalidatePath(`/products/${productId}/edit`);
  };

  return (
    <div className="p-4">
      {product && (
        <ProductsEditForm
          defaultValues={product}
          storeProduct={updateRecord}
          storeTag={storeTag}
        />
      )}
    </div>
  );
}
