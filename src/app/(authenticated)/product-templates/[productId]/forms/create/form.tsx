"use client";

import Select from "react-select";
import AsyncSelect from "react-select/async";
import { useState, ChangeEvent, useEffect } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Controller, useForm } from "react-hook-form";
import { toast } from "react-hot-toast";
import Image from "next/image";
import makeAnimated from "react-select/animated";
import FormPreview from "../../../../../../components/form-preview";
import Link from "next/link";

export default function FormsCreateForm({
	productId,
	storeProductForm,
}: {
	productId: string;
	storeProductForm: (data: FormData) => Promise<void>;
}) {
	const [preview, setPreview] = useState<string>();
	const [template, setTemplate] = useState<FormTemplate>();

	const animatedComponents = makeAnimated();

	const {
		watch,
		handleSubmit,
		register,
		reset,
		control,
		setValue,
		formState: { errors, isSubmitting },
	} = useForm<
		ProductForm & {
			upload: File;
			taskId: string;
			formTemplateId?: string;
			hasForm: boolean;
		}
	>({
		defaultValues: {
			name: "",
			details: "",
			formTemplateId: "",
			sections: [],
		},
	});

	const fieldTypes: FieldType[] = [
		{
			key: "text",
			label: "Text input",
		},
		{
			key: "tel",
			label: "Phone input",
		},
		{
			key: "email",
			label: "Email input",
		},
		{
			key: "textarea",
			label: "Textarea input",
		},
		{
			key: "checkbox",
			label: "Checkbox input",
		},
		{
			key: "multicheck",
			label: "Multi-checkbox input",
		},
		{
			key: "radio",
			label: "Radio button",
		},
		{
			key: "select",
			label: "Select options",
		},
		{
			key: "multiselect",
			label: "Multi-select options",
		},
		{
			key: "password",
			label: "Password input",
		},
		{
			key: "date",
			label: "Date picker",
		},
		{
			key: "time",
			label: "Time picker",
		},
		{
			key: "datetime",
			label: "Date and time picker",
		},
		{
			key: "number",
			label: "Number input",
		},
		{
			key: "range",
			label: "Range input",
		},
		{
			key: "color",
			label: "Color picker",
		},
		{
			key: "file",
			label: "File upload",
		},
		{
			key: "image",
			label: "Image upload",
		},
		{
			key: "video",
			label: "Video upload",
		},
		{
			key: "slot",
			label: "Time slot picker",
		},
		{
			key: "location",
			label: "Location picker",
		},
	];

	const productForm = watch();

	const handleUploadedFile = (event: ChangeEvent<HTMLInputElement>) => {
		const files = event.target.files;

		if (files) {
			const file = files[0];
			setValue("upload", file);
			const urlImage = URL.createObjectURL(file);

			setPreview(urlImage);
		}
	};

	const fetchFormTemplates = async (s: string) => {
		if (s.length > 3) {
			const types: FormTemplate[] = await fetch(
				`/api/form-templates?s=${s}`
			).then(r => r.json());

			return types;
		}

		return [];
	};

	const onSubmit: SubmitHandler<
		Partial<ProductForm & { upload: File; taskId: string }>
	> = async (p: Partial<ProductForm & { upload: File; taskId: string }>) => {
		const data = new FormData();

		data.append("name", productForm.name!);
		data.append("details", productForm.details!);

		if (productForm.auth) {
			Object.entries(productForm.auth).map(([authKey, authValue]) => {
				data.append(authKey, String(authValue));
			});
		}

		if (productForm.headers) {
			Object.entries(productForm.headers).map(
				([headerKey, headerValue]) => {
					data.append(headerKey, String(headerValue));
				}
			);
		}

		productForm.sections.map((s, si) => {
			data.append(`sections[${si}][id]`, s.id.toString());
			data.append(`sections[${si}][name]`, s.name);
			data.append(`sections[${si}][details]`, s.details);

			if (s.repeatable) {
				data.append(
					`sections[${si}][repeatable]`,
					s.repeatable.toString()
				);
			}

			if (s.repeats) {
				data.append(`sections[${si}][repeats]`, s.repeats.toString());
			}

			if (s.hasCost) {
				data.append(`sections[${si}][hasCost]`, s.hasCost.toString());
			}

			if (s.cost) {
				data.append(`sections[${si}][cost]`, s.cost.toString());
			}

			s.fields.map((f, fi) => {
				data.append(
					`sections[${si}][fields][${fi}][id]`,
					f.id.toString()
				);
				data.append(`sections[${si}][fields][${fi}][type]`, f.type);
				data.append(`sections[${si}][fields][${fi}][name]`, f.name);
				data.append(`sections[${si}][fields][${fi}][label]`, f.label);
				data.append(
					`sections[${si}][fields][${fi}][defaultValue]`,
					f.defaultValue
				);

				if (f.cost) {
					data.append(
						`sections[${si}][fields][${fi}][cost]`,
						f.cost.toString()
					);
				}

				data.append(
					`sections[${si}][fields][${fi}][placeholder]`,
					f.placeholder
				);
				data.append(
					`sections[${si}][fields][${fi}][required]`,
					f.required.toString()
				);

				if (f.repeatable) {
					data.append(
						`sections[${si}][fields][${fi}][repeatable]`,
						f.repeatable.toString()
					);
				}

				if (f.repeats) {
					data.append(
						`sections[${si}][fields][${fi}][repeats]`,
						f.repeats?.toString()
					);
				}

				if (f.min) {
					data.append(
						`sections[${si}][fields][${fi}][min]`,
						f.min?.toString()
					);
				}

				if (f.max) {
					data.append(
						`sections[${si}][fields][${fi}][max]`,
						f.max?.toString()
					);
				}

				f.options?.map((o, oi) => {
					data.append(
						`sections[${si}][fields][${fi}][options][${oi}][value]`,
						o.value.toString()
					);
					data.append(
						`sections[${si}][fields][${fi}][options][${oi}][label]`,
						o.label.toString()
					);
				});
			});
		});

		if (productForm.upload) {
			data.append("image", productForm.upload);
		}

		await toast
			.promise(
				storeProductForm(data),
				{
					loading: "Creating form...",
					success: "Form has been saved 👌",
					error: "Could not save form 🤯",
				},
				{
					position: "bottom-center",
				}
			)
			.then(() => {
				// setCreating(false);
				reset();
			})
			.catch(e => {
				console.error(e);
			});
	};

	useEffect(() => {
		if (template) {
			setValue("name", template.name);
			setValue("details", template.details);
			setValue("sections", Object.values(template.sections));
		}
	}, [template]);

	return (
		<>
			<div className="flex justify-between px-4">
				<h1 className="text-2xl font-bold">
					{template
						? `Creating from ${template.name}`
						: "Create new form for product"}
				</h1>

				<Link href={`/products/${productId}/forms`}>
					<span className="px-5 py-2 bg-primary text-white rounded-lg">
						<span>Back to forms</span>
					</span>
				</Link>
			</div>

			<div
				id="drawer-create-productForm-default"
				className="w-full p-4 bg-white dark:bg-default-800 h-screen"
			>
				<form
					className="overflow-y-auto grid grid-cols-12 gap-x-4 pb-40"
					onSubmit={handleSubmit(onSubmit)}
				>
					<div className="space-y-4 col-span-6">
						<div className="w-full">
							<label
								htmlFor="name"
								className="block mb-2 text-sm font-medium text-default-900 dark:text-white"
							>
								Select from template
							</label>
							<Controller
								name="taskId"
								control={control}
								render={({ field }) => (
									<AsyncSelect
										isMulti={false}
										loadOptions={fetchFormTemplates}
										components={animatedComponents}
										placeholder="Select template"
										getOptionLabel={(
											option: FormTemplate
										) => option.name}
										getOptionValue={(
											option: FormTemplate
										) => option.id}
										isSearchable
										classNames={{
											control: () =>
												"!py-[1px] !rounded-lg",
											menu: () => "py-1",
										}}
										onChange={value => {
											if (value) {
												setTemplate(value);
												setValue(
													"formTemplateId",
													value?.id!
												);
											}
										}}
									/>
								)}
							/>
						</div>

						<div>
							<label
								htmlFor="name"
								className="block mb-2 text-sm font-medium text-default-900 dark:text-white"
							>
								Form name
							</label>
							<input
								type="text"
								id="name"
								{...register("name")}
								className="bg-default-50 border border-default-300 text-default-900 text-sm rounded-lg focus:ring-default-600 focus:border-default-600 block w-full p-3 dark:bg-default-700 dark:border-default-600 dark:placeholder-default-400 dark:text-white dark:focus:ring-default-600 dark:focus:border-default-500"
								placeholder="Type form name"
								required
							/>
						</div>

						<div className="w-full flex space-x-2">
							<div className="w-4/5">
								<label
									htmlFor="description"
									className="block mb-2 text-sm font-medium text-default-900 dark:text-white"
								>
									Description
								</label>
								<textarea
									{...register("details")}
									className="h-24 bg-default-50 border border-default-300 text-default-900 text-sm rounded-lg focus:ring-default-600 focus:border-default-600 block w-full p-3 dark:bg-default-700 dark:border-default-600 dark:placeholder-default-400 dark:text-white dark:focus:ring-default-600 dark:focus:border-default-500"
								/>
							</div>

							<div className="w-1/5">
								<label
									htmlFor="image"
									className="block mb-2 text-sm font-medium text-default-900 dark:text-white w-full"
								>
									Form image
								</label>
								<label className="bg-default-50 border border-dashed border-default-300 text-default-900 text-sm rounded-lg focus:ring-default-600 focus:border-default-600 w-full h-24 flex justify-center items-center dark:bg-default-700 dark:border-default-600 dark:placeholder-default-400 dark:text-white dark:focus:ring-default-600 dark:focus:border-default-500">
									<input
										type="file"
										name="image"
										id="image"
										className="hidden"
										onChange={handleUploadedFile}
									/>
									{preview ? (
										<Image
											src={preview}
											alt="preview"
											width={100}
											height={50}
											className="w-1/2"
										/>
									) : (
										<p>Click to select file</p>
									)}
								</label>
							</div>
						</div>
						<div className="mb-6">
							<label
								htmlFor="name"
								className="block mb-2 text-sm font-medium text-default-900 dark:text-white"
							>
								Form action
							</label>

							<div className="flex justify-between items-center w-full px-4">
								{Object.entries({
									Save: "Save",
									Send: "Send",
									SaveSend: "Save and Send",
								}).map(([a, action]) => (
									<label
										key={a}
										className="relative inline-flex items-center cursor-pointer"
									>
										<input
											{...register("action")}
											type="radio"
											value={a}
											className="sr-only peer"
										/>
										<div className="w-11 h-6 bg-default-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-primary rounded-full peer dark:bg-default-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-default-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-default-600 peer-checked:bg-primary"></div>
										<span className="ml-3 text-sm font-medium text-default-900 dark:text-default-300">
											{action}
										</span>
									</label>
								))}
							</div>

							{["Send", "SaveSend"].includes(
								productForm.action
							) && (
								<div className="my-4 space-y-2">
									<div className="space-y-2">
										<label> Authentication (Basic Auth)</label>
										<div className="flex w-full">
											<input
												className="flex-1 px-5 py-3 rounded-l-lg border border-default-200 bg-white"
												type="text"
												name="username"
												placeholder="Username/Client ID"
											/>
											<input
												className="flex-1 px-5 py-3 rounded-r-lg border border-default-200 bg-white"
												type="text"
												name="password"
												placeholder="Password/Client secret"
											/>
										</div>
									</div>


									<div className="space-y-2">
										<label> Headers</label>
										<div className="flex w-full items-center space-x-2">
											<div className="flex-1">
											<input
												className="w-1/2 px-5 py-3 rounded-l-lg border border-default-200 bg-white"
												type="text"
												name="key"
												placeholder="Key, e.g Content-Type"
											/>
											<input
												className="w-1/2 px-5 py-3 rounded-r-lg border border-default-200 bg-white"
												type="text"
												name="password"
												placeholder="Value, e.g Application/json"
											/>
											</div>

											<button className="w-12 h-12" type="button">
											<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-10 h-10 text-default-500">
  <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
</svg>
											</button>
										</div>
									</div>
								</div>
							)}
						</div>

						<div className="flex justify-end">
							<button
								type="submit"
								className="text-white justify-center bg-primary hover:bg-default-800 focus:ring-4 focus:outline-none focus:ring-default-300 font-medium rounded-lg text-sm px-5 py-3 text-center dark:bg-primary dark:hover:bg-primary dark:focus:ring-default-800"
							>
								Submit form details
							</button>
						</div>

						<h2>Form Preview</h2>

						<FormPreview sections={productForm.sections} />
					</div>

					<div className="space-y-4 col-span-6">
						<div className="flex justify-between items-center my-3">
							<label className="text-lg font-bold">
								Form sections
							</label>

							<button
								type="button"
								onClick={() =>
									setValue("sections", [
										...productForm.sections,
										{
											name: "New section",
											details: "",
											fields: [],
											repeatable: false,
											repeats: 0,
											id: Math.random()
												.toString(16)
												.slice(2, 8)
												.toUpperCase(),
										},
									])
								}
								className="bg-primary text-white py-2 px-4 rounded-lg"
							>
								Add section
							</button>
						</div>

						{productForm.sections?.map((section, s) => (
							<div
								key={section.id}
								className="border border-default-600 rounded-lg px-4 py-3 mb-4"
							>
								<div>
									<div className="flex justify-between items-center mb-3">
										<label className="text-lg font-bold ">
											Section {s + 1}. {section.name}
										</label>

										<button
											type="button"
											className="border border-default-600 text-default-600 py-2 px-4 rounded-lg"
											onClick={() =>
												setValue(
													"sections",
													productForm.sections.map(
														(sec, seci) => {
															if (seci === s) {
																return {
																	...section,
																	fields: [
																		...section.fields,
																		...[
																			{
																				name: "",
																				label: "",
																				id: "",
																				type: "text",
																				defaultValue:
																					"",
																				placeholder:
																					"",
																				required:
																					false,
																			},
																		],
																	],
																};
															}

															return sec;
														}
													)
												)
											}
										>
											Add field
										</button>
									</div>

									<div className="mb-3">
										<input
											{...register(`sections.${s}.name`)}
											defaultValue={section.name}
											placeholder="Section name"
											className="bg-default-50 border border-default-300 text-default-900 text-sm rounded-lg focus:ring-default-600 focus:border-default-600 block w-full p-3 dark:bg-default-700 dark:border-default-600 dark:placeholder-default-400 dark:text-white dark:focus:ring-default-600 dark:focus:border-default-500"
										/>
									</div>
									<div className="mb-3">
										<textarea
											{...register(
												`sections.${s}.details`
											)}
											defaultValue={section.details}
											placeholder="Section description"
											className="bg-default-50 border border-default-300 text-default-900 text-sm rounded-lg focus:ring-default-600 focus:border-default-600 block w-full p-3 dark:bg-default-700 dark:border-default-600 dark:placeholder-default-400 dark:text-white dark:focus:ring-default-600 dark:focus:border-default-500"
										/>
									</div>

									<div className="mb-3">
										<div className="flex justify-between items-center">
											<label className="relative inline-flex items-center cursor-pointer">
												<input
													{...register(
														`sections.${s}.repeatable`
													)}
													type="checkbox"
													className="sr-only peer"
												/>
												<div className="w-11 h-6 bg-default-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-primary rounded-full peer dark:bg-default-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-default-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-default-600 peer-checked:bg-primary"></div>
												<span className="ml-3 text-sm font-medium text-default-900 dark:text-default-300">
													Repeatable
												</span>
											</label>

											{section.repeatable && (
												<input
													{...register(
														`sections.${s}.repeats`
													)}
													type="number"
													placeholder="Repeats"
													className="bg-default-50 border border-default-300 text-default-900 text-sm rounded-lg focus:ring-default-600 focus:border-default-600 block w-2/5 p-2.5 dark:bg-default-700 dark:border-default-600 dark:placeholder-default-400 dark:text-white dark:focus:ring-default-600 dark:focus:border-default-500"
												/>
											)}
										</div>
									</div>
								</div>

								{section.fields?.map((field, f) => (
									<div key={field.id} className="flex">
										<div className="pt-4 h-6">
											<span className="w-full font-bold border border-default-600 border-r-0 rounded-l-lg px-1">
												{f + 1}.
											</span>
										</div>
										<div className="mt-2 grid grid-cols-3 gap-3 border border-default-600 rounded-lg p-2 flex-1">
											<div>
												<label className="my-3">
													Field name
												</label>
												<input
													{...register(
														`sections.${s}.fields.${f}.name`
													)}
													placeholder="Field name e.g firstName"
													className="bg-default-50 border border-default-300 text-default-900 text-sm rounded-lg focus:ring-default-600 focus:border-default-600 block w-full p-3 dark:bg-default-700 dark:border-default-600 dark:placeholder-default-400 dark:text-white dark:focus:ring-default-600 dark:focus:border-default-500"
												/>
											</div>

											<div>
												<label className="my-3">
													Field label
												</label>
												<input
													{...register(
														`sections.${s}.fields.${f}.label`
													)}
													placeholder="Field label e.g First Name"
													className="bg-default-50 border border-default-300 text-default-900 text-sm rounded-lg focus:ring-default-600 focus:border-default-600 block w-full p-3 dark:bg-default-700 dark:border-default-600 dark:placeholder-default-400 dark:text-white dark:focus:ring-default-600 dark:focus:border-default-500"
												/>
											</div>

											<div>
												<label className="my-3">
													Field placeholder
												</label>
												<input
													{...register(
														`sections.${s}.fields.${f}.placeholder`
													)}
													placeholder="Placeholder text"
													className="bg-default-50 border border-default-300 text-default-900 text-sm rounded-lg focus:ring-default-600 focus:border-default-600 block w-full p-3 dark:bg-default-700 dark:border-default-600 dark:placeholder-default-400 dark:text-white dark:focus:ring-default-600 dark:focus:border-default-500"
												/>
											</div>

											<div className="space-y-2">
												<label>Field type</label>

												<Controller
													name={`sections.${s}.fields.${f}.type`}
													control={control}
													render={() => (
														<Select
															options={fieldTypes}
															getOptionValue={(
																o: FieldType
															) => o.key}
															components={
																animatedComponents
															}
															placeholder="Select type"
															required
															isSearchable
															isMulti={false}
															classNames={{
																control: () =>
																	"!py-[1px] !rounded-lg",
																menu: () =>
																	"py-1",
															}}
															defaultValue={{
																key:
																	productForm
																		.sections[
																		s
																	]["fields"][
																		f
																	].type ||
																	"text",
																label:
																	fieldTypes.find(
																		t =>
																			t.key ===
																			productForm
																				.sections[
																				s
																			][
																				"fields"
																			][f]
																				.type
																	)?.label ||
																	"Text",
															}}
															onChange={v =>
																setValue(
																	`sections.${s}.fields.${f}.type`,
																	v?.key ||
																		"text"
																)
															}
														/>
													)}
												/>
											</div>

											{[
												"select",
												"multiselect",
												"radio",
												"checkbox",
												"multicheck",
											].includes(
												section.fields[f].type
											) && (
												<div className="col-span-2">
													<div className="flex justify-between">
														<label>
															Field options
														</label>

														<button
															type="button"
															className="text-default-600 px-4 rounded-lg"
															onClick={() =>
																setValue(
																	"sections",
																	productForm.sections.map(
																		(
																			sec,
																			seci
																		) => {
																			if (
																				seci ===
																				s
																			) {
																				return {
																					...section,
																					fields: section.fields.map(
																						(
																							field,
																							fo
																						) => {
																							if (
																								fo ===
																								f
																							) {
																								return {
																									...field,
																									options:
																										[
																											...(field.options ||
																												[]),
																											...[
																												{
																													label: "",
																													value: "",
																												},
																											],
																										],
																								};
																							}
																							return field;
																						}
																					),
																				};
																			}

																			return sec;
																		}
																	)
																)
															}
														>
															<svg
																xmlns="http://www.w3.org/2000/svg"
																fill="none"
																viewBox="0 0 24 24"
																strokeWidth={
																	1.5
																}
																stroke="currentColor"
																className="w-6 h-6"
															>
																<path
																	strokeLinecap="round"
																	strokeLinejoin="round"
																	d="M12 4.5v15m7.5-7.5h-15"
																/>
															</svg>
														</button>
													</div>

													{section.fields[
														f
													].options?.map(
														(option, oi) => (
															<div
																key={oi}
																className="flex mb-2"
															>
																<input
																	{...register(
																		`sections.${s}.fields.${f}.options.${oi}.value`
																	)}
																	placeholder={`#${
																		oi + 1
																	} value`}
																	className="bg-default-50 border border-default-300 text-default-900 text-sm rounded-l-lg focus:ring-default-600 focus:border-default-600 block w-full p-1.5 dark:bg-default-700 dark:border-default-600 dark:placeholder-default-400 dark:text-white dark:focus:ring-default-600 dark:focus:border-default-500"
																/>
																<input
																	{...register(
																		`sections.${s}.fields.${f}.options.${oi}.label`
																	)}
																	placeholder={`#${
																		oi + 1
																	} label`}
																	className="bg-default-50 border border-l-0 border-default-300 text-default-900 text-sm rounded-r-lg focus:ring-default-600 focus:border-default-600 block w-full p-1.5 dark:bg-default-700 dark:border-default-600 dark:placeholder-default-400 dark:text-white dark:focus:ring-default-600 dark:focus:border-default-500"
																/>
																<a
																	href="#"
																	className="text-red-600 px-3 rounded-lg flex justify-center items-center ml-1"
																>
																	<svg
																		xmlns="http://www.w3.org/2000/svg"
																		fill="none"
																		viewBox="0 0 24 24"
																		strokeWidth={
																			1.5
																		}
																		stroke="currentColor"
																		className="w-6 h-6"
																	>
																		<path
																			strokeLinecap="round"
																			strokeLinejoin="round"
																			d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"
																		/>
																	</svg>
																</a>
															</div>
														)
													)}
												</div>
											)}

											{field.type === "range" && (
												<div className="flex items-center space-x-2">
													<div>
														<label className="my-3">
															Min value
														</label>
														<input
															{...register(
																`sections.${s}.fields.${f}.min`
															)}
															type="number"
															placeholder="Minimum value"
															className="bg-default-50 border border-default-300 text-default-900 text-sm rounded-lg focus:ring-default-600 focus:border-default-600 block w-full p-3 dark:bg-default-700 dark:border-default-600 dark:placeholder-default-400 dark:text-white dark:focus:ring-default-600 dark:focus:border-default-500"
														/>
													</div>

													<div>
														<label className="my-3">
															Max value
														</label>
														<input
															{...register(
																`sections.${s}.fields.${f}.max`
															)}
															type="number"
															placeholder="Maximum value"
															className="bg-default-50 border border-default-300 text-default-900 text-sm rounded-lg focus:ring-default-600 focus:border-default-600 block w-full p-3 dark:bg-default-700 dark:border-default-600 dark:placeholder-default-400 dark:text-white dark:focus:ring-default-600 dark:focus:border-default-500"
														/>
													</div>
												</div>
											)}

											<div className="space-y-2">
												<label className="block">
													Field default value
												</label>
												<input
													{...register(
														`sections.${s}.fields.${f}.defaultValue`
													)}
													placeholder="Field default value"
													className="bg-default-50 border border-default-300 text-default-900 text-sm rounded-lg focus:ring-default-600 focus:border-default-600 block w-full p-3 dark:bg-default-700 dark:border-default-600 dark:placeholder-default-400 dark:text-white dark:focus:ring-default-600 dark:focus:border-default-500"
												/>
											</div>

											<div className="items-center space-y-2 flex flex-col">
												<label className="text-white dark:text-primary-500">
													Required
												</label>

												<label className="relative inline-flex items-center cursor-pointer">
													<input
														{...register(
															`sections.${s}.fields.${f}.required`
														)}
														type="checkbox"
														className="sr-only peer"
													/>
													<div className="w-11 h-6 bg-default-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-primary rounded-full peer dark:bg-default-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-default-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-default-600 peer-checked:bg-primary"></div>
													<span className="ml-3 text-sm font-medium text-default-900 dark:text-default-300">
														Required
													</span>
												</label>
											</div>

											<div className="col-span-2">
												<div className="flex justify-between items-center">
													<label className="relative inline-flex items-center cursor-pointer">
														<input
															{...register(
																`sections.${s}.fields.${f}.repeatable`
															)}
															type="checkbox"
															className="sr-only peer"
														/>
														<div className="w-11 h-6 bg-default-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-primary rounded-full peer dark:bg-default-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-default-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-default-600 peer-checked:bg-primary"></div>
														<span className="ml-3 text-sm font-medium text-default-900 dark:text-default-300">
															Repeatable
														</span>
													</label>

													{section.fields[f]
														.repeatable && (
														<input
															{...register(
																`sections.${s}.fields.${f}.repeats`
															)}
															type="number"
															placeholder="Repeats"
															className="bg-default-50 border border-default-300 text-default-900 text-sm rounded-lg focus:ring-default-600 focus:border-default-600 block w-2/5 p-2.5 dark:bg-default-700 dark:border-default-600 dark:placeholder-default-400 dark:text-white dark:focus:ring-default-600 dark:focus:border-default-500"
														/>
													)}
												</div>
											</div>

											<div className="col-span-2">
												<div className="flex justify-between items-center">
													<label className="relative inline-flex items-center cursor-pointer">
														<input
															{...register(
																`sections.${s}.fields.${f}.hasCost`
															)}
															type="checkbox"
															className="sr-only peer"
														/>
														<div className="w-11 h-6 bg-default-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-primary rounded-full peer dark:bg-default-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-default-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-default-600 peer-checked:bg-primary"></div>
														<span className="ml-3 text-sm font-medium text-default-900 dark:text-default-300">
															Has cost
														</span>
													</label>

													{section.fields[f]
														.hasCost && (
														<input
															{...register(
																`sections.${s}.fields.${f}.cost`
															)}
															type="number"
															placeholder="Cost in KES"
															className="bg-default-50 border border-default-300 text-default-900 text-sm rounded-lg focus:ring-default-600 focus:border-default-600 block w-2/5 p-2.5 dark:bg-default-700 dark:border-default-600 dark:placeholder-default-400 dark:text-white dark:focus:ring-default-600 dark:focus:border-default-500"
														/>
													)}
												</div>
											</div>
										</div>
									</div>
								))}

								<div className="flex justify-end mt-2">
									<button
										type="button"
										className="border border-default-600 text-default-600 py-2 px-4 rounded-lg"
										onClick={() =>
											setValue(
												"sections",
												productForm.sections.map(
													(sec, seci) => {
														if (seci === s) {
															return {
																...section,
																fields: [
																	...section.fields,
																	...[
																		{
																			name: "",
																			label: "",
																			id: "",
																			type: "text",
																			defaultValue:
																				"",
																			placeholder:
																				"",
																			required:
																				false,
																		},
																	],
																],
															};
														}

														return sec;
													}
												)
											)
										}
									>
										Add field
									</button>
								</div>
							</div>
						))}

						{productForm.sections.length > 2 && (
							<div className="flex justify-end w-full">
								<button
									type="submit"
									className="text-white justify-center bg-primary hover:bg-default-800 focus:ring-4 focus:outline-none focus:ring-default-300 font-medium rounded-lg text-sm px-5 py-3 text-center dark:bg-primary dark:hover:bg-primary dark:focus:ring-default-800"
								>
									Submit form details
								</button>
							</div>
						)}
					</div>
				</form>
			</div>
		</>
	);
}
