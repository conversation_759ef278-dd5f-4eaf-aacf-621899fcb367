import { api } from "@/lib/api";
import { revalidatePath } from "next/cache";
import Link from "next/link";
import FormsCreateForm from "./form";
import { cache } from "react";
import { Metadata } from "next";

const fetchProduct = cache(async (productId: string) => {
	return await api.get<Product>(`products/${productId}`);
});

export const generateMetadata = async ({
	params,
}: {
	params: { productId: string };
}): Promise<Metadata> => {
	const product = await fetchProduct(params.productId);

	return {
		title: `Attach form to ${product?.name}`,
	};
};

export default async function AdminFormsCreate({
	params,
}: {
	params: { productId: string };
}) {
	const createRecord = async (data: FormData) => {
		"use server";

		await api.post(`products/${params.productId}/forms`, data);

		revalidatePath(`/products/${params.productId}/forms`);
	};

	return (
		<div className="p-6 h-full bg-white">
			<FormsCreateForm
				storeProductForm={createRecord}
				productId={params.productId}
			/>
		</div>
	);
}
