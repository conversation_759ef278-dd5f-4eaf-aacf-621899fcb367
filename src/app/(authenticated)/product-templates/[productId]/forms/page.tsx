import PaginatedTable from "@/components/table";
import { api, imagePath as globalImagePath } from "@/lib/api";
import Link from "next/link";
import Image from "next/image";

import { Metadata } from "next";
import { revalidatePath } from "next/cache";
import ProductFormDelete from "./destroy";

export const metadata: Metadata = {
  title: "Product Forms",
  description: "List of forms",
  keywords: ["forms", "list", "forms list"],
};

export default async function ProductFormIndex({
  params,
  searchParams,
}: {
  params: Record<string, string>;
  searchParams: Record<string, string>;
}) {
  const forms = await api.get<PaginatedData<ProductForm>>(
    `products/${params.productId}/forms`,
    searchParams,
  );

  const imagePath = async (path?: string) => {
    "use server";
    return globalImagePath(path);
  };

  const updateRecord = async (productform: FormData) => {
    "use server";

    await api.put(`forms/${productform.get("id")}`, productform);

    revalidatePath(`/products/${params.productId}/forms`);
  };

  const deleteRecord = async (id: string) => {
    "use server";

    await api.destroy(id, "forms");

    revalidatePath(`/products/${params.productId}/forms`);
  };

  return (
    <PaginatedTable<ProductForm>
      records={forms!}
      columns={[
        {
          id: "name",
          title: "Form",
          render: (productform: ProductForm) => (
            <Link
              href={`/products/${params.productId}/forms/${productform.id}`}
              className="flex items-center"
            >
              <div className="user-avatar">
                <Image
                  src={globalImagePath(productform.image?.url)}
                  alt={productform.name}
                  width={40}
                  height={40}
                  className="rounded-sm"
                />
              </div>
              <div className="ms-3">
                <span className="tb-lead">{productform.name}</span>
              </div>
            </Link>
          ),
        },

        {
          id: "details",
          title: "Details",
          render: (productform: ProductForm) => (
            <div
              className="flex flex-col"
              dangerouslySetInnerHTML={{
                __html: productform.details,
              }}
            ></div>
          ),
        },
        {
          id: "actions",
          title: "...",
          render: (productform: ProductForm) => (
            <ul className="flex w-4 items-center space-x-2">
              <li className="flex-1">
                <Link
                  className="flex items-center justify-center text-sm text-primary"
                  href={`/products/${params.productId}/forms/${productform.id}/edit`}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={1.5}
                    stroke="currentColor"
                    className="h-6 w-6"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125"
                    />
                  </svg>
                </Link>
              </li>
              <li>
                <ProductFormDelete
                  id={productform.id}
                  deleteForm={deleteRecord}
                />
              </li>
            </ul>
          ),
        },
      ]}
      path="forms"
      tools={
        <div className="flex items-center justify-between bg-white px-4 pb-4 pt-6 dark:bg-default-900">
          <div className="w-2/3">
            <button
              id="dropdownActionButton"
              data-dropdown-toggle="dropdownAction"
              className="inline-flex items-center rounded-lg border border-default-300 bg-white px-3 py-1.5 text-sm font-medium text-default-500 hover:bg-default-100 focus:outline-none focus:ring-4 focus:ring-default-200 dark:border-default-600 dark:bg-default-800 dark:text-default-400 dark:hover:border-default-600 dark:hover:bg-default-700 dark:focus:ring-default-700"
              type="button"
            >
              <span className="sr-only">Action button</span>
              Action
              <svg
                className="ml-2 h-3 w-3"
                aria-hidden="true"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M19 9l-7 7-7-7"
                ></path>
              </svg>
            </button>
            <div
              id="dropdownAction"
              className="z-10 hidden w-44 divide-y divide-default-100 rounded-lg bg-white shadow dark:divide-default-600 dark:bg-default-700"
            >
              <ul
                className="py-1 text-sm text-default-700 dark:text-default-200"
                aria-labelledby="dropdownActionButton"
              >
                <li>
                  <a
                    href="#"
                    className="block px-4 py-2 hover:bg-default-100 dark:hover:bg-default-600 dark:hover:text-white"
                  >
                    Reward
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="block px-4 py-2 hover:bg-default-100 dark:hover:bg-default-600 dark:hover:text-white"
                  >
                    Promote
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="block px-4 py-2 hover:bg-default-100 dark:hover:bg-default-600 dark:hover:text-white"
                  >
                    Activate account
                  </a>
                </li>
              </ul>
              <div className="py-1">
                <a
                  href="#"
                  className="block px-4 py-2 text-sm text-default-700 hover:bg-default-100 dark:text-default-200 dark:hover:bg-default-600 dark:hover:text-white"
                >
                  Delete User
                </a>
              </div>
            </div>
          </div>
          <div className="flex space-x-2">
            <label htmlFor="table-search" className="sr-only">
              Search
            </label>

            <div className="relative">
              <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                <svg
                  className="h-5 w-5 text-default-500 dark:text-default-400"
                  aria-hidden="true"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fillRule="evenodd"
                    d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                    clipRule="evenodd"
                  ></path>
                </svg>
              </div>
              <input
                type="text"
                id="table-search-users"
                className="block w-80  rounded-lg border border-default-300 bg-default-50 p-3 pl-10 text-sm text-default-900 focus:border-blue-500 focus:ring-blue-500 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
                placeholder="Search for users"
              />
            </div>

            <Link
              className="rounded-lg bg-primary px-6 py-2 text-white"
              href={`/products/${params.productId}/forms/create`}
            >
              Add form
            </Link>
          </div>
        </div>
      }
      title="Forms"
    />
  );
}
