import { api, imagePath } from "@/lib/api";
import { Metadata } from "next";
import Link from "next/link";
import { revalidatePath } from "next/cache";
import PaginatedTable from "@/components/table";
import AdminProductCategoriesEditForm from "./edit";
import Image from "next/image";

const getProductCategory = async (categoryId: string) => {
  return await api.get<ProductCategory>(`product-categories/${categoryId}`);
};

export const generateMetadata = async ({
  params,
}: {
  params: { categoryId: string };
}): Promise<Metadata> => {
  const category = await getProductCategory(params.categoryId);


  return {
    title: category?.name,
  };
};

export default async function AdminProductCategoryIndex({
  params,
}: {
  params: { categoryId: string };
}) {
  const products = await api.get<PaginatedData<Product>>(
    `product-categories/${params.categoryId}/products`,
    { per: 30 },
  );

  const updateProduct = async (product: FormData) => {
    "use server";

    await api.put(`products/${product.get("id")}`, product);

    revalidatePath(`/products/categories/${params.categoryId}`);
  };

  return (
    <PaginatedTable<Product>
      records={products!}
      columns={[
        {
          id: "name",
          title: "Name",
          class: "w-90",
          render: (product: Product) => (
            <Link
              href={`/products/${product.id}`}
              className="flex items-center"
            >
              <Image
                src={imagePath(product.image?.url)}
                alt={product.name}
                width={100}
                height={100}
                className="size-16"
              />
              <div className="ms-3">
                <p className="text-lead">{product.name}</p>
                <div
                  className="truncate text-sm"
                  dangerouslySetInnerHTML={{
                    __html: product.details,
                  }}
                />
              </div>
            </Link>
          ),
        },
        {
          id: "actions",
          class: "osen-tb-col osen-tb-col-tools",
          title: "Actions",
          render: (product: Product) => (
            <div className="flex items-center space-x-2 pr-5">
              <AdminProductCategoriesEditForm
                defaultValues={product}
                updateProduct={updateProduct}
              />

              <Link href={`/products/${product.id}`}>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={1.5}
                  stroke="currentColor"
                  className="h-6 w-6"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"
                  />
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"
                  />
                </svg>
              </Link>
            </div>
          ),
        },
      ]}
      path="/product-templates"
      tools={
        <div className="flex items-center justify-between bg-white px-4 pb-4 pt-6 dark:bg-default-900">
          <div>
            <button
              id="dropdownActionButton"
              data-dropdown-toggle="dropdownAction"
              className="inline-flex items-center rounded-lg border border-default-300 bg-white px-3 py-1.5 text-sm font-medium text-default-500 hover:bg-default-100 focus:outline-none focus:ring-4 focus:ring-default-200 dark:border-default-600 dark:bg-default-800 dark:text-default-400 dark:hover:border-default-600 dark:hover:bg-default-700 dark:focus:ring-default-700"
              type="button"
            >
              <span className="sr-only">Action button</span>
              Action
              <svg
                className="ml-2 h-3 w-3"
                aria-hidden="true"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M19 9l-7 7-7-7"
                ></path>
              </svg>
            </button>
            <div
              id="dropdownAction"
              className="z-10 hidden w-44 divide-y divide-default-100 rounded-lg bg-white shadow dark:divide-default-600 dark:bg-default-700"
            >
              <ul
                className="py-1 text-sm text-default-700 dark:text-default-200"
                aria-labelledby="dropdownActionButton"
              >
                <li>
                  <a
                    href="#"
                    className="block px-4 py-2 hover:bg-default-100 dark:hover:bg-default-600 dark:hover:text-white"
                  >
                    Reward
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="block px-4 py-2 hover:bg-default-100 dark:hover:bg-default-600 dark:hover:text-white"
                  >
                    Promote
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="block px-4 py-2 hover:bg-default-100 dark:hover:bg-default-600 dark:hover:text-white"
                  >
                    Activate account
                  </a>
                </li>
              </ul>
              <div className="py-1">
                <a
                  href="#"
                  className="block px-4 py-2 text-sm text-default-700 hover:bg-default-100 dark:text-default-200 dark:hover:bg-default-600 dark:hover:text-white"
                >
                  Delete User
                </a>
              </div>
            </div>
          </div>
          <div className="flex flex-row justify-between space-x-2">
            <div>
              <label htmlFor="table-search" className="sr-only">
                Search
              </label>
              <div className="relative">
                <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                  <svg
                    className="h-5 w-5 text-default-500 dark:text-default-400"
                    aria-hidden="true"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fillRule="evenodd"
                      d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                      clipRule="evenodd"
                    ></path>
                  </svg>
                </div>
                <input
                  type="text"
                  id="table-search-users"
                  className="block w-80  rounded-lg border border-default-300 bg-default-50 p-3 pl-10 text-sm text-default-900 focus:border-blue-500 focus:ring-blue-500 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
                  placeholder="Search for users"
                />
              </div>
            </div>

            <Link
              href={`/products/create?productCategoryId=${params.categoryId}`}
              className="flex items-center justify-center rounded-lg bg-primary px-6 py-2 text-white hover:bg-default-800 dark:bg-primary"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="currentColor"
                className="bi bi-plus-lg h-5 w-5"
                viewBox="0 0 16 16"
              >
                <path
                  fillRule="evenodd"
                  d="M8 2a.5.5 0 0 1 .5.5v5h5a.5.5 0 0 1 0 1h-5v5a.5.5 0 0 1-1 0v-5h-5a.5.5 0 0 1 0-1h5v-5A.5.5 0 0 1 8 2Z"
                />
              </svg>

              <span className="ml-2">Add Product in Category</span>
            </Link>
          </div>
        </div>
      }
      title="product-templates"
    />
  );
}
