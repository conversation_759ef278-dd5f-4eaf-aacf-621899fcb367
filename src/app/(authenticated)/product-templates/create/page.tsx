import { api } from "@/lib/api";
import ProductForm from "./form";
import { auth } from "@/auth";
import BackBtn from "@/components/back";

export const dynamic = "force-dynamic";
export const fetchCache = "force-no-store";

export const metadata = {
  title: "Create item",
  description: "Create product or service",
  keywords: ["product"],
};

export default async function page({
  searchParams,
}: {
  searchParams: Record<string, string>;
}) {
  const session = await auth();

  const services = await api.get<PaginatedData<Service>>(
    session?.vendor ? `vendors/${session?.vendor?.id}/services` : "services",
    {
      per: 300,
    },
  );

  const loadProductTypes = async (serviceId: string) => {
    "use server";

    return await api.get<PaginatedData<ProductType>>(
      `services/${serviceId}/product-types`,
      {
        per: 30,
      },
    );
  };

  const loadCategories = async (productTypeId: string) => {
    "use server";

    return await api.get<PaginatedData<ProductCategory>>(
      `product-types/${productTypeId}/categories`,
      {},
    );
  };

  const storeProductCategory = async (data: FormData) => {
    "use server";

    return await api.post<ProductCategory>("product-categories", data);
  };

  const storeTag = async (data: FormData) => {
    "use server";

    return await api.post<Tag>("tags", data);
  };

  const storeProduct = async (data: FormData) => {
    "use server";

    if (session?.branch) {
      data.append("branchId", session?.branch?.id);
      data.append(
        "vendorId",
        searchParams.vendorId || session?.branch?.vendorId,
      );
    }

    return await api.post<Product>("product-templates", data);
  };

  return (
    <div className="page-content space-y-6 p-6">
      <BackBtn />
      
      {services && (
        <ProductForm
          storeProductCategory={storeProductCategory}
          storeProduct={storeProduct}
          storeTag={storeTag}
          services={services.data}
          loadProductTypes={loadProductTypes}
          loadProductCategories={loadCategories}
        />
      )}
    </div>
  );
}
