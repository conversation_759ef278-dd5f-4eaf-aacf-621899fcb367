"use client";

import { useForm } from "react-hook-form";
import toast from "react-hot-toast";

export default function EditProduct({
  defaultValues,
  updateRecord,
  vendors,
}: {
  defaultValues: Product;
  updateRecord: (data: Product) => Promise<void>;
  vendors: PaginatedData<Vendor>;
}) {
  const { register, handleSubmit, watch } = useForm<Product>({
    defaultValues,
  });

  const product = watch();

  const onSubmit = async (payload: Product) => {
    toast.promise(updateRecord(payload), {
      loading: "Updating product...",
      success: "Product updated!",
      error: "Error updating product",
    });
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="flex space-x-2">
      <select className="p-2" {...register("vendorId")}>
        <option>Select vendor</option>
        {vendors?.data?.map((vendor) => (
          <option key={vendor.id} value={vendor.id}>
            {vendor.name}
          </option>
        ))}
      </select>

      <button type="submit">Save</button>
    </form>
  );
}
