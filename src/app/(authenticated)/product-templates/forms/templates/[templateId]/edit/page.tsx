import { api } from "@/lib/api";
import AdminFormsEditForm from "./form";
import { revalidatePath } from "next/cache";
import { cache } from "react";

const fetchForm = cache((id: string) =>
  api.get<FormTemplate>(`form-templates/${id}`),
);

export const generateMetadata = async ({
  params: { productId, formId },
}: {
  params: { productId: string; formId: string };
}) => {
  const form = await fetchForm(formId);

  return {
    title: `Edit ${form?.name}`,
  };
};

export default async function page({
  params,
}: {
  params: { productId: string; formId: string };
}) {
  const productForm = await fetchForm(params.formId);

  const updateRecord = async (data: FormData) => {
    "use server";

    await api.put(`form-templates/${params.formId}`, data);

    revalidatePath(`/form-templates/${params.formId}/edit`);
  };

  return (
    <div>
      {productForm && (
        <AdminFormsEditForm
          updateFormTemplate={updateRecord}
          defaultValues={productForm}
        />
      )}
    </div>
  );
}
