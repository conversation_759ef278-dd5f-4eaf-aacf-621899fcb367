import { api } from "@/lib/api";
import { revalidatePath } from "next/cache";
import Link from "next/link";
import FormsCreateForm from "./form";

export const metadata = {
  title: "Create template",
};

export default async function AdminFormsCreate({
  params,
}: {
  params: { productId: string };
}) {
  const createRecord = async (data: FormData) => {
    "use server";

    await api.post(`form-templates`, data);

    revalidatePath(`/form-templates`);
  };

  return (
    <div className="h-full bg-white p-4">
      <div className="flex justify-between px-4">
        <h1 className="text-2xl font-bold">Create template</h1>

        <Link href={`/form-templates`}>
          <span className="rounded-lg bg-primary px-5 py-2 text-white">
            <span>Back to form-templates</span>
          </span>
        </Link>
      </div>

      <FormsCreateForm storeFormTemplate={createRecord} />
    </div>
  );
}
