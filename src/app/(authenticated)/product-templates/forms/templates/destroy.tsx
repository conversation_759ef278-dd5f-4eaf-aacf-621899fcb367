"use client";

import { useForm } from "react-hook-form";

export default function FormTemplateDelete({
  id,
  deleteForm,
}: {
  id: string;
  deleteForm: (id: string) => Promise<void>;
}) {
  const { handleSubmit } = useForm({
    defaultValues: {
      id,
    },
  });

  const onSubmit = async (data: { id: string }) => {
    await deleteForm(data.id);
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <button
        type="submit"
        className="rounded-lg bg-red-400 px-4 py-2 text-white"
      >
        Delete
      </button>
    </form>
  );
}
