import PaginatedTable from "@/components/table";
import { api, imagePath as globalImagePath } from "@/lib/api";
import Link from "next/link";
import Image from "next/image";

import { Metadata } from "next";
import { revalidatePath } from "next/cache";
import FormTemplateDelete from "./destroy";

export const metadata: Metadata = {
  title: "Product Form Templates",
  description: "List of form templates",
  keywords: ["form-templates", "list", "form-templates list"],
};

export default async function FormTemplateIndex({
  searchParams,
}: {
  searchParams: Record<string, string>;
}) {
  const templates = await api.get<PaginatedData<FormTemplate>>(
    `form-templates`,
    searchParams,
  );

  const updateRecord = async (productform: FormData) => {
    "use server";

    await api.put(`form-templates/${productform.get("id")}`, productform);

    revalidatePath(`/products/forms/templates`);
  };

  const deleteRecord = async (id: string) => {
    "use server";

    await api.destroy(id, "form-templates");

    revalidatePath(`/products/forms/templates`);
  };

  return (
    <PaginatedTable<FormTemplate>
      records={templates!}
      columns={[
        {
          id: "name",
          title: "Template",
        },

        {
          id: "details",
          title: "Details",
          render: (productform: FormTemplate) => (
            <div
              className="flex flex-col"
              dangerouslySetInnerHTML={{
                __html: productform.details,
              }}
            ></div>
          ),
        },
        {
          id: "actions",
          title: "...",
          render: (productform: FormTemplate) => (
            <ul className="flex w-4 items-center space-x-2">
              <li className="flex-1">
                <Link
                  className="rounded-lg bg-primary px-5 py-3 text-sm font-medium text-white hover:bg-default-800 focus:outline-none focus:ring-4 focus:ring-default-300 dark:bg-primary dark:hover:bg-primary dark:focus:ring-default-800"
                  href={`/form-templates/${productform.id}/edit`}
                >
                  Edit
                </Link>
              </li>
              <li>
                <FormTemplateDelete
                  id={productform.id}
                  deleteForm={deleteRecord}
                />
              </li>
            </ul>
          ),
        },
      ]}
      path="form-templates"
      tools={
        <div className="flex items-center justify-between bg-white px-4 pb-4 pt-6 dark:bg-default-900">
          <div className="w-2/3">
            <button
              id="dropdownActionButton"
              data-dropdown-toggle="dropdownAction"
              className="inline-flex items-center rounded-lg border border-default-300 bg-white px-3 py-1.5 text-sm font-medium text-default-500 hover:bg-default-100 focus:outline-none focus:ring-4 focus:ring-default-200 dark:border-default-600 dark:bg-default-800 dark:text-default-400 dark:hover:border-default-600 dark:hover:bg-default-700 dark:focus:ring-default-700"
              type="button"
            >
              <span className="sr-only">Action button</span>
              Action
              <svg
                className="ml-2 h-3 w-3"
                aria-hidden="true"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M19 9l-7 7-7-7"
                ></path>
              </svg>
            </button>
            <div
              id="dropdownAction"
              className="z-10 hidden w-44 divide-y divide-default-100 rounded-lg bg-white shadow dark:divide-default-600 dark:bg-default-700"
            >
              <ul
                className="py-1 text-sm text-default-700 dark:text-default-200"
                aria-labelledby="dropdownActionButton"
              >
                <li>
                  <a
                    href="#"
                    className="block px-4 py-2 hover:bg-default-100 dark:hover:bg-default-600 dark:hover:text-white"
                  >
                    Reward
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="block px-4 py-2 hover:bg-default-100 dark:hover:bg-default-600 dark:hover:text-white"
                  >
                    Promote
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="block px-4 py-2 hover:bg-default-100 dark:hover:bg-default-600 dark:hover:text-white"
                  >
                    Activate account
                  </a>
                </li>
              </ul>
              <div className="py-1">
                <a
                  href="#"
                  className="block px-4 py-2 text-sm text-default-700 hover:bg-default-100 dark:text-default-200 dark:hover:bg-default-600 dark:hover:text-white"
                >
                  Delete User
                </a>
              </div>
            </div>
          </div>
          <div className="flex space-x-2">
            <label htmlFor="table-search" className="sr-only">
              Search
            </label>

            <div className="relative">
              <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                <svg
                  className="h-5 w-5 text-default-500 dark:text-default-400"
                  aria-hidden="true"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fillRule="evenodd"
                    d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                    clipRule="evenodd"
                  ></path>
                </svg>
              </div>
              <input
                type="text"
                id="table-search-users"
                className="block w-80  rounded-lg border border-default-300 bg-default-50 p-3 pl-10 text-sm text-default-900 focus:border-blue-500 focus:ring-blue-500 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
                placeholder="Search for users"
              />
            </div>

            <Link
              className="rounded-lg bg-primary px-6 py-2 text-white"
              href={`/products/forms/templates/create`}
            >
              Add template
            </Link>
          </div>
        </div>
      }
      title="Forms"
    />
  );
}
