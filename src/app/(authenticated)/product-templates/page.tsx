import { api } from "@/lib/api";
import { auth } from "@/auth";

import { revalidatePath } from "next/cache";
import { Metadata } from "next";

import ProductsTable from "@/components/tables/product-templates-table";

export const metadata: Metadata = {
  title: "Product & Service Template Listing",
  description: "List of products",
  keywords: ["product-templates", "list", "products list"],
};

export default async function ProductIndex({
  searchParams,
}: {
  searchParams: Record<string, string>;
}) {
  const session = await auth();

  const products = await api.get<PaginatedData<Product>>("product-templates", searchParams);

  const loadVendors = async (s: string) => {
    "use server";

    if (s.length > 3) {
      const res: Vendor[] = await fetch(`/api/vendors?s=${s}`).then((r) => r.json());

      return res
    }

    return [];
  };

  const createRecord = async (data: FormData) => {
    "use server";

    await api.post("product-templates", data);

    revalidatePath("/product-templates");
  };

  const updateRecord = async (product: Product) => {
    "use server";

    await api.put(`products/${product.id}`, product);

    revalidatePath("/product-templates");
  };

  const deleteRecord = async (product: FormData) => {
    "use server";

    await api.destroy(product.get("id") as string, "product-templates");

    revalidatePath("/product-templates");
  };

  return (
    <div className="bg-white p-10 dark:bg-default-900">
      <ProductsTable
        data={products?.data ?? []}
        meta={products?.meta!}
        action={deleteRecord}
        session={session}
        updateRecord={updateRecord}
        loadVendors={loadVendors}
      />
    </div>
  );
}
