import { api, imagePath } from "@/lib/api";
import { Metadata } from "next";
import Link from "next/link";
import AdminProductCategoriesCreateForm from "./create";
import AdminProductCategoriesEditForm from "./edit";
import { revalidatePath } from "next/cache";
import Image from "next/image";

const getSpeciality = async (specialityId: string) => {
  return await api.get<Speciality>(`specialities/${specialityId}`);
};

export const generateMetadata = async ({
  params,
}: {
  params: { typeId: string };
}): Promise<Metadata> => {
  const speciality = await getSpeciality(params.typeId);

  return {
    title: speciality?.name,
  };
};

export default async function AdminSpecialityIndex({
  params,
}: {
  params: { typeId: string };
}) {
  const categories = await api.get<PaginatedData<Speciality>>(
    `specialities/${params.typeId}/categories`,
    { per: 30 },
  );

  const storeSpeciality = async (category: FormData) => {
    "use server";

    await api.post("product-categories", category);
    revalidatePath("/products/categories");
  };

  const updateSpeciality = async (category: FormData) => {
    "use server";

    await api.put(`product-categories/${category.get("id")}`, category);
    revalidatePath("/products/categories");
  };

  return (
    <div className="flex flex-1 flex-col px-4 pt-8">
      <div className="grid flex-1 grid-cols-5 gap-3">
        {categories?.data.map((category) => (
          <div
            className="rounded-md border border-default-200 p-4"
            key={category.id}
          >
            <div className="flex space-x-1">
              <Link href={`/products/categories/${category.id}`}>
                <Image
                  src={imagePath(category.image?.url)}
                  alt={category.name}
                  width={100}
                  height={100}
                  className="size-16 rounded-full"
                />
              </Link>
              <div className="w-3/5">
                <div className="flex justify-end">
                  <AdminProductCategoriesEditForm
                    defaultValues={category}
                    updateSpeciality={updateSpeciality}
                  />
                </div>

                <Link href={`/products/categories/${category.id}`}>
                  <h3>{category.name}</h3>
                </Link>
              </div>
            </div>

            <article>{category.details}</article>
          </div>
        ))}
      </div>

      <AdminProductCategoriesCreateForm
        storeSpeciality={storeSpeciality}
        typeId={params.typeId}
      />
    </div>
  );
}
