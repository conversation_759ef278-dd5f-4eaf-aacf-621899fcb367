"use client";

import { useParams } from "next/navigation";
import AsyncSelect from "react-select/async";

export default function ProductCategoriesearchForm() {
  const params = useParams();

  const loadCategories = async (s: string) => {
    if (s.length < 3) return [];

    const response: ProductCategory[] = await fetch(
      `/api/product-types/${params.typeId}/categories?s=${s}`,
    ).then((res) => res.json());

    return response;
  };

  return (
    <AsyncSelect
      className="w-1/3"
      classNames={{
        control: () =>
          "!border !rounded-lg !border-default-200 px-4 dark:bg-default-50",
      }}
      placeholder="Search in product categories"
      loadOptions={loadCategories}
      getOptionLabel={(option) => option.name}
      getOptionValue={(option) => option.id}
    />
  );
}
