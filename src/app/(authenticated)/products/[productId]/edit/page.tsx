import { api, imagePath } from "@/lib/api";
import ProductsEditForm from "./form";
import { revalidatePath } from "next/cache";
import { cache } from "react";
import { auth } from "@/auth";

interface User {
	id: string;
	title: string;
	firstName: string;
	lastName: string;
	gender: string | null;
	dob: string | null;
	email: string;
	phone: string;
	idpass: string;
	rememberMeToken: string | null;
	details: string | null;
	location: Record<string, any> | null;
	geom: string | null;
	avatar: AttachmentContract | null;
	createdAt: string;
	updatedAt: string;
	name: string;
	status: string;
	avatarUrl: string;
	initials: string;
	devices: Device[];
	roles: Role[];
	permissions: Permission[];
	notifications: DatabaseNotification[];

	// computed
	identifier: string;
	online: boolean;
	vendorId: string;
}


const fetchProduct = cache((productId: string) =>
  api.get<Product>(`products/${productId}`),
);

export const generateMetadata = async ({
  params: { productId },
}: {
  params: { productId: string };
}) => {


  const product = await fetchProduct(productId);

  return {
    title: product?.name,
    description: product?.details,
    keywords: ["products", "list", "products list"],
  };
};

export default async function page({
  params: { productId },
}: {
  params: { productId: string };
}) {
  const product = await fetchProduct(productId);

  const session = await auth();
 
  const loggedInUserId = session?.user?.id;

  // const loggedInUserRole = session?.user?.roles[0]?.name;
  

  const userDetail: unknown = session?.user ?  await api.get(`/users/${loggedInUserId}`) : "";

  const userDetails = userDetail as User;



  const storeTag = async (data: FormData) => {
    "use server";

    return await api.post<Tag>("tags", data);
  };

  const updateRecord = async (product: FormData) => {
    "use server";

    await api.put(`products/${productId}`, product);

    revalidatePath(`/products/${productId}/edit`);
  };

  const loadVendors = async (s: string) => {
    "use server";

    if (s.length > 3) {
      const res = await api.get<PaginatedData<Vendor>>("vendors", { s });

      return res?.data ?? [];
    }

    return [];
  };

  const loadBranches = async (s: string, vendorId: string) => {
    "use server";

    if (s.length > 3) {
      const res = await api.get<PaginatedData<Branch>>(
        `vendors/${vendorId}/branches`,
        { s },
      );

      return res?.data ?? [];
    }

    return [];
  };

  return (
    <div className="p-4">
      {product && (
        <ProductsEditForm
          userDetails={userDetails}
          defaultValues={product}
          storeProduct={updateRecord}
          storeTag={storeTag}
          loadVendors={loadVendors}
          loadBranches={loadBranches}
          branch={session?.branch}
        />
      )}
    </div>
  );
}
