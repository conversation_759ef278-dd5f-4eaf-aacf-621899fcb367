import FormPreview from "../../../../../../components/form-preview";
import { api, imagePath } from "@/lib/api";
import Image from "next/image";
import Link from "next/link";
import { cache } from "react";

const fetchForm = cache((id: string) => api.get<ProductForm>(`forms/${id}`));

export const generateMetadata = async ({
  params: { productId, formId },
}: {
  params: { productId: string; formId: string };
}) => {
  const form = await fetchForm(formId);

  return {
    title: form?.name,
    description: form?.details,
  };
};

export default async function page({
  params,
}: {
  params: { productId: string; formId: string };
}) {
  const productForm = await fetchForm(params.formId);

  return (
    <section className="p-4">
      <div className="mb-4 flex items-center justify-between">
        <h1 className="text-base">{productForm?.details}</h1>

        <Link
          href={`/products/${params.productId}/forms/${params.formId}/edit`}
          className="rounded-lg bg-primary px-5 py-2 text-white hover:underline"
        >
          Edit
        </Link>
      </div>

      <div className="grid grid-cols-1 gap-5 lg:grid-cols-5">
        <div className="lg:col-span-3">
          {productForm && (
            <FormPreview sections={Object.values(productForm.sections)} />
          )}
        </div>

        <div className="lg:col-span-2">
          <div className="w-full overflow-hidden rounded-lg">
            <Image
              className="h-full w-full object-cover"
              src={imagePath(productForm?.image?.url)}
              width={100}
              height={100}
              alt=""
            />
          </div>
        </div>
      </div>
    </section>
  );
}
