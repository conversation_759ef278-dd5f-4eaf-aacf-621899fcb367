import { api } from "@/lib/api";
import { revalidatePath } from "next/cache";
import FormsCreateForm from "./form";
import { cache } from "react";
import { Metadata } from "next";

const fetchProduct = cache(async (productId: string) => {
	return await api.get<Product>(`products/${productId}`);
});

export const generateMetadata = async ({
	params,
}: {
	params: { productId: string };
}): Promise<Metadata> => {
	const product = await fetchProduct(params.productId);

	return {
		title: `Attach form to ${product?.name}`,
	};
};

export default async function AdminFormsCreate({
	params,
}: {
	params: { productId: string };
}) {
	const createRecord = async (data: FormData) => {
		"use server";

		return await api.post<ProductForm>(`products/${params.productId}/forms`, data);
	};

	return (
		<div className="p-6 h-full bg-white relative">
			<FormsCreateForm
				storeProductForm={createRecord}
				productId={params.productId}
			/>
		</div>
	);
}
