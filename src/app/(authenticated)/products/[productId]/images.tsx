"use client";

import Image from "next/image";
import { Swiper, SwiperSlide } from "swiper/react";

// Import Swiper styles
import "swiper/css";
import { imagePath } from "@/lib/api";

export default function ProductImages({ product }: { product: Product }) {
  return (
    <div className="grid grid-cols-1">
      <div>
        <Swiper slidesPerView={1}>
          <SwiperSlide>
            <Image
              width="500"
              height="430"
              alt={product.name}
              src={imagePath(product.image?.url)}
              className="mx-auto h-full w-full"
            />
          </SwiperSlide>
        </Swiper>
      </div>

      <Swiper spaceBetween={50} slidesPerView={3}>
        {product.gallery?.map((i) => (
          <SwiperSlide
            className="!h-24 !w-24 cursor-pointer lg:!h-32 lg:!w-32 "
            key={i.name}
          >
            <Image
              width="124"
              height="124"
              alt={i.name}
              src={imagePath(i.url)}
              className="h-full w-full rounded"
            />
          </SwiperSlide>
        ))}
      </Swiper>
    </div>
  );
}
