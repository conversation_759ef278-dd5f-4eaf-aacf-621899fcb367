import { api, imagePath } from "@/lib/api";
import { cache } from "react";
import ProductImages from "./images";
import Link from "next/link";
import Image from "next/image";

const fetchProduct = cache(async (productId: string) => {
  return await api.get<Product>(`products/${productId}`);
});

export const generateMetadata = async ({
  params: { productId },
}: {
  params: { productId: string };
}) => {
  const product = await fetchProduct(productId);

  return {
    title: product?.name,
    description: product?.details,
  };
};

export default async function Product({
  params: { productId },
}: {
  params: { productId: string };
}) {
  const product = await fetchProduct(productId);

  return (
    <div className="page-content mb-6 space-y-6 p-6">
      {product && (
        <>
          <div className="flex w-full items-center justify-between">
            <ol
              aria-label="Breadcrumb"
              className="hidden min-w-0 items-center gap-2 whitespace-nowrap md:flex"
            >
              {product.service?.task && (
                <li className="text-sm">
                  <Link
                    href={`/tasks/${product.service?.taskId}`}
                    className="flex items-center gap-2 align-middle text-default-800 transition-all hover:text-default-500"
                  >
                    {product.service?.task?.name}
                    <svg
                      stroke="currentColor"
                      fill="none"
                      stroke-width="2"
                      viewBox="0 0 24 24"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      height="16"
                      width="16"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="m9 18 6-6-6-6"></path>
                    </svg>
                  </Link>
                </li>
              )}

              <li className="text-sm">
                <Link
                  href={`/services/${product.serviceId}`}
                  className="flex items-center gap-2 align-middle text-default-800 transition-all hover:text-default-500"
                >
                  {product.service?.name}
                  <svg
                    stroke="currentColor"
                    fill="none"
                    stroke-width="2"
                    viewBox="0 0 24 24"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    height="16"
                    width="16"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path d="m9 18 6-6-6-6"></path>
                  </svg>
                </Link>
              </li>

              {product.category?.productType && (
                <li aria-current="page" className="text-sm">
                  <Link
                    href={`/products/types/${product.category?.productType?.id}`}
                    className="flex items-center gap-2 align-middle text-default-800 transition-all hover:text-default-500"
                  >
                    {product.category?.productType?.name}
                    <svg
                      stroke="currentColor"
                      fill="none"
                      stroke-width="2"
                      viewBox="0 0 24 24"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      height="16"
                      width="16"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="m9 18 6-6-6-6"></path>
                    </svg>
                  </Link>
                </li>
              )}
              <li
                aria-current="page"
                className="truncate text-sm font-medium text-primary hover:text-default-500"
              >
                <Link href={`products/categories/${product.category?.id}`}>
                  {product.category?.name}
                </Link>
              </li>
            </ol>
            <Link
              href={`/products/${productId}/edit`}
              className="rounded-lg bg-primary px-5 py-2 text-white"
            >
              Edit
            </Link>
          </div>
          <div className="grid gap-6 lg:grid-cols-2">
            <div className="rounded-l">
              <ProductImages product={product} />
            </div>
            <div className="rounded-lg border border-default-200 p-6">
              <div>
                <div className="mb-1 flex flex-wrap items-end justify-between font-medium text-default-800">
                  <h4 className="text-4xl">KES {product.price}</h4>
                </div>
                <h5 className="mb-2 text-lg font-medium text-default-600">
                  <span className="text-base font-normal text-default-500">
                    by{" "}
                  </span>
                  {product.vendor?.name} ({product.branch?.name})
                </h5>
                <div className="mb-3 flex items-center gap-3">
                  <div className="flex gap-1.5">
                    <svg
                      stroke="currentColor"
                      fill="currentColor"
                      stroke-width="0"
                      viewBox="0 0 576 512"
                      className="text-yellow-400"
                      height="18"
                      width="18"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"></path>
                    </svg>
                    <svg
                      stroke="currentColor"
                      fill="currentColor"
                      stroke-width="0"
                      viewBox="0 0 576 512"
                      className="text-yellow-400"
                      height="18"
                      width="18"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"></path>
                    </svg>
                    <svg
                      stroke="currentColor"
                      fill="currentColor"
                      stroke-width="0"
                      viewBox="0 0 576 512"
                      className="text-yellow-400"
                      height="18"
                      width="18"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"></path>
                    </svg>
                    <svg
                      stroke="currentColor"
                      fill="currentColor"
                      stroke-width="0"
                      viewBox="0 0 576 512"
                      className="text-yellow-400"
                      height="18"
                      width="18"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"></path>
                    </svg>
                    <svg
                      stroke="currentColor"
                      fill="currentColor"
                      stroke-width="0"
                      viewBox="0 0 640 512"
                      className="text-yellow-400"
                      height="18"
                      width="18"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="M320 376.4l.1-.1 26.4 14.1 85.2 45.5-16.5-97.6-4.8-28.7 20.7-20.5 70.1-69.3-96.1-14.2-29.3-4.3-12.9-26.6L320.1 86.9l-.1 .3V376.4zm175.1 98.3c2 12-3 24.2-12.9 31.3s-23 8-33.8 2.3L320.1 439.8 191.8 508.3C181 514 167.9 513.1 158 506s-14.9-19.3-12.9-31.3L169.8 329 65.6 225.9c-8.6-8.5-11.7-21.2-7.9-32.7s13.7-19.9 25.7-21.7L227 150.3 291.4 18c5.4-11 16.5-18 28.8-18s23.4 7 28.8 18l64.3 132.3 143.6 21.2c12 1.8 22 10.2 25.7 21.7s.7 24.2-7.9 32.7L470.5 329l24.6 145.7z"></path>
                    </svg>
                  </div>
                  <div className="h-4 w-px bg-default-400"></div>
                  <h5 className="text-sm text-default-500">1221 Reviews</h5>
                </div>

                <article
                  className="prose mb-4 text-default-700"
                  dangerouslySetInnerHTML={{
                    __html: product.details,
                  }}
                />

                <div className="mb-5 flex gap-2">
                  <div className="flex items-center gap-2.5 rounded-full border border-default-200 px-3 py-1.5">
                    <svg
                      stroke="currentColor"
                      fill="currentColor"
                      stroke-width="0"
                      viewBox="0 0 24 24"
                      height="1em"
                      width="1em"
                      color="green"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="M20 3H4a1 1 0 0 0-1 1v16a1 1 0 0 0 1 1h16a1 1 0 0 0 1-1V4a1 1 0 0 0-1-1zm-1 16H5V5h14v14z"></path>
                      <circle cx="12" cy="12" r="5"></circle>
                    </svg>
                    <span className="text-xs">
                      {product.stock} {product.availability}
                    </span>
                  </div>
                  <div className="flex flex-wrap space-x-2">
                    {product.tags.map((tag) => (
                      <div
                        key={tag.id}
                        className="flex items-center rounded-full border border-default-200 px-3 py-1.5"
                      >
                        <span className="text-xs">{tag.name}</span>
                      </div>
                    ))}
                  </div>
                  <div className="flex items-center rounded-full border border-default-200 px-3 py-1.5">
                    <span className="text-xs">{product.condition}</span>
                  </div>
                </div>
                <div className="mb-8 flex items-center gap-3">
                  <h4 className="text-sm text-default-700">Size :</h4>
                  <div>
                    <input
                      type="radio"
                      name="size-options"
                      id="S0"
                      className="peer hidden"
                      value="S"
                      checked
                    />
                    <label
                      htmlFor="S0"
                      className="flex h-9 w-9 cursor-pointer select-none items-center justify-center rounded-full bg-default-200 text-center text-sm peer-checked:bg-primary peer-checked:text-white"
                    >
                      S
                    </label>
                  </div>
                  <div>
                    <input
                      type="radio"
                      name="size-options"
                      id="M1"
                      className="peer hidden"
                      value="M"
                      checked
                    />
                    <label
                      htmlFor="M1"
                      className="flex h-9 w-9 cursor-pointer select-none items-center justify-center rounded-full bg-default-200 text-center text-sm peer-checked:bg-primary peer-checked:text-white"
                    >
                      M
                    </label>
                  </div>
                  <div>
                    <input
                      type="radio"
                      name="size-options"
                      id="L2"
                      className="peer hidden"
                      value="L"
                      checked
                    />
                    <label
                      htmlFor="L2"
                      className="flex h-9 w-9 cursor-pointer select-none items-center justify-center rounded-full bg-default-200 text-center text-sm peer-checked:bg-primary peer-checked:text-white"
                    >
                      L
                    </label>
                  </div>
                </div>
                <div className="mb-6 hidden">
                  <h4 className="mb-4 text-lg font-medium text-default-700">
                    Product data
                    <span className="text-sm text-default-400">
                      (per serving)
                    </span>
                  </h4>
                  <div className="rounded-lg border border-default-200 p-3">
                    <div className="grid grid-cols-4 justify-center">
                      <div className="text-center">
                        <h4 className="mb-1 text-base font-medium text-default-700">
                          564
                        </h4>
                        <h4 className="text-base text-default-700">Calories</h4>
                      </div>
                      <div className="text-center">
                        <h4 className="mb-1 text-base font-medium text-default-700">
                          306mg
                        </h4>
                        <h4 className="text-base text-default-700">Fat</h4>
                      </div>
                      <div className="text-center">
                        <h4 className="mb-1 text-base font-medium text-default-700">
                          2gm
                        </h4>
                        <h4 className="text-base text-default-700">Carbs</h4>
                      </div>
                      <div className="text-center">
                        <h4 className="mb-1 text-base font-medium text-default-700">
                          6.5gm
                        </h4>
                        <h4 className="text-base text-default-700">Protein</h4>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="flex items-center">
                  <svg
                    stroke="currentColor"
                    fill="none"
                    stroke-width="2"
                    viewBox="0 0 24 24"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    className="me-2 text-primary"
                    height="20"
                    width="20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path>
                    <circle cx="12" cy="12" r="3"></circle>
                  </svg>
                  <h5 className="text-sm text-default-600">
                    <span className="font-semibold text-primary">152</span>
                    &nbsp; People are viewing this right now
                  </h5>
                </div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-4 justify-center gap-4">
            <div className="rounded-lg border border-default-200 py-5 text-center">
              <h4 className="mb-1 text-base font-medium text-default-700">
                {product.availability}
              </h4>
              <h4 className="text-base text-default-700">Availability</h4>
            </div>
            <div className="rounded-lg border border-default-200 py-5 text-center">
              <h4 className="mb-1 text-base font-medium text-default-700">
                {product.condition}
              </h4>
              <h4 className="text-base text-default-700">Condition</h4>
            </div>
            <div className="rounded-lg border border-default-200 py-5 text-center">
              <h4 className="mb-1 text-base font-medium text-default-700">
                {product.stock}
              </h4>
              <h4 className="text-base text-default-700">Stock</h4>
            </div>
            <div className="rounded-lg border border-default-200 py-5 text-center">
              <h4 className="mb-1 text-base font-medium text-default-700">
                {product.ref}
              </h4>
              <h4 className="text-base text-default-700">Reference</h4>
            </div>
          </div>

          {product.accompaniments?.length > 0 && (
            <>
              <div className="flex justify-between">
                <h1>Accompaniments</h1>
              </div>

              <div className="grid grid-cols-5 gap-5">
                {product.accompaniments?.map((accompaniment) => (
                  <Link
                    href={`/products/${accompaniment.id}`}
                    className="flex items-center space-x-4 rounded-lg bg-default-100 shadow-lg"
                    key={accompaniment.id}
                  >
                    <Image
                      src={imagePath(accompaniment.image?.url)}
                      alt={accompaniment.name}
                      width={100}
                      height={100}
                      className="h-24 w-24 rounded-lg py-4"
                    />

                    <div className="flex flex-col space-y-2">
                      <h1>{accompaniment.name}</h1>
                      <article
                        dangerouslySetInnerHTML={{
                          __html: accompaniment.details,
                        }}
                      />
                    </div>
                  </Link>
                ))}
              </div>
            </>
          )}

          {product.upsells?.length > 0 && (
            <>
              <div className="flex justify-between">
                <h1>Upsells</h1>
              </div>

              <div className="grid grid-cols-5 gap-5">
                {product.upsells?.map((upsell) => (
                  <Link
                    href={`/products/${upsell.id}`}
                    className="flex items-center space-x-4 rounded-lg bg-default-100 shadow-lg"
                    key={upsell.id}
                  >
                    <Image
                      src={imagePath(upsell.image?.url)}
                      alt={upsell.name}
                      width={100}
                      height={100}
                      className="h-24 w-24 rounded-lg py-4"
                    />

                    <div className="flex flex-col space-y-2">
                      <h1>{upsell.name}</h1>
                      <article
                        dangerouslySetInnerHTML={{
                          __html: upsell.details,
                        }}
                      />
                    </div>
                  </Link>
                ))}
              </div>
            </>
          )}
        </>
      )}
    </div>
  );
}
