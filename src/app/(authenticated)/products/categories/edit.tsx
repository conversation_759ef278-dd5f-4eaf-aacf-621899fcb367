"use client";

import { useState, useRef, ChangeEvent, useEffect } from "react";
import { SubmitHandler, useForm } from "react-hook-form";
import { toast } from "react-hot-toast";
import Image from "next/image";
import { useClickOutside } from "@/hooks/useClickOutside";

export default function AdminProductCategoryEditForm({
  defaultValues,
  updateProductCategory,
}: {
  defaultValues: ProductCategory;
  updateProductCategory: (data: FormData) => Promise<void>;
}) {
  const [editing, setEditing] = useState(false);
  const [preview, setPreview] = useState<string>();

  const ref = useClickOutside(() => setEditing(false));

  const {
    watch,
    handleSubmit,
    register,
    reset,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<Partial<ProductCategory & { upload: File }>>({
    defaultValues,
  });

  const productcategory = watch();

  const handleUploadedFile = (event: ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;

    if (files) {
      const file = files[0];
      setValue("upload", file);
      const urlImage = URL.createObjectURL(file);

      setPreview(urlImage);
    }
  };

  const editProductCategory: SubmitHandler<
    Partial<ProductCategory & { upload: File }>
  > = (productcategory: Partial<ProductCategory & { upload: File }>) => {
    const data = new FormData();

    data.append("id", productcategory.id!);
    data.append("name", productcategory.name!);
    data.append("details", productcategory.details!);
    data.append("image", productcategory.upload!);

    toast
      .promise(
        updateProductCategory(data),
        {
          loading: "Updating productcategory...",
          success: "ProductCategory has been saved 👌",
          error: "Could not save productcategory 🤯",
        },
        {
          position: "bottom-center",
        },
      )
      .then(() => {
        reset({
          name: "",
          image: null,
          details: "",
        });

        setEditing(false);
      });
  };

  useEffect(() => {
    // const productcategoryImage = imagePath(defaultValues.image?.url, defaultValues.imageUrl)
    // setPreview(productcategoryImage)
  }, []);

  return (
    <>
      <button
        onClick={() => setEditing(!editing)}
        id="editProductCategoryButton"
        className="z-5 flex items-center justify-center rounded-lg bg-primary px-5 py-2 text-sm font-medium text-white hover:bg-default-800 focus:outline-none focus:ring-4 focus:ring-default-300 dark:bg-primary dark:hover:bg-primary dark:focus:ring-default-800"
        type="button"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          fill="currentColor"
          className="bi bi-pencil-fill"
          viewBox="0 0 16 16"
        >
          <path d="M12.854.146a.5.5 0 0 0-.707 0L10.5 1.793 14.207 5.5l1.647-1.646a.5.5 0 0 0 0-.708l-3-3zm.646 6.061L9.793 2.5 3.293 9H3.5a.5.5 0 0 1 .5.5v.5h.5a.5.5 0 0 1 .5.5v.5h.5a.5.5 0 0 1 .5.5v.5h.5a.5.5 0 0 1 .5.5v.207l6.5-6.5zm-7.468 7.468A.5.5 0 0 1 6 13.5V13h-.5a.5.5 0 0 1-.5-.5V12h-.5a.5.5 0 0 1-.5-.5V11h-.5a.5.5 0 0 1-.5-.5V10h-.5a.499.499 0 0 1-.175-.032l-.179.178a.5.5 0 0 0-.11.168l-2 5a.5.5 0 0 0 .65.65l5-2a.5.5 0 0 0 .168-.11l.178-.178z" />
        </svg>
      </button>

      {editing && (
        <div
          ref={ref}
          id="drawer-edite-productcategory-default"
          className="fixed right-0 top-0 z-40 h-screen w-full max-w-xs overflow-y-auto bg-white p-4 transition-transform dark:bg-default-800"
          tabIndex={-1}
          aria-labelledby="drawer-label"
          aria-hidden="true"
        >
          <h5
            id="drawer-label"
            className="mb-6 inline-flex items-center text-sm font-semibold uppercase text-default-500 dark:text-default-400"
          >
            {productcategory.name}
          </h5>
          <button
            onClick={() => setEditing(!editing)}
            type="button"
            data-drawer-dismiss="drawer-edite-productcategory-default"
            aria-controls="drawer-edite-productcategory-default"
            className="absolute right-2.5 top-2.5 inline-flex items-center rounded-lg bg-transparent p-1.5 text-sm text-default-400 hover:bg-default-200 hover:text-default-900 dark:hover:bg-default-600 dark:hover:text-white"
          >
            <svg
              aria-hidden="true"
              className="h-5 w-5"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fillRule="evenodd"
                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                clipRule="evenodd"
              ></path>
            </svg>
            <span className="sr-only">Close menu</span>
          </button>

          <form onSubmit={handleSubmit(editProductCategory)}>
            <div className="space-y-4">
              <div>
                <label
                  htmlFor="name"
                  className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
                >
                  Name
                </label>
                <input
                  type="text"
                  {...register("name")}
                  id="name"
                  className="block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
                  placeholder="Type productcategory name"
                  required
                />
              </div>

              <div>
                <label
                  htmlFor="image"
                  className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
                >
                  Image or icon
                </label>
                <label className="flex h-40 w-full items-center justify-center rounded-lg border border-dashed border-default-300 bg-default-50 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600">
                  <input
                    type="file"
                    name="image"
                    id="image"
                    className="hidden"
                    onChange={handleUploadedFile}
                  />
                  {preview ? (
                    <Image
                      src={preview}
                      alt="preview"
                      width={100}
                      height={100}
                      className="w-1/2"
                    />
                  ) : (
                    <p>Click to select file</p>
                  )}
                </label>
              </div>

              <div>
                <label
                  htmlFor="description"
                  className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
                >
                  Description
                </label>
                <textarea
                  id="description"
                  rows={4}
                  {...register("details")}
                  className=".5 block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
                  placeholder="Enter productcategory description here"
                />
              </div>

              <div className="bottom-0 left-0 flex w-full justify-center space-x-4 pb-4 md:absolute md:px-4">
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full justify-center rounded-lg bg-primary px-5 py-3 text-center text-sm font-medium text-white hover:bg-default-800 focus:outline-none focus:ring-4 focus:ring-default-300 dark:bg-primary dark:hover:bg-primary dark:focus:ring-default-800"
                >
                  Update product type details
                </button>
              </div>
            </div>
          </form>
        </div>
      )}
    </>
  );
}
