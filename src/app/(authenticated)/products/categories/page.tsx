import { api, imagePath } from "@/lib/api";
import { Metadata } from "next";
import Link from "next/link";
import AdminProductCategoryCreateForm from "./create";
import AdminProductCategoryEditForm from "./edit";
import { revalidatePath } from "next/cache";
import Image from "next/image";

export const dynamic = "force-dynamic";
export const fetchCache = "force-no-store";

export const metadata: Metadata = {
  title: "Product Types",
};

export default async function AdminProductCategoryIndex() {
  const productcategory = await api.get<PaginatedData<ProductCategory>>(
    "product-categories",
    { per: 30 },
  );

  const storeProductCategory = async (productcategory: FormData) => {
    "use server";

    await api.post("product-categories", productcategory);
    revalidatePath("/products/types");
  };

  const updateProductCategory = async (productcategory: FormData) => {
    "use server";

    await api.put(
      `product-categories/${productcategory.get("id")}`,
      productcategory,
    );
    revalidatePath("/products/types");
  };

  return (
    <div className="flex flex-1 flex-col px-4 pt-8">
      <div className="grid flex-1 grid-cols-5 gap-3">
        {productcategory?.data.map((productcategory) => (
          <div
            className="rounded-md border border-default-200 p-4"
            key={productcategory.id}
          >
            <div className="flex space-x-1">
              <Link href={`/products/types/${productcategory.id}`}>
                <Image
                  src={imagePath(productcategory.image?.url)}
                  alt={productcategory.name}
                  width={100}
                  height={100}
                  className="size-16 rounded-full"
                />
              </Link>
              <div className="w-3/5">
                <div className="flex justify-end">
                  <AdminProductCategoryEditForm
                    defaultValues={productcategory}
                    updateProductCategory={updateProductCategory}
                  />
                </div>

                <Link href={`/products/types/${productcategory.id}`}>
                  <h3>{productcategory.name}</h3>
                </Link>
              </div>
            </div>

            <article>{productcategory.details}</article>
          </div>
        ))}
      </div>

      <AdminProductCategoryCreateForm
        storeProductCategory={storeProductCategory}
      />
    </div>
  );
}
