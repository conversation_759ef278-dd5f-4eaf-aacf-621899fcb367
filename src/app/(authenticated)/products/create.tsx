// "use client";

// import Select from "react-select";
// import AsyncSelect from "react-select/async";
// import { useState, ChangeEvent, useEffect } from "react";
// import { Submit<PERSON><PERSON><PERSON>, Controller, useForm } from "react-hook-form";
// import { toast } from "react-hot-toast";
// import Image from "next/image";
// import makeAnimated from "react-select/animated";
// import { CKEditor } from "@ckeditor/ckeditor5-react";
// import ClassicEditor from "@ckeditor/ckeditor5-build-classic";
// import { useRouter, useSearchParams } from "next/navigation";

// export default function AdminProductsCreateForm({
//   storeProduct,
//   categoryId,
//   tasks,
// }: {
//   storeProduct: (data: FormData) => Promise<void>;
//   categoryId?: string;
//   tasks: Task[];
// }) {
//   const [creating, setCreating] = useState(true);
//   const [preview, setPreview] = useState<string>();

//   const animatedComponents = makeAnimated();

//   const query = useSearchParams();

//   const router = useRouter();

//   const {
//     watch,
//     handleSubmit,
//     register,
//     reset,
//     control,
//     setValue,
//     formState: { errors, isSubmitting },
//   } = useForm<
//     Partial<
//       Product & {
//         upload: File;
//         taskId: string;
//         serviceId?: string;
//         productTypeId?: string;
//         productCategoryId?: string;
//         vendorId?: string;
//         branchId?: string;
//         form: boolean;
//       }
//     >
//   >({
//     defaultValues: {
//       name: "",
//       ref: "",
//       details: "",
//       price: 0,
//       discounted: 0,
//       stock: -1,
//       active: true,
//       featured: false,
//       type: "Digital",
//       condition: "New",
//       status: "Draft",
//       availability: "In Stock",
//       shipping: "Free",
//       mode: "Single",
//       payment: "Prepaid",
//       visibility: "Public",
//       productCategoryId: "",
//       vendorId: "",
//       branchId: "",
//       serviceId: "",
//       formId: "",
//       meta: {},
//       extra: {},
//       gallery: [],
//       form: false,
//     },
//   });

//   const product = watch();

//   const handleUploadedFile = (event: ChangeEvent<HTMLInputElement>) => {
//     const files = event.target.files;

//     if (files) {
//       const file = files[0];
//       setValue("upload", file);
//       const urlImage = URL.createObjectURL(file);

//       setPreview(urlImage);
//     }
//   };

//   const fetchServices = async (s: string) => {
//     if (s.length > 3) {
//       const services: Service[] = await fetch(
//         //@ts-ignore
//         `/api/tasks/${product.taskId.value}/services?s=${s}`,
//       ).then((r) => r.json());

//       return services?.map((service) => ({
//         value: service.id,
//         label: service.name,
//       }));
//     }

//     return [];
//   };

//   const fetchProductTypes = async (s: string) => {
//     if (s.length > 3) {
//       const types: ProductType[] = await fetch(
//         //@ts-ignore
//         `/api/services/${product.serviceId.value || product.serviceId}/product-types?s=${s}`,
//       ).then((r) => r.json());

//       return types?.map((t) => ({ value: t.id, label: t.name }));
//     }

//     return [];
//   };

//   const fetchProductCategories = async (s: string) => {
//     if (s.length > 2) {
//       const categories: ProductCategory[] = await fetch(
//         //@ts-ignore
//         `/api/product-types/${product.productTypeId.value}/categories?s=${s}`,
//       ).then((r) => r.json());

//       return categories?.map((category) => ({
//         value: category.id,
//         label: category.name,
//       }));
//     }

//     return [];
//   };

//   const fetchVendors = async (s: string) => {
//     if (s.length > 3) {
//       const vendors: Vendor[] = await fetch(`/api/vendors?s=${s}`).then((r) =>
//         r.json(),
//       );

//       return vendors?.map((vendor) => ({
//         value: vendor.id,
//         label: vendor.name,
//       }));
//     }

//     return [];
//   };

//   const fetchBranches = async (s: string) => {
//     if (s.length > 3) {
//       const branches: Branch[] = await fetch(
//         //@ts-ignore
//         `/api/vendors/${product.vendorId.value}/branches?s=${s}`,
//       ).then((r) => r.json());

//       return branches?.map((b) => ({
//         value: b.id,
//         label: b.name,
//       }));
//     }

//     return [];
//   };

//   const onSubmit: SubmitHandler<
//     Partial<Product & { upload: File; taskId: string }>
//   > = (p: Partial<Product & { upload: File; taskId: string }>) => {
//     const data = new FormData();

//     data.append("name", product.name!);
//     data.append("details", product.details!);
//     data.append("image", product.upload!);
//     //@ts-ignore
//     data.append("taskId", product.taskId.value);
//     //@ts-ignore
//     data.append("productCategoryId", product.productCategoryId.value);
//     data.append("ref", product.ref!);
//     data.append("price", product.price!.toString());
//     data.append("discounted", product.discounted!.toString());
//     data.append("stock", product.stock!.toString());
//     data.append("active", product.active!.toString());
//     data.append("featured", product.featured!.toString());
//     data.append("type", product.type!);
//     data.append("condition", product.condition!);
//     // data.append("status", product.status!);
//     data.append("availability", p.availability!);
//     data.append("shipping", product.shipping!);
//     data.append("mode", product.mode!);
//     data.append("payment", product.payment!);
//     data.append("visibility", product.visibility!);
//     //@ts-ignore
//     data.append("vendorId", product.vendorId!.value || product.vendorId!);
//     //@ts-ignore
//     data.append("serviceId", product.serviceId!.value);
//     // data.append("meta", product.meta!);
//     // data.append("extra", product.extra.setti!);

//     toast
//       .promise(
//         storeProduct(data),
//         {
//           loading: "Creating product...",
//           success: "Product has been saved 👌",
//           error: "Could not save product 🤯",
//         },
//         {
//           position: "bottom-center",
//         },
//       )
//       .then(() => {
//         reset();
//         router.push("/products");
//       })
//       .catch((e) => {

//       });
//   };

//   useEffect(() => {
//     if (query.has("serviceId")) {
//       setValue("serviceId", query.get("serviceId")!);

//       fetch(`/api/services/${query.get("serviceId")}`)
//         .then((r) => r.json())
//         .then((service) => {
//           setValue("taskId", service.taskId);
//         });
//     }
//   }, []);

//   return (
//     <div
//       id="drawer-create-product-default"
//       className="w-full bg-white p-4 dark:bg-default-800"
//     >
//       <form
//         className="grid grid-cols-12 gap-x-4 overflow-y-auto pb-40"
//         onSubmit={handleSubmit(onSubmit)}
//       >
//         <div className="col-span-8 space-y-4">
//           <div>
//             <label
//               htmlFor="name"
//               className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
//             >
//               Product name
//             </label>
//             <input
//               type="text"
//               id="name"
//               {...register("name")}
//               className="block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
//               placeholder="Type product name"
//               required
//             />
//           </div>

//           <div>
//             <label
//               htmlFor="price"
//               className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
//             >
//               Price (KES)
//             </label>
//             <div className="flex ">
//               <input
//                 type="number"
//                 id="price"
//                 {...register("price")}
//                 className="block w-full rounded-l-lg border border-r-0 border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
//                 placeholder="Price"
//                 required
//               />
//               <input
//                 type="number"
//                 id="discounted"
//                 {...register("discounted")}
//                 className="block w-full rounded-r-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
//                 placeholder="Discounted"
//                 required
//               />
//             </div>
//           </div>

//           <div>
//             <label
//               htmlFor="description"
//               className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
//             >
//               Description
//             </label>
//             <CKEditor
//               editor={ClassicEditor}
//               data=""
//               onBlur={(event, editor) => {
//                 setValue("details", editor.getData());
//               }}
//             />
//           </div>

//           <div className="flex w-full space-x-2">
//             <div className="flex-1">
//               <label
//                 htmlFor="name"
//                 className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
//               >
//                 External reference
//               </label>
//               <input
//                 type="text"
//                 id="name"
//                 {...register("ref")}
//                 className="block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
//                 placeholder="Type reference"
//                 required
//               />
//             </div>

//             <div className="flex-1">
//               <label
//                 htmlFor="stock"
//                 className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
//               >
//                 Available stock (-1 for unlimited)
//               </label>
//               <input
//                 type="number"
//                 id="stock"
//                 {...register("stock")}
//                 className="block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
//                 required
//               />
//             </div>
//           </div>
//         </div>

//         <div className="col-span-4 space-y-4">
//           <div className="mb-4 flex w-full justify-between px-4">
//             <label className="relative inline-flex cursor-pointer items-center">
//               <input
//                 {...register("active")}
//                 type="checkbox"
//                 className="peer sr-only"
//               />
//               <div className="peer h-6 w-11 rounded-full bg-default-200 after:absolute after:start-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-default-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-primary peer-checked:after:translate-x-full peer-checked:after:border-white peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:border-default-600 dark:bg-default-700 dark:peer-focus:ring-primary rtl:peer-checked:after:-translate-x-full"></div>
//               <span className="ms-3 text-sm font-medium text-default-900 dark:text-default-300">
//                 {product.active ? "Active" : "Inactive"}
//               </span>
//             </label>

//             <label className="relative inline-flex cursor-pointer items-center">
//               <input
//                 {...register("featured")}
//                 type="checkbox"
//                 className="peer sr-only"
//               />
//               <div className="peer h-6 w-11 rounded-full bg-default-200 after:absolute after:start-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-default-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-primary peer-checked:after:translate-x-full peer-checked:after:border-white peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:border-default-600 dark:bg-default-700 dark:peer-focus:ring-primary rtl:peer-checked:after:-translate-x-full"></div>
//               <span className="ms-3 text-sm font-medium text-default-900 dark:text-default-300">
//                 {product.featured ? "Featured" : "Not featured"}
//               </span>
//             </label>
//           </div>

//           <div>
//             <label
//               htmlFor="image"
//               className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
//             >
//               Product image
//             </label>
//             <label className="flex h-24 w-full items-center justify-center rounded-lg border border-dashed border-default-300 bg-default-50 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600">
//               <input
//                 type="file"
//                 name="image"
//                 id="image"
//                 className="hidden"
//                 onChange={handleUploadedFile}
//               />
//               {preview ? (
//                 <Image
//                   src={preview}
//                   alt="preview"
//                   width={100}
//                   height={50}
//                   className="w-1/2"
//                 />
//               ) : (
//                 <p>Click to select file</p>
//               )}
//             </label>
//           </div>

//           {!product.taskId && (
//             <div className="mb-6">
//               <label
//                 htmlFor="vendorId"
//                 className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
//               >
//                 Select vendor
//               </label>
//               <Controller
//                 name="vendorId"
//                 control={control}
//                 rules={{ required: true }}
//                 render={({ field }) => (
//                   <AsyncSelect
//                     {...field}
//                     isClearable
//                     // cacheOptions
//                     isSearchable
//                     //@ts-ignore
//                     loadOptions={fetchVendors}
//                     components={animatedComponents}
//                     placeholder="Select vendor"
//                     required
//                     classNames={{
//                       control: () => "!py-[1px] !rounded-lg",
//                       menu: () => "py-1",
//                     }}
//                   />
//                 )}
//               />
//             </div>
//           )}

//           {product.vendorId && (
//             <div className="mb-6">
//               <label
//                 htmlFor="vendorId"
//                 className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
//               >
//                 Select branch
//               </label>
//               <Controller
//                 name="branchId"
//                 control={control}
//                 rules={{ required: true }}
//                 render={({ field }) => (
//                   <AsyncSelect
//                     {...field}
//                     isClearable
//                     // cacheOptions
//                     isSearchable
//                     //@ts-ignore
//                     loadOptions={fetchBranches}
//                     components={animatedComponents}
//                     placeholder="Select branch"
//                     required
//                     classNames={{
//                       control: () => "!py-[1px] !rounded-lg",
//                       menu: () => "py-1",
//                     }}
//                   />
//                 )}
//               />
//             </div>
//           )}

//           <div className="mb-6">
//             <label
//               htmlFor="name"
//               className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
//             >
//               Select task
//             </label>
//             <Controller
//               name="taskId"
//               control={control}
//               render={({ field }) => (
//                 <Select
//                   {...field}
//                   //@ts-ignore
//                   options={tasks.map((task) => ({
//                     value: task.id,
//                     label: task.name,
//                   }))}
//                   components={animatedComponents}
//                   placeholder="Select task"
//                   required
//                   isSearchable
//                   classNames={{
//                     control: () => "!py-[1px] !rounded-lg",
//                     menu: () => "py-1",
//                   }}
//                 />
//               )}
//             />
//           </div>

//           {product.taskId && !product.serviceId && (
//             <div className="mb-6">
//               <label
//                 htmlFor="name"
//                 className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
//               >
//                 Select services
//               </label>
//               <Controller
//                 name="serviceId"
//                 control={control}
//                 rules={{ required: true }}
//                 render={({ field: { onChange, value } }) => (
//                   <AsyncSelect
//                     isClearable
//                     isSearchable
//                     // cacheOptions
//                     //@ts-ignore
//                     loadOptions={fetchServices}
//                     components={animatedComponents}
//                     placeholder="Select services"
//                     required
//                     classNames={{
//                       control: () => "!py-[1px] !rounded-lg",
//                     }}
//                     onChange={onChange}
//                     value={value}
//                   />
//                 )}
//               />
//             </div>
//           )}

//           {product.serviceId && (
//             <div className="mb-6">
//               <label
//                 htmlFor="name"
//                 className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
//               >
//                 Select product type
//               </label>

//               <Controller
//                 name="productTypeId"
//                 control={control}
//                 rules={{ required: true }}
//                 render={({ field }) => (
//                   <AsyncSelect
//                     {...field}
//                     isClearable
//                     isSearchable
//                     // cacheOptions
//                     //@ts-ignore
//                     loadOptions={fetchProductTypes}
//                     components={animatedComponents}
//                     placeholder="Select types"
//                     required
//                     classNames={{
//                       control: () => "!py-[1px] !rounded-lg",
//                       menu: () => "py-1 z-50 mb-5",
//                     }}
//                   />
//                 )}
//               />
//             </div>
//           )}

//           {product.productTypeId && (
//             <div className="mb-6">
//               <label
//                 htmlFor="name"
//                 className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
//               >
//                 Select product category
//               </label>
//               <Controller
//                 name="productCategoryId"
//                 control={control}
//                 rules={{ required: true }}
//                 render={({ field }) => (
//                   <AsyncSelect
//                     {...field}
//                     isClearable
//                     // cacheOptions
//                     isSearchable
//                     //@ts-ignore
//                     loadOptions={fetchProductCategories}
//                     components={animatedComponents}
//                     placeholder="Select category"
//                     required
//                     classNames={{
//                       control: () => "!py-[1px] !rounded-lg",
//                       menu: () => "py-1",
//                     }}
//                   />
//                 )}
//               />
//             </div>
//           )}

//           <div className="mb-40 space-y-4">
//             <div>
//               <label>Status</label>
//               <div className="flex w-full space-x-4">
//                 {["Draft", "Published"].map((s) => (
//                   <label
//                     key={s}
//                     className="relative inline-flex cursor-pointer items-center"
//                   >
//                     <input
//                       {...register("status")}
//                       type="radio"
//                       className="peer sr-only"
//                     />
//                     <div className="peer h-6 w-11 rounded-full bg-default-200 after:absolute after:start-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-default-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-primary peer-checked:after:translate-x-full peer-checked:after:border-white peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:border-default-600 dark:bg-default-700 dark:peer-focus:ring-primary rtl:peer-checked:after:-translate-x-full"></div>
//                     <span className="ms-3 text-sm font-medium text-default-900 dark:text-default-300">
//                       {s}
//                     </span>
//                   </label>
//                 ))}
//               </div>
//             </div>
//           </div>

//           {product.form && (
//             <>
//               <div className="col-span-8 space-y-4">
//                 <div>
//                   <label
//                     htmlFor="name"
//                     className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
//                   >
//                     Form name
//                   </label>
//                   <input
//                     type="text"
//                     id="name"
//                     {...register("name")}
//                     className="block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
//                     placeholder="e.g Additional items"
//                     required
//                   />
//                 </div>
//               </div>
//             </>
//           )}

//           <div className="flex w-full items-center justify-between py-4">
//             <label className="relative inline-flex cursor-pointer items-center">
//               <input
//                 {...register("form")}
//                 type="checkbox"
//                 className="peer sr-only"
//               />
//               <div className="peer h-6 w-11 rounded-full bg-default-200 after:absolute after:start-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-default-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-primary peer-checked:after:translate-x-full peer-checked:after:border-white peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:border-default-600 dark:bg-default-700 dark:peer-focus:ring-primary rtl:peer-checked:after:-translate-x-full"></div>
//               <span className="ms-3 text-sm font-medium text-default-900 dark:text-default-300">
//                 {product.form ? "Has form" : "Add form"}
//               </span>
//             </label>

//             <button
//               type="submit"
//               className="justify-center rounded-lg bg-primary px-5 py-3 text-center text-sm font-medium text-white hover:bg-default-800 focus:outline-none focus:ring-4 focus:ring-default-300 dark:bg-primary dark:hover:bg-primary dark:focus:ring-default-800"
//             >
//               Save product details
//             </button>
//           </div>
//         </div>
//       </form>
//     </div>
//   );
// }
