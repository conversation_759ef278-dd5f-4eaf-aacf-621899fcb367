import { api } from "@/lib/api";
import ProductForm from "./form";
import { auth } from "@/auth";
import BackBtn from "@/components/back";

export const dynamic = "force-dynamic";
export const fetchCache = "force-no-store";

export const metadata = {
  title: "Create item",
  description: "Create product or service",
  keywords: ["product"],
};


interface User {
	id: string;
	title: string;
	firstName: string;
	lastName: string;
	gender: string | null;
	dob: string | null;
	email: string;
	phone: string;
	idpass: string;
	rememberMeToken: string | null;
	details: string | null;
	location: Record<string, any> | null;
	geom: string | null;
	avatar: AttachmentContract | null;
	createdAt: string;
	updatedAt: string;
	name: string;
	status: string;
	avatarUrl: string;
	initials: string;
	devices: Device[];
	roles: Role[];
	permissions: Permission[];
	notifications: DatabaseNotification[];

	// computed
	identifier: string;
	online: boolean;
	vendorId: string;
}


export default async function page({
  searchParams,
}: {
  searchParams: Record<string, string>;
}) {
  const session = await auth();
  const loggedInUserId = session?.user?.id;

  // const loggedInUserRole = session?.user?.roles[0]?.name;
  

  const userDetail: unknown = session?.user ?  await api.get(`/users/${loggedInUserId}`) : "";

  const userDetails = userDetail as User;



  const services = await api.get<PaginatedData<Service>>(
    session?.vendor ? `vendors/${session?.vendor?.id}/services` : "services",
    {
      per: 300,
    },
  );

  const loadProductTypes = async (serviceId: string) => {
    "use server";

    return await api.get<PaginatedData<ProductType>>(
      `services/${serviceId}/product-types`,
      {
        per: 30,
      },
    );
  };

  const loadCategories = async (productTypeId: string) => {
    "use server";

    return await api.get<PaginatedData<ProductCategory>>(
      `product-types/${productTypeId}/categories`,
      {},
    );
  };

  const loadVendors = async (s: string) => {
    "use server";

    if (s.length > 3) {
      const res = await api.get<PaginatedData<Vendor>>("vendors", { s });

      return res?.data ?? [];
    }

    return [];
  };

  const loadBranches = async (s: string, vendorId: string) => {
    "use server";

    if (s.length > 3) {
      const res = await api.get<PaginatedData<Branch>>(
        `vendors/${vendorId}/branches`,
        { s },
      );

      return res?.data ?? [];
    }

    return [];
  };

  const storeProductType = async (data: FormData) => {
    "use server";

    return await api.post<ProductType>("product-types", data);
  };

  const storeProductCategory = async (data: FormData) => {
    "use server";

    return await api.post<ProductCategory>("product-categories", data);
  };

  const storeTag = async (data: FormData) => {
    "use server";

    return await api.post<Tag>("tags", data);
  };

  const storeProduct = async (data: FormData) => {
    "use server";

    if (session?.branch) {
      data.append("branchId", session?.branch?.id);
      data.append(
        "vendorId",
        searchParams.vendorId || session?.branch?.vendorId,
      );
    }

    return await api.post<Product>("products", data);
  };


  return (
    <div className="page-content space-y-6 p-5">
      <BackBtn />
      
      {services && (
        <ProductForm
          userDetails={userDetails}
          storeProductType={storeProductType}
          storeProductCategory={storeProductCategory}
          storeProduct={storeProduct}
          storeTag={storeTag}
          services={services.data}
          loadProductTypes={loadProductTypes}
          loadProductCategories={loadCategories}
          loadVendors={loadVendors}
          loadBranches={loadBranches}
          branchId={session?.branch?.vendorId}
        />
      )}
    </div>
  );
}
