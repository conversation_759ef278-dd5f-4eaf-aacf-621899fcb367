"use client";

import { Controller, useForm } from "react-hook-form";
import toast from "react-hot-toast";
import AsyncSelect from "react-select/async";

export default function EditProductBranch({
  defaultValues,
  updateRecord,
}: {
  defaultValues: Product;
  updateRecord: (data: Product) => Promise<void>;
}) {
  const { setValue, handleSubmit, watch, control } = useForm<Product>({
    defaultValues,
  });

  const fetchBranches = async (input: string) => {
    const branches: Branch[] = await fetch(
      `/api/vendors/${defaultValues.vendorId}/branches?s=${input}`,
    ).then((res) => res.json());
    return branches;
  };

  const onSubmit = async (payload: Product) => {
    toast.promise(updateRecord(payload), {
      loading: "Updating product...",
      success: "Product updated!",
      error: "Error updating product",
    });
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="flex space-x-2">
      <Controller
        name="branchId"
        control={control}
        render={({ field }) => (
          <AsyncSelect
            loadOptions={fetchBranches}
            placeholder="Select branch"
            getOptionLabel={(b: Branch) => b.name}
            getOptionValue={(b: Branch) => b.id}
            onChange={(v) => setValue("branchId", v?.id!)}
            components={{
              IndicatorSeparator: () => null,
            }}
            classNames={{
              control: () => "py-[1px] !rounded-lg",
            }}
          />
        )}
      />

      <button type="submit">Save</button>
    </form>
  );
}
