"use client";

import { useF<PERSON>, Controller } from "react-hook-form";
import toast from "react-hot-toast";
import AsyncSelect from "react-select/async";

export default function EditProduct({
  defaultValues,
  updateRecord,
  loadVendors,
}: {
  defaultValues: Product;
  updateRecord: (data: Product) => Promise<void>;
  loadVendors: (s: string) => Promise<Vendor[]>;
}) {
  const { register, handleSubmit, watch, control, setValue } = useForm<Product>(
    {
      defaultValues,
    },
  );

  const onSubmit = async (payload: Product) => {
    toast.promise(updateRecord(payload), {
      loading: "Updating product...",
      success: "Product updated!",
      error: "Error updating product",
    });
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="flex space-x-2">
      <Controller
        name="vendorId"
        control={control}
        render={({ field }) => (
          <AsyncSelect
            loadOptions={loadVendors}
            placeholder="Select vendor"
            isSearchable
            getOptionLabel={(vendor: Vendor) => vendor.name}
            getOptionValue={(vendor: Vendor) => vendor.id}
            onChange={(vendor) => {
              setValue("vendorId", vendor?.id!);
            }}
            components={{
              IndicatorSeparator: () => null,
            }}
            classNames={{
              control: () => "py-[1px] !rounded-lg",
            }}
          />
        )}
      />

      <button type="submit">Save</button>
    </form>
  );
}
