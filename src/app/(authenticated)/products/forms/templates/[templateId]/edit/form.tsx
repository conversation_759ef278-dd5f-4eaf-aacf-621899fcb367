"use client";

import Select from "react-select";
import AsyncSelect from "react-select/async";
import { useState, ChangeEvent } from "react";
import { Submit<PERSON><PERSON><PERSON>, Controller, useForm } from "react-hook-form";
import { toast } from "react-hot-toast";
import Image from "next/image";
import makeAnimated from "react-select/animated";
import FormPreview from "../../../../../../../components/form-preview";
import { fieldTypes } from "@/lib/utils";

export default function FormsUpdateForm({
  defaultValues,
  updateFormTemplate,
}: {
  defaultValues: FormTemplate;
  updateFormTemplate: (data: FormData) => Promise<void>;
}) {
  const [preview, setPreview] = useState<string>();

  const animatedComponents = makeAnimated();

  const {
    watch,
    handleSubmit,
    register,
    reset,
    control,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<
    FormTemplate & {
      upload: File;
      taskId: string;
      formTemplateId?: string;
      hasForm: boolean;
    }
  >({
    defaultValues: {
      ...defaultValues,
      sections: Object.values(defaultValues.sections),
    },
  });

  const productForm = watch();

  const handleUploadedFile = (event: ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;

    if (files) {
      const file = files[0];
      setValue("upload", file);
      const urlImage = URL.createObjectURL(file);

      setPreview(urlImage);
    }
  };

  const onSubmit: SubmitHandler<
    Partial<FormTemplate & { upload: File; taskId: string }>
  > = async (p: Partial<FormTemplate & { upload: File; taskId: string }>) => {
    const data = new FormData();

    data.append("name", productForm.name!);
    data.append("details", productForm.details!);

    productForm.sections.map((s, si) => {
      data.append(`sections[${si}][id]`, s.id);
      data.append(`sections[${si}][name]`, s.name);
      data.append(`sections[${si}][details]`, s.details);

      if (s.repeatable) {
        data.append(`sections[${si}][repeatable]`, s.repeatable.toString());
      }

      if (s.repeats) {
        data.append(`sections[${si}][repeats]`, s.repeats.toString());
      }

      s.fields.map((f, fi) => {
        data.append(`sections[${si}][fields][${fi}][id]`, f.id);
        data.append(`sections[${si}][fields][${fi}][type]`, f.type);
        data.append(`sections[${si}][fields][${fi}][name]`, f.name);
        data.append(`sections[${si}][fields][${fi}][label]`, f.label);
        data.append(
          `sections[${si}][fields][${fi}][defaultValue]`,
          f.defaultValue,
        );
        data.append(
          `sections[${si}][fields][${fi}][placeholder]`,
          f.placeholder,
        );
        data.append(
          `sections[${si}][fields][${fi}][required]`,
          f.required.toString(),
        );

        if (f.repeatable) {
          data.append(
            `sections[${si}][fields][${fi}][repeatable]`,
            f.repeatable.toString(),
          );
        }

        if (f.repeats) {
          data.append(
            `sections[${si}][fields][${fi}][repeats]`,
            f.repeats?.toString(),
          );
        }

        if (f.min) {
          data.append(`sections[${si}][fields][${fi}][min]`, f.min?.toString());
        }

        if (f.max) {
          data.append(`sections[${si}][fields][${fi}][max]`, f.max?.toString());
        }

        f.options?.map((o, oi) => {
          data.append(
            `sections[${si}][fields][${fi}][options][${oi}][value]`,
            o.value.toString(),
          );
          data.append(
            `sections[${si}][fields][${fi}][options][${oi}][label]`,
            o.label.toString(),
          );
        });
      });
    });

    if (productForm.upload) {
      data.append("image", productForm.upload);
    }

    await toast
      .promise(
        updateFormTemplate(data),
        {
          loading: "Creating form...",
          success: "Form has been saved 👌",
          error: "Could not save form 🤯",
        },
        {
          position: "bottom-center",
        },
      )
      .then(() => {
        // setCreating(false);
        reset();
      })
      .catch((e) => {
        console.error(e);
      });
  };

  return (
    <div className="h-full bg-white">
      <div className="h-full w-full bg-white p-4 dark:bg-default-800">
        <form
          className="grid grid-cols-12 gap-x-4 overflow-y-auto pb-40"
          onSubmit={handleSubmit(onSubmit)}
        >
          <div className="col-span-6 space-y-4">
            <div className="my-3 flex items-center justify-between">
              <label className="text-lg font-bold">Form sections</label>

              <button
                type="button"
                onClick={() =>
                  setValue("sections", [
                    ...productForm.sections,
                    {
                      name: "New section",
                      details: "",
                      fields: [],
                      repeatable: false,
                      repeats: 0,
                      id: Math.random().toString(16).slice(2, 8),
                    },
                  ])
                }
                className="rounded-lg bg-primary px-4 py-2 text-white"
              >
                Add section
              </button>
            </div>

            {productForm.sections?.map((section, s) => (
              <div
                key={section.id}
                className="mb-4 rounded-lg border border-default-200 px-4 py-3"
              >
                <div>
                  <div className="mb-3 flex items-center justify-between">
                    <label className="flex gap-2 text-lg font-bold">
                      <button
                        type="button"
                        onClick={() =>
                          setValue(
                            "sections",
                            productForm.sections.filter(
                              (s) => s.id !== section.id,
                            ),
                          )
                        }
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                          strokeWidth={1.5}
                          stroke="currentColor"
                          className="size-6 text-danger-500"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"
                          />
                        </svg>
                      </button>
                      Section {s + 1}. {section.name}
                    </label>

                    <button
                      type="button"
                      className="rounded-lg border border-default-200 px-4 py-2 text-default-600"
                      onClick={() =>
                        setValue(
                          "sections",
                          productForm.sections.map((sec, seci) => {
                            if (seci === s) {
                              return {
                                ...section,
                                fields: [
                                  ...section.fields,
                                  ...[
                                    {
                                      name: "",
                                      label: "",
                                      id: Math.random().toString(16).slice(2, 8),
                                      type: "text",
                                      defaultValue: "",
                                      placeholder: "",
                                      required: false,
                                    },
                                  ],
                                ],
                              };
                            }

                            return sec;
                          }),
                        )
                      }
                    >
                      Add field
                    </button>
                  </div>

                  <div className="mb-3">
                    <input
                      {...register(`sections.${s}.name`)}
                      defaultValue={section.name}
                      placeholder="Section name"
                      className="block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-200 focus:ring-default-600 dark:border-default-200 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
                    />
                  </div>

                  <div className="mb-3">
                    <textarea
                      {...register(`sections.${s}.details`)}
                      defaultValue={section.details}
                      placeholder="Section description"
                      className="block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-200 focus:ring-default-600 dark:border-default-200 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
                    />
                  </div>

                  <div className="mb-3">
                    <div className="flex items-center justify-between">
                      <label className="relative inline-flex cursor-pointer items-center">
                        <input
                          {...register(`sections.${s}.repeatable`)}
                          type="checkbox"
                          className="peer sr-only"
                        />
                        <div className="peer h-6 w-11 rounded-full bg-default-200 after:absolute after:start-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-default-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-primary peer-checked:after:translate-x-full peer-checked:after:border-white peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:border-default-200 dark:bg-default-700 dark:peer-focus:ring-primary rtl:peer-checked:after:-translate-x-full"></div>
                        <span className="ml-3 text-sm font-medium text-default-900 dark:text-default-300">
                          Repeatable
                        </span>
                      </label>

                      {section.repeatable && (
                        <input
                          {...register(`sections.${s}.repeats`)}
                          type="number"
                          placeholder="Repeats"
                          className="block w-2/5 rounded-lg border border-default-300 bg-default-50 p-2.5 text-sm text-default-900 focus:border-default-200 focus:ring-default-600 dark:border-default-200 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
                        />
                      )}
                    </div>
                  </div>
                </div>

                {section.fields?.map((field, f) => (
                  <div key={field.id} className="flex w-full">
                    <div className="flex h-6 flex-col gap-3 pt-4">
                      <span className="w-full rounded-l-lg border border-r-0 border-default-200 px-1 font-bold">
                        {f + 1}.
                      </span>

                      <button
                        type="button"
                        onClick={() => {
                          const sections = productForm.sections;
                          sections[s].fields.splice(f, 1);
                          setValue("sections", sections);
                        }}
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                          strokeWidth={1.5}
                          stroke="currentColor"
                          className="size-6 text-danger-500"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"
                          />
                        </svg>
                      </button>
                    </div>

                    <div className="mt-2 grid flex-1 grid-cols-3 gap-3 rounded-lg border border-default-200 p-2">
                      <div>
                        <label className="my-3">Field name</label>
                        <input
                          {...register(`sections.${s}.fields.${f}.name`)}
                          id={`sections.${s}.fields.${f}.name`}
                          placeholder="Field name e.g firstName"
                          className="block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-200 focus:ring-default-600 dark:border-default-200 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
                        />
                      </div>

                      <div>
                        <label className="my-3">Field label</label>
                        <input
                          {...register(`sections.${s}.fields.${f}.label`)}
                          id={`sections.${s}.fields.${f}.label`}
                          placeholder="Field label e.g First Name"
                          className="block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-200 focus:ring-default-600 dark:border-default-200 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
                        />
                      </div>

                      <div>
                        <label className="my-3">Field placeholder</label>
                        <input
                          {...register(`sections.${s}.fields.${f}.placeholder`)}
                          id={`sections.${s}.fields.${f}.placeholder`}
                          placeholder="Placeholder text"
                          className="block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-200 focus:ring-default-600 dark:border-default-200 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
                        />
                      </div>

                      <div className="space-y-2">
                        <label>Field type</label>

                        <Controller
                          name={`sections.${s}.fields.${f}.type`}
                          control={control}
                          render={() => (
                            <Select
                              options={fieldTypes}
                              id={`sections.${s}.fields.${f}.type`}
                              getOptionValue={(o: FieldType) => o.key}
                              components={animatedComponents}
                              placeholder="Select type"
                              required
                              isSearchable
                              isMulti={false}
                              classNames={{
                                control: () =>
                                  "!py-[4px] !rounded-lg !border !border-default-200 !bg-default-50",
                                menu: () => "py-1",
                              }}
                              defaultValue={{
                                key:
                                  productForm.sections[s]["fields"][f].type ||
                                  "text",
                                label:
                                  fieldTypes.find(
                                    (t) =>
                                      t.key ===
                                      productForm.sections[s]["fields"][f].type,
                                  )?.label || "Text",
                              }}
                              onChange={(v) =>
                                setValue(
                                  `sections.${s}.fields.${f}.type`,
                                  v?.key || "text",
                                )
                              }
                            />
                          )}
                        />
                      </div>

                      {[
                        "select",
                        "multiselect",
                        "radio",
                        "checkbox",
                        "multicheck",
                      ].includes(section.fields[f].type) && (
                        <div className="col-span-2">
                          <div className="flex justify-between">
                            <label>Field options</label>

                            <button
                              type="button"
                              className="rounded-lg px-4 text-default-600"
                              onClick={() =>
                                setValue(
                                  "sections",
                                  productForm.sections.map((sec, seci) => {
                                    if (seci === s) {
                                      return {
                                        ...section,
                                        fields: section.fields.map(
                                          (field, fo) => {
                                            if (fo === f) {
                                              return {
                                                ...field,
                                                options: [
                                                  ...(field.options || []),
                                                  ...[
                                                    {
                                                      label: "",
                                                      value: "",
                                                    },
                                                  ],
                                                ],
                                              };
                                            }
                                            return field;
                                          },
                                        ),
                                      };
                                    }

                                    return sec;
                                  }),
                                )
                              }
                            >
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                strokeWidth={1.5}
                                stroke="currentColor"
                                className="h-6 w-6"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  d="M12 4.5v15m7.5-7.5h-15"
                                />
                              </svg>
                            </button>
                          </div>

                          {section.fields[f].options?.map((option, oi) => (
                            <div key={oi} className="mb-2 flex">
                              <input
                                {...register(
                                  `sections.${s}.fields.${f}.options.${oi}.value`,
                                )}
                                placeholder={`#${oi + 1} value`}
                                className="block w-full rounded-l-lg border border-default-300 bg-default-50 p-1.5 text-sm text-default-900 focus:border-default-200 focus:ring-default-600 dark:border-default-200 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
                              />
                              <input
                                {...register(
                                  `sections.${s}.fields.${f}.options.${oi}.label`,
                                )}
                                placeholder={`#${oi + 1} label`}
                                className="block w-full rounded-r-lg border border-l-0 border-default-300 bg-default-50 p-1.5 text-sm text-default-900 focus:border-default-200 focus:ring-default-600 dark:border-default-200 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
                              />
                              <a
                                href="#"
                                className="ml-1 flex items-center justify-center rounded-lg px-3 text-red-600"
                              >
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                  strokeWidth={1.5}
                                  stroke="currentColor"
                                  className="h-6 w-6"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"
                                  />
                                </svg>
                              </a>
                            </div>
                          ))}
                        </div>
                      )}

                      {field.type === "range" && (
                        <div className="flex items-center space-x-2">
                          <div>
                            <label className="my-3">Min value</label>
                            <input
                              {...register(`sections.${s}.fields.${f}.min`)}
                              id={`sections.${s}.fields.${f}.min`}
                              type="number"
                              placeholder="Minimum value"
                              className="block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-200 focus:ring-default-600 dark:border-default-200 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
                            />
                          </div>

                          <div>
                            <label className="my-3">Max value</label>
                            <input
                              {...register(`sections.${s}.fields.${f}.max`)}
                              id={`sections.${s}.fields.${f}.max`}
                              type="number"
                              placeholder="Maximum value"
                              className="block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-200 focus:ring-default-600 dark:border-default-200 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
                            />
                          </div>
                        </div>
                      )}

                      <div className="space-y-2">
                        <label className="block">Field default value</label>
                        <input
                          {...register(
                            `sections.${s}.fields.${f}.defaultValue`,
                          )}
                          id={`sections.${s}.fields.${f}.defaultValue`}
                          placeholder="Field default value"
                          className="block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-200 focus:ring-default-600 dark:border-default-200 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
                        />
                      </div>

                      <div className="flex flex-col items-center space-y-2">
                        <label className="dark:text-primary-500">
                          Required
                        </label>

                        <label className="relative inline-flex cursor-pointer items-center">
                          <input
                            {...register(`sections.${s}.fields.${f}.required`)}
                            id={`sections.${s}.fields.${f}.required`}
                            type="checkbox"
                            className="peer sr-only"
                          />
                          <div className="peer h-6 w-11 rounded-full bg-default-200 after:absolute after:start-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-default-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-primary peer-checked:after:translate-x-full peer-checked:after:border-white peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:border-default-200 dark:bg-default-700 dark:peer-focus:ring-primary rtl:peer-checked:after:-translate-x-full"></div>
                          <span className="ml-3 text-sm font-medium text-default-900 dark:text-default-300">
                            Required
                          </span>
                        </label>
                      </div>

                      <div className="col-span-2">
                        <div className="flex items-center justify-between">
                          <label className="relative inline-flex cursor-pointer items-center">
                            <input
                              {...register(
                                `sections.${s}.fields.${f}.repeatable`,
                              )}
                              id={`sections.${s}.fields.${f}.repeatable`}
                              type="checkbox"
                              className="peer sr-only"
                            />
                            <div className="peer h-6 w-11 rounded-full bg-default-200 after:absolute after:start-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-default-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-primary peer-checked:after:translate-x-full peer-checked:after:border-white peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:border-default-200 dark:bg-default-700 dark:peer-focus:ring-primary rtl:peer-checked:after:-translate-x-full"></div>
                            <span className="ml-3 text-sm font-medium text-default-900 dark:text-default-300">
                              Repeatable
                            </span>
                          </label>

                          {section.fields[f].repeatable && (
                            <input
                              {...register(`sections.${s}.fields.${f}.repeats`)}
                              id={`sections.${s}.fields.${f}.repeats`}
                              type="number"
                              placeholder="Repeats"
                              className="block w-2/5 rounded-lg border border-default-300 bg-default-50 p-2.5 text-sm text-default-900 focus:border-default-200 focus:ring-default-600 dark:border-default-200 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
                            />
                          )}
                        </div>
                      </div>

                      <div className="col-span-2">
                        <div className="flex items-center justify-between">
                          <label className="relative inline-flex cursor-pointer items-center">
                            <input
                              {...register(`sections.${s}.fields.${f}.hasCost`)}
                              id={`sections.${s}.fields.${f}.hasCost`}
                              type="checkbox"
                              className="peer sr-only"
                            />
                            <div className="peer h-6 w-11 rounded-full bg-default-200 after:absolute after:start-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-default-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-primary peer-checked:after:translate-x-full peer-checked:after:border-white peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:border-default-200 dark:bg-default-700 dark:peer-focus:ring-primary rtl:peer-checked:after:-translate-x-full"></div>
                            <span className="ml-3 text-sm font-medium text-default-900 dark:text-default-300">
                              Has cost
                            </span>
                          </label>

                          {section.fields[f].hasCost && (
                            <input
                              {...register(`sections.${s}.fields.${f}.cost`)}
                              id={`sections.${s}.fields.${f}.cost`}
                              type="number"
                              placeholder="Cost in KES"
                              className="block w-2/5 rounded-lg border border-default-300 bg-default-50 p-2.5 text-sm text-default-900 focus:border-default-200 focus:ring-default-600 dark:border-default-200 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
                            />
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}

                <div className="mt-2 flex justify-end">
                  <button
                    type="button"
                    className="rounded-lg border border-default-200 px-4 py-2 text-default-600"
                    onClick={() =>
                      setValue(
                        "sections",
                        productForm.sections.map((sec, seci) => {
                          if (seci === s) {
                            return {
                              ...section,
                              fields: [
                                ...section.fields,
                                ...[
                                  {
                                    name: "",
                                    label: "",
                                    id: Math.random().toString(16).slice(2, 8),
                                    type: "text",
                                    defaultValue: "",
                                    placeholder: "",
                                    required: false,
                                  },
                                ],
                              ],
                            };
                          }

                          return sec;
                        }),
                      )
                    }
                  >
                    Add field
                  </button>
                </div>
              </div>
            ))}

            {productForm.sections.length > 2 && (
              <div className="flex w-full justify-end">
                <button
                  type="submit"
                  className="justify-center rounded-lg bg-primary px-5 py-3 text-center text-sm font-medium text-white hover:bg-default-800 focus:outline-none focus:ring-4 focus:ring-default-300 dark:bg-primary dark:hover:bg-primary dark:focus:ring-default-800"
                >
                  Submit form details
                </button>
              </div>
            )}
          </div>

          <div className="col-span-6 space-y-4">
            <div>
              <label
                htmlFor="name"
                className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
              >
                Form name
              </label>
              <input
                type="text"
                id="name"
                {...register("name")}
                className="block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-200 focus:ring-default-600 dark:border-default-200 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
                placeholder="Type form name"
                required
              />
            </div>

            <div className="flex w-full space-x-2">
              <div className="w-4/5">
                <label
                  htmlFor="description"
                  className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
                >
                  Description
                </label>
                <textarea
                  {...register("details")}
                  className="block h-40 w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-200 focus:ring-default-600 dark:border-default-200 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
                />
              </div>

              <div className="w-1/5">
                <label
                  htmlFor="image"
                  className="mb-2 block w-full text-sm font-medium text-default-900 dark:text-white"
                >
                  Form image
                </label>
                <label className="flex h-40 w-full items-center justify-center rounded-lg border border-dashed border-default-300 bg-default-50 text-sm text-default-900 focus:border-default-200 focus:ring-default-600 dark:border-default-200 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600">
                  <input
                    type="file"
                    name="image"
                    id="image"
                    className="hidden"
                    onChange={handleUploadedFile}
                  />
                  {preview ? (
                    <Image
                      src={preview}
                      alt="preview"
                      width={100}
                      height={50}
                      className="w-1/2"
                    />
                  ) : (
                    <p>Click to select file</p>
                  )}
                </label>
              </div>
            </div>

            <div className="flex justify-end">
              <button
                type="submit"
                className="justify-center rounded-lg bg-primary px-5 py-3 text-center text-sm font-medium text-white hover:bg-default-800 focus:outline-none focus:ring-4 focus:ring-default-300 dark:bg-primary dark:hover:bg-primary dark:focus:ring-default-800"
              >
                Submit form details
              </button>
            </div>

            <h2>Form Preview</h2>

            <FormPreview sections={productForm.sections} />
          </div>
        </form>
      </div>
    </div>
  );
}
