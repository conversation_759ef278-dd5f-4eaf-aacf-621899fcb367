import { api } from "@/lib/api";
import { revalidatePath } from "next/cache";
import FormsCreateForm from "./form";

export const metadata = {
  title: "Create form template",
};

export default async function AdminFormsCreate({
  params,
}: {
  params: { productId: string };
}) {
  const createRecord = async (data: FormData) => {
    "use server";

    await api.post(`form-templates`, data);

    revalidatePath(`/form-templates`);
  };

  return (
    <div className="h-full bg-white p-4">
      <FormsCreateForm storeFormTemplate={createRecord} />
    </div>
  );
}
