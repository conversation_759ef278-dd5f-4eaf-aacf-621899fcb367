import { api } from "@/lib/api";
import { auth } from "@/auth";
import { Metadata } from "next";
import ProductsTable from "@/components/tables/products-table";

export const metadata: Metadata = {
  title: "Product & Service Listing",
  description: "List of products",
  keywords: ["products", "list", "products list"],
};

interface User {
  id: string;
  title: string;
  firstName: string;
  lastName: string;
  gender: string | null;
  dob: string | null;
  email: string;
  phone: string;
  idpass: string;
  rememberMeToken: string | null;
  details: string | null;
  location: Record<string, any> | null;
  geom: string | null;
  avatar: AttachmentContract | null;
  createdAt: string;
  updatedAt: string;
  name: string;
  status: string;
  avatarUrl: string;
  initials: string;
  devices: Device[];
  roles: Role[];
  permissions: Permission[];
  notifications: DatabaseNotification[];

  // computed
  identifier: string;
  online: boolean;
  vendorId: string;
}

export default async function ProductIndex({
  searchParams,
}: {
  searchParams: Record<string, string>;
}) {
  const session = await auth();

  if (!session || !session.user) {
    return <div>Please log in to view products.</div>;
  }

  const loggedInUserId = session?.user?.id;
  // const loggedInUserId = "01hvstmhh5z859rf278r0b2ee8";

  // Fetch the user details
  const userDetail: unknown = await api.get(`/users/${loggedInUserId}`);
  const userDetails = userDetail as User;


    // Assign the initial role to the first role in the array
    let userRole = userDetails.roles[0].name;
    const userRoleLength = userDetails.roles.length;

    // Check if the user has multiple roles
    if (userRoleLength > 1) {
      // Loop through each role to check for "vendor" or "admin"
      for (let i = 0; i < userRoleLength; i++) {
        if (userDetails.roles[i].name === "vendor") {
          userRole = "vendor"; // Assign "vendor" as the role if found
          break; // Exit the loop if "vendor" is found
        } else if (userDetails.roles[i].name === "admin") {
          userRole = "admin"; // Assign "admin" as the role if "vendor" isn't found
        }
      }
    }

      

  // Check if the user has roles assigned
  if (!userRole || userDetails.roles.length === 0) {
    return <div>You do not have permission to view any products.</div>;
  }

  // Check if the user is a vendor and has a vendorId
  if (userRole === "vendor" && !loggedInUserId) {
    return <div>You do not have a valid vendor account.</div>;
  }

  let products;

  // If the user is an admin, fetch all products
  if (userRole === "admin") {
    products = await api.get<PaginatedData<Product>>("products", searchParams);
  }
  // If the user is a vendor, fetch their products
  else if (userRole === "vendor") {
    // const UserId = "01hvstmhh5z859rf278r0b2ee8";
    const UserId = session?.branch?.vendorId;
    products = await api.get<PaginatedData<Product>>(
      `vendors/${UserId}/products`,
      searchParams
    );

  } else {
    // Handle other cases where the user is not an admin or vendor
    return <div>You do not have permission to view any products.</div>;
  }

  return (
    <div className="h-full min-h-screen bg-white p-5 dark:bg-default-900">
      <ProductsTable
        data={products?.data ?? []}
        meta={products?.meta!}
        session={session}
      />
    </div>
  );
}
