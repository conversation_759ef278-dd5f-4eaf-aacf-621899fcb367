import { api, imagePath } from "@/lib/api";
import { Metadata } from "next";
import Link from "next/link";
import AdminSpecialitiesCreateForm from "./create";
import AdminSpecialitiesEditForm from "./edit";
import { revalidatePath } from "next/cache";
import Image from "next/image";

export const dynamic = "force-dynamic";
export const fetchCache = "force-no-store";

export const metadata: Metadata = {
  title: "Specialities",
};

export default async function AdminSpecialityIndex() {
  const specialities = await api.get<PaginatedData<Speciality>>(
    "specialities",
    { per: 30 },
  );

  const storeSpeciality = async (speciality: FormData) => {
    "use server";

    await api.post("specialities", speciality);
    revalidatePath("/products/specialities");
  };

  const updateSpeciality = async (speciality: FormData) => {
    "use server";

    await api.put(`specialities/${speciality.get("id")}`, speciality);
    revalidatePath("/products/specialities");
  };

  return (
    <div className="flex flex-1 flex-col px-4 pt-8">
      <div className="grid flex-1 grid-cols-5 gap-3">
        {specialities?.data.map((speciality) => (
          <div
            className="rounded-md border border-default-200 p-4"
            key={speciality.id}
          >
            <div className="flex space-x-1">
              <Link href={`/products/specialities/${speciality.id}`}>
                <Image
                  src={imagePath(speciality.image?.url)}
                  alt={speciality.name}
                  width={100}
                  height={100}
                  className="size-16 rounded-full"
                />
              </Link>
              <div className="w-3/5">
                <Link href={`/products/specialities/${speciality.id}`}>
                  <h3>{speciality.name}</h3>
                </Link>
              </div>
            </div>

            <article>{speciality.details}</article>
          </div>
        ))}
      </div>

      <AdminSpecialitiesCreateForm storeSpeciality={storeSpeciality} />
    </div>
  );
}
