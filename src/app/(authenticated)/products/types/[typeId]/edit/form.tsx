"use client";

import { useState, useRef, ChangeEvent, useEffect } from "react";
import { SubmitHandler, useForm } from "react-hook-form";
import { toast } from "react-hot-toast";
import Image from "next/image";
import { useClickOutside } from "@/hooks/useClickOutside";

export default function ProductTypesEditForm({
  defaultValues,
  updateProductType,
}: {
  defaultValues: ProductType;
  updateProductType: (data: FormData) => Promise<void>;
}) {
  const [preview, setPreview] = useState<string>();

  const {
    watch,
    handleSubmit,
    register,
    reset,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<Partial<ProductType & { upload: File }>>({
    defaultValues,
  });

  const producttype = watch();

  const handleUploadedFile = (event: ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;

    if (files) {
      const file = files[0];
      setValue("upload", file);
      const urlImage = URL.createObjectURL(file);

      setPreview(urlImage);
    }
  };

  const editProductType: SubmitHandler<
    Partial<ProductType & { upload: File }>
  > = (producttype: Partial<ProductType & { upload: File }>) => {
    const data = new FormData();

    data.append("id", producttype.id!);
    data.append("name", producttype.name!);
    data.append("details", producttype.details!);
    data.append("image", producttype.upload!);

    toast
      .promise(
        updateProductType(data),
        {
          loading: "Updating producttype...",
          success: "ProductType has been saved 👌",
          error: "Could not save producttype 🤯",
        },
        {
          position: "bottom-center",
        },
      )
      .then(() => {
        reset({
          name: "",
          image: null,
          details: "",
        });

      });
  };

  useEffect(() => {
    // const producttypeImage = imagePath(defaultValues.image?.url, defaultValues.imageUrl)
    // setPreview(producttypeImage)
  }, []);

  return (
    <div
          id={`editProductTypeDrawer-${producttype.id}`}
          className="w-full p-4 bg-white rounded-lg h-full min-h-screen"
          tabIndex={-1}
          aria-labelledby="drawer-label"
          aria-hidden="true"
        >
          <form onSubmit={handleSubmit(editProductType)} className="w-full">
            <div className="space-y-4">
              <div>
                <label
                  htmlFor="name"
                  className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
                >
                  Name
                </label>
                <input
                  type="text"
                  {...register("name")}
                  id="name"
                  className="block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
                  placeholder="Type producttype name"
                  required
                />
              </div>

              <div>
                <label
                  htmlFor="image"
                  className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
                >
                  Image or icon
                </label>
                <label className="flex h-40 w-full items-center justify-center rounded-lg border border-dashed border-default-300 bg-default-50 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600">
                  <input
                    type="file"
                    name="image"
                    id="image"
                    className="hidden"
                    onChange={handleUploadedFile}
                  />
                  {preview ? (
                    <Image
                      src={preview}
                      alt="preview"
                      width={100}
                      height={100}
                      className="w-1/2"
                    />
                  ) : (
                    <p>Click to select file</p>
                  )}
                </label>
              </div>

              <div>
                <label
                  htmlFor="description"
                  className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
                >
                  Description
                </label>
                <textarea
                  id="description"
                  rows={4}
                  {...register("details")}
                  className=".5 block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
                  placeholder="Enter producttype description here"
                />
              </div>

              <div className="flex justify-end pb-4 md:absolute">
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-96 justify-center rounded-lg bg-primary px-5 py-3 text-center text-sm font-medium text-white hover:bg-default-800 focus:outline-none focus:ring-4 focus:ring-default-300 dark:bg-primary dark:hover:bg-primary dark:focus:ring-default-800"
                >
                  Update product type details
                </button>
              </div>
            </div>
          </form>
        </div>
    );
}
