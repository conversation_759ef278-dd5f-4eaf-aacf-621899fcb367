import { revalidatePath } from "next/cache";
import ProductTypesEditForm from "./form";
import { api } from "@/lib/api";
import { Metadata } from "next";

const getProductType = async (productTypeId: string) => {
  return await api.get<ProductType>(`product-types/${productTypeId}`);
};

export const generateMetadata = async ({
  params: { typeId },
}: {
  params: { typeId: string };
}): Promise<Metadata> => {
  const productType = await getProductType(typeId);

  return {
    title: `Edit ${productType?.name}`,
  };
};

export default async function page({
  params: { typeId },
}: {
  params: { typeId: string };
}) {
  const productType = await getProductType(typeId);

  const updateProductType = async (productType: FormData) => {
    "use server";

    await api.put(`product-types/${typeId}`, productType);

    revalidatePath(`/products/types/${typeId}`);
  };

  return (
    <div>
      {productType && (
        <ProductTypesEditForm
          defaultValues={productType}
          updateProductType={updateProductType}
        />
      )}
    </div>
  );
}
