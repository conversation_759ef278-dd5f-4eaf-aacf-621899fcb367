import { api, imagePath } from "@/lib/api";
import { Metadata } from "next";
import Link from "next/link";
import AdminProductCategoriesCreateForm from "./create";
import AdminProductCategoriesEditForm from "./edit";
import { revalidatePath } from "next/cache";
import Image from "next/image";
import PaginatedTable from "@/components/table";
import ProductTypeSearchForm from "./search";
import { auth } from "@/auth";

const getProductType = async (producttypeId: string) => {
  return await api.get<ProductType>(`product-types/${producttypeId}`);
};

export const generateMetadata = async ({
  params,
}: {
  params: { typeId: string };
}): Promise<Metadata> => {
  const producttype = await getProductType(params.typeId);

  return {
    title: producttype?.name,
  };
};

export default async function AdminProductCategoryIndex({
  params,
}: {
  params: { typeId: string };
}) {
  const session = await auth();
  const categories = await api.get<PaginatedData<ProductCategory>>(
    `product-types/${params.typeId}/categories`,
    { per: 30 },
  );

  const storeProductCategory = async (category: FormData) => {
    "use server";

    await api.post("product-categories", category);
    revalidatePath("/products/categories");
  };

  const updateProductCategory = async (category: FormData) => {
    "use server";

    await api.put(`product-categories/${category.get("id")}`, category);
    revalidatePath("/products/categories");
  };

  const deleteRecord = async (data: FormData) => {
    "use server";

    await api.destroy(data.get("id") as string, "product-categories");

    revalidatePath(`/products/types/${params.typeId}`);
  };

  
  return (
    <div className="py-4">
      {categories && (
        <PaginatedTable<ProductCategory>
          records={categories}
          title={"Categories"}
          columns={[
            {
              id: "name",
              title: "Category",
              render: (category) => (
                <div className="flex items-center space-x-1">
                  <Link href={`/products/categories/${category.id}`}>
                    <Image
                      src={imagePath(category.image?.url)}
                      alt={category.name}
                      width={250}
                      height={250}
                      className="size-16 rounded-full"
                    />
                  </Link>
                  <div className="w-3/5">
                    <Link href={`/products/categories/${category.id}`}>
                      <h3>{category.name}</h3>
                    </Link>
                  </div>
                </div>
              ),
            },
            {
              id: "slug",
              title: "slug",
              render: (category) => (
                <Link href={`/products/categories/${category.id}`}>
                  {category.slug}
                </Link>
              ),
            },
            { id: "details", title: "Details" },
            {
              id: "actions",
              title: "Actions",
              render: (category) => {
                return (
                  <div className="flex items-center space-x-2">
                    <AdminProductCategoriesEditForm
                      defaultValues={category}
                      updateProductCategory={updateProductCategory}
                    />
                    
                    {!session?.branch && <form className="text-primary" action={deleteRecord}>
                     
                      <button type="submit" className="text-red-500" name="id" value={category.id}>
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                          strokeWidth={1.5}
                          stroke="currentColor"
                          className="h-6 w-6"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"
                          />
                        </svg>
                      </button>
                    </form>}
                  </div>
                );
              },
            },
          ]}
          path={`/products/types/${params.typeId}`}
          tools={
            <div className="mb-4 flex justify-between px-4">
              <ProductTypeSearchForm />

              <AdminProductCategoriesCreateForm
                storeProductCategory={storeProductCategory}
                typeId={params.typeId}
              />
            </div>
          }
        />
      )}
    </div>
  );
}
