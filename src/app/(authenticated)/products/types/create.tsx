"use client";

import AsyncSelect from "react-select/async";
import { useState, ChangeEvent } from "react";
import makeAnimated from "react-select/animated";
import { Submit<PERSON><PERSON><PERSON>, Controller, useForm } from "react-hook-form";
import { toast } from "react-hot-toast";
import Image from "next/image";
import { useClickOutside } from "@/hooks/useClickOutside";

export default function AdminProductTypesCreateForm({
  serviceId,
  taskId,
  storeProductType,
  classNames = "fixed bottom-2 right-4 z-5 text-white bg-primary hover:bg-default-800 rounded-full size-16 dark:bg-primary flex justify-center items-center",
}: {
  storeProductType: (data: FormData) => Promise<void>;
  serviceId?: string;
  taskId?: string;
  classNames?: string;
}) {
  const [creating, setCreating] = useState(false);
  const [preview, setPreview] = useState<string>();

  const animatedComponents = makeAnimated();

  const ref = useClickOutside(() => setCreating(false));

  const {
    setValue,
    watch,
    handleSubmit,
    register,
    reset,
    control,
    formState: { errors, isSubmitting },
  } = useForm<Partial<ProductType & { upload: File; taskId?: string }>>({
    defaultValues: {
      //@ts-ignore
      serviceId: { value: serviceId },
      name: "",
    },
  });

  const producttype = watch();

  const { ref: registerRef, ...rest } = register("image");

  const handleUploadedFile = (event: ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;

    if (files) {
      const file = files[0];
      setValue("upload", file);
      const urlImage = URL.createObjectURL(file);

      setPreview(urlImage);
    }
  };

  const fetchServices = async (s: string) => {
    if (s.length > 3) {
      const services: Service[] = await fetch(
        //@ts-ignore
        `/api/tasks/${producttype.taskId.value}/services?s=${s}`,
      ).then((r) => r.json());

      return services?.map((service) => ({
        value: service.id,
        label: service.name,
      }));
    }

    return [];
  };

  const fetchTasks = async (s: string) => {
    if (s.length > 3) {
      const types: Task[] = await fetch(
        //@ts-ignore
        `/api/tasks?s=${s}`,
      ).then((r) => r.json());

      return types?.map((t) => ({ value: t.id, label: t.name }));
    }

    return [];
  };

  const fetchVendorCategories = async (s: string) => {
    if (s.length > 3) {
      const categories: VendorCategory[] = await fetch(
        //@ts-ignore
        `/api/vendor-types/${producttype.taskId.value}/categories?s=${s}`,
      ).then((r) => r.json());

      return categories?.map((category) => ({
        value: category.id,
        label: category.name,
      }));
    }

    return [];
  };

  const createProductType: SubmitHandler<
    Partial<ProductType & { upload: File }>
  > = (producttype: Partial<ProductType & { upload: File }>) => {
    const data = new FormData();

    data.append("name", producttype.name!);
    data.append("details", producttype.details!);
    data.append("image", producttype.upload!);
    //@ts-ignore
    data.append("serviceId", producttype.serviceId?.value!);

    toast.promise(
      storeProductType(data),
      {
        loading: "Creating product type...",
        success: "Product type has been saved 👌",
        error: "Could not save product type 🤯",
      },
      {
        position: "bottom-center",
      },
    );

    reset({
      name: "",
      image: null,
      details: "",
    });

    setCreating(false);
  };

  return (
    <>
      <button
        onClick={() => setCreating(!creating)}
        id="createProductTypeButton"
        className={classNames}
        type="button"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="currentColor"
          className="bi bi-plus-lg h-5 w-5"
          viewBox="0 0 16 16"
        >
          <path
            fillRule="evenodd"
            d="M8 2a.5.5 0 0 1 .5.5v5h5a.5.5 0 0 1 0 1h-5v5a.5.5 0 0 1-1 0v-5h-5a.5.5 0 0 1 0-1h5v-5A.5.5 0 0 1 8 2Z"
          />
        </svg>

        <span className="only">Create product type</span>
      </button>

      {creating && (
        <div
          ref={ref}
          id="drawer-create-producttype-default"
          className="fixed right-0 top-0 z-40 h-screen w-full max-w-xs overflow-y-auto bg-white p-4 transition-transform dark:bg-default-800"
          tabIndex={-1}
          aria-labelledby="drawer-label"
          aria-hidden="true"
        >
          <h5
            id="drawer-label"
            className="mb-6 inline-flex items-center text-sm font-semibold uppercase text-default-500 dark:text-default-400"
          >
            New product type
          </h5>
          <button
            onClick={() => setCreating(!creating)}
            type="button"
            data-drawer-dismiss="drawer-create-producttype-default"
            aria-controls="drawer-create-producttype-default"
            className="absolute right-2.5 top-2.5 inline-flex items-center rounded-lg bg-transparent p-1.5 text-sm text-default-400 hover:bg-default-200 hover:text-default-900 dark:hover:bg-default-600 dark:hover:text-white"
          >
            <svg
              aria-hidden="true"
              className="h-5 w-5"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fillRule="evenodd"
                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                clipRule="evenodd"
              ></path>
            </svg>
            <span className="sr-only">Close menu</span>
          </button>
          <form onSubmit={handleSubmit(createProductType)}>
            <div className="space-y-4">
              {!serviceId && (
                <>
                  {!taskId && (
                    <div className="mb-6">
                      <label
                        htmlFor="taskId"
                        className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
                      >
                        Select task
                      </label>
                      <Controller
                        name="taskId"
                        control={control}
                        render={({ field: { onChange, value } }) => (
                          <AsyncSelect
                            isClearable
                            isSearchable
                            // cacheOptions
                            //@ts-ignore
                            loadOptions={fetchTasks}
                            components={animatedComponents}
                            placeholder="Select task"
                            required
                            classNames={{
                              control: () => "!py-[1px] !rounded-lg",
                              menu: () => "py-1",
                              input: () => "w-full [&>*]:!w-full",
                            }}
                            onChange={onChange}
                            value={value}
                          />
                        )}
                      />
                    </div>
                  )}

                  {producttype.taskId && !serviceId && (
                    <div className="mb-6">
                      <label
                        htmlFor="serviceId"
                        className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
                      >
                        Select services
                      </label>
                      <Controller
                        name="serviceId"
                        control={control}
                        rules={{ required: true }}
                        render={({ field: { onChange, value } }) => (
                          <AsyncSelect
                            isClearable
                            isSearchable
                            // cacheOptions
                            //@ts-ignore
                            loadOptions={fetchServices}
                            components={animatedComponents}
                            placeholder="Select services"
                            required
                            classNames={{
                              control: () => "!py-[1px] !rounded-lg",
                              input: () => "w-full",
                            }}
                            onChange={onChange}
                            value={value}
                          />
                        )}
                      />
                    </div>
                  )}
                </>
              )}

              <div>
                <label
                  htmlFor="name"
                  className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
                >
                  Name
                </label>
                <input
                  type="text"
                  {...register("name")}
                  id="name"
                  className="block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
                  placeholder="Type product type name"
                  required
                />
              </div>

              <div>
                <label
                  htmlFor="image"
                  className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
                >
                  Image or icon
                </label>
                <label className="flex h-40 w-full items-center justify-center rounded-lg border border-dashed border-default-300 bg-default-50 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600">
                  <input
                    type="file"
                    name="image"
                    id="image"
                    className="hidden"
                    onChange={handleUploadedFile}
                  />
                  {preview ? (
                    <Image
                      src={preview}
                      alt="preview"
                      width={100}
                      height={100}
                      className="w-1/2"
                    />
                  ) : (
                    <p>Click to select file</p>
                  )}
                </label>
              </div>

              <div>
                <label
                  htmlFor="description"
                  className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
                >
                  Description
                </label>
                <textarea
                  id="description"
                  rows={4}
                  {...register("details")}
                  className=".5 block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
                  placeholder="Enter product type description here"
                />
              </div>

              <div className="bottom-0 left-0 flex w-full justify-center space-x-4 pb-4 md:absolute md:px-4">
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full justify-center rounded-lg bg-primary px-5 py-3 text-center text-sm font-medium text-white hover:bg-default-800 focus:outline-none focus:ring-4 focus:ring-default-300 dark:bg-primary dark:hover:bg-primary dark:focus:ring-default-800"
                >
                  Save type details
                </button>
              </div>
            </div>
          </form>
        </div>
      )}
    </>
  );
}
