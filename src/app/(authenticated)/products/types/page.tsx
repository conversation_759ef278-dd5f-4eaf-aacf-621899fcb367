import { api, imagePath } from "@/lib/api";
import { Metadata } from "next";
import Link from "next/link";
import AdminProductTypesCreateForm from "./create";
import AdminProductTypesEditForm from "./edit";
import { revalidatePath } from "next/cache";
import Image from "next/image";

export const dynamic = "force-dynamic";
export const fetchCache = "force-no-store";

export const metadata: Metadata = {
  title: "Product types",
};

export default async function AdminProductTypeIndex() {
  const producttypes = await api.get<PaginatedData<ProductType>>(
    "product-types",
    { per: 30 },
  );

  const storeProductType = async (producttype: FormData) => {
    "use server";

    await api.post("product-types", producttype);
    revalidatePath("/products/types");
  };

  const updateProductType = async (producttype: FormData) => {
    "use server";

    await api.put(`product-types/${producttype.get("id")}`, producttype);
    revalidatePath("/products/types");
  };

  return (
    <div className="flex flex-1 flex-col px-4 pt-8">
      <div className="grid flex-1 grid-cols-5 gap-3">
        {producttypes?.data.map((producttype) => (
          <div
            className="rounded-md border border-default-200 p-4"
            key={producttype.id}
          >
            <div className="flex space-x-1">
              <Link href={`/products/types/${producttype.id}`}>
                <Image
                  src={imagePath(producttype.image?.url)}
                  alt={producttype.name}
                  width={100}
                  height={100}
                  className="size-16 rounded-full"
                />
              </Link>
              <div className="w-3/5">
                <div className="flex justify-end">
                  <AdminProductTypesEditForm
                    defaultValues={producttype}
                    updateProductType={updateProductType}
                  />
                </div>

                <Link href={`/products/types/${producttype.id}`}>
                  <h3>{producttype.name}</h3>
                </Link>
              </div>
            </div>

            <article>{producttype.details}</article>
          </div>
        ))}
      </div>

      <AdminProductTypesCreateForm storeProductType={storeProductType} />
    </div>
  );
}
