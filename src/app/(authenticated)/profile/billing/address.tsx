"use client";

import Select from "react-select";

export default function ProfileBillingAddress({
  defaultValues,
  updateProfile,
}: {
  defaultValues: User;
  updateProfile: (data: User) => Promise<void>;
}) {
  return (
    <form className="p-6">
      <div className="grid gap-6 lg:grid-cols-2">
        <div className="relative max-w-full">
          <label
            className="mb-2 block text-sm font-medium text-default-900"
            htmlFor="bfName"
          >
            First Name
          </label>
          <div className="relative max-w-full">
            <input
              type="text"
              placeholder="Enter Your First Name"
              name="bfName"
              id="bfName"
              className="form-input w-full rounded-lg border border-default-200 px-4 py-3 dark:bg-default-50"
            />
          </div>
        </div>
        <div className="relative max-w-full">
          <label
            className="mb-2 block text-sm font-medium text-default-900"
            htmlFor="blName"
          >
            Last Name
          </label>
          <div className="relative max-w-full">
            <input
              type="text"
              placeholder="Enter Your Last Name"
              name="blName"
              id="blName"
              className="form-input w-full rounded-lg border border-default-200 px-4 py-3 dark:bg-default-50"
            />
          </div>
        </div>
        <div className="relative max-w-full lg:col-span-2">
          <label
            className="mb-2 block text-sm font-medium text-default-900"
            htmlFor="bcompanyName"
          >
            Company Name (Optional)
          </label>
          <div className="relative max-w-full">
            <input
              type="text"
              placeholder="Enter Your Company Name"
              name="bcompanyName"
              id="bcompanyName"
              className="form-input w-full rounded-lg border border-default-200 px-4 py-3 dark:bg-default-50"
            />
          </div>
        </div>
        <div className="relative w-full lg:col-span-2">
          <label
            className="mb-2 block text-sm font-medium text-default-900"
            htmlFor="baddress"
          >
            Address
          </label>
          <div className="relative w-full">
            <textarea
              placeholder="Road No. 47/x, House no. 123/B, Flat No. B4"
              name="baddress"
              rows={3}
              className="w-full rounded-lg border border-default-200 px-4 py-3 dark:bg-default-50"
            ></textarea>
          </div>
        </div>
        <div className="lg:col-span-2">
          <label
            htmlFor="billing-country1"
            className="mb-2 block text-sm font-medium text-default-900"
          >
            Country/Region
          </label>
          <div className="relative">
            <div
              className="css-b62m3t-container block w-full cursor-pointer rounded-lg border border-default-200 bg-transparent focus-within:border focus-within:border-primary dark:bg-default-50"
              id="billing-country1"
            >
              <Select />
            </div>
          </div>
        </div>
        <div className="lg:col-span-2">
          <label
            htmlFor="billing-state-province1"
            className="mb-2 block text-sm font-medium text-default-900"
          >
            State/Province
          </label>
          <div className="relative">
            <div
              className="css-b62m3t-container block w-full cursor-pointer rounded-lg border border-default-200 bg-transparent focus-within:border focus-within:border-primary dark:bg-default-50"
              id="billing-state-province1"
            >
              <Select />
            </div>
          </div>
        </div>
        <div>
          <label
            htmlFor="billing-city1"
            className="mb-2 block text-sm font-medium text-default-900"
          >
            City
          </label>
          <div className="relative">
            <div
              className="css-b62m3t-container block w-full cursor-pointer rounded-lg border border-default-200 bg-transparent focus-within:border focus-within:border-primary dark:bg-default-50"
              id="billing-city1"
            >
              <Select />
            </div>
          </div>
        </div>
        <div>
          <label
            htmlFor="billing-zip-code1"
            className="mb-2 block text-sm font-medium text-default-900"
          >
            ZIP/Postal Code
          </label>
          <div className="relative">
            <div
              className="css-b62m3t-container block w-full cursor-pointer rounded-lg border border-default-200 bg-transparent focus-within:border focus-within:border-primary dark:bg-default-50"
              id="billing-zip-code1"
            >
              <Select />
            </div>
          </div>
        </div>
        <div className="relative max-w-full lg:col-span-2">
          <label
            className="mb-2 block text-sm font-medium text-default-900"
            htmlFor="bemail"
          >
            Email
          </label>
          <div className="relative max-w-full">
            <input
              type="text"
              placeholder="<EMAIL>"
              name="bemail"
              id="bemail"
              className="form-input w-full rounded-lg border border-default-200 px-4 py-3 dark:bg-default-50"
            />
          </div>
        </div>
        <div className="relative max-w-full lg:col-span-2">
          <label
            className="mb-2 block text-sm font-medium text-default-900"
            htmlFor="bphoneNo"
          >
            Phone Number
          </label>
          <div className="relative max-w-full">
            <input
              type="text"
              placeholder="******-XXX-4567"
              name="bphoneNo"
              id="bphoneNo"
              className="form-input w-full rounded-lg border border-default-200 px-4 py-3 dark:bg-default-50"
            />
          </div>
        </div>
        <div>
          <button
            type="submit"
            className="flex items-center justify-center gap-2 rounded-lg border border-primary bg-primary px-6 py-3 text-center text-sm font-semibold text-white shadow-sm transition-all duration-200 hover:border-default-700 hover:bg-default-500"
          >
            Save Changes
          </button>
        </div>
      </div>
    </form>
  );
}
