import { api } from "@/lib/api";
import ProfileBillingAddress from "./address";
import { cache } from "react";
import { Metadata } from "next";

const fetchUser = cache(() => api.get<User>("auth/me"));

export const metadata: Metadata = {
  title: "Billing Address",
};

export default async function page() {
  const user = await fetchUser();
  return (
    <div>
      {user && (
        <ProfileBillingAddress
          defaultValues={user}
          updateProfile={async (data: User) => {
            "use server";

            await api.put(`/users/${user?.id}`, data);
          }}
        />
      )}
    </div>
  );
}
