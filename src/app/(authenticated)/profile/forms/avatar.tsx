"use client";

import { imagePath } from "@/lib/api";
import { useState } from "react";

export default function ProfileFormsAvatar({ user }: { user: User }) {
  const [files, setFiles] = useState<[]>([]);

  return (
    <label
      className="flex h-80 items-center justify-center rounded-lg border border-dashed"
      role="button"
      htmlFor="filepond--browser-ql8p2ymcq"
      style={{
        backgroundImage: `url(${imagePath(user.avatar?.url, user.avatarUrl)})`,
        backgroundSize: "cover",
        backgroundPosition: "center",
      }}
    >
      <input
        type="file"
        id="filepond--browser-ql8p2ymcq"
        name="filepond"
        aria-controls="filepond--assistant-ql8p2ymcq"
        aria-labelledby="filepond--drop-label-ql8p2ymcq"
        className="hidden"
      />
      <p id="filepond--drop-label-ql8p2ymcq" aria-hidden="true">
        Add Photo
      </p>
    </label>
  );
}
