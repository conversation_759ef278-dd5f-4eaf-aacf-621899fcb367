"use client";

import { useForm } from "react-hook-form";
import Select from "react-select";

export default function ProfileFormsDetails({ user }: { user: User }) {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues: user,
  });
  return (
    <form className="grid gap-6 lg:grid-cols-2">
      <div className="relative max-w-full">
        <label
          className="mb-2 block text-sm font-medium text-default-900"
          htmlFor="fName"
        >
          First Name
        </label>
        <div className="relative max-w-full">
          <input
            type="text"
            placeholder="Enter Your First Name"
            {...register("firstName", { required: true })}
            className="form-input w-full rounded-lg border border-default-200 px-4 py-3 dark:bg-default-50"
          />
        </div>
      </div>
      <div className="relative max-w-full">
        <label
          className="mb-2 block text-sm font-medium text-default-900"
          htmlFor="lName"
        >
          Last Name
        </label>
        <div className="relative max-w-full">
          <input
            type="text"
            placeholder="Enter Your Last Name"
            {...register("lastName", { required: true })}
            className="form-input w-full rounded-lg border border-default-200 px-4 py-3 dark:bg-default-50"
          />
        </div>
      </div>
      <div className="relative max-w-full">
        <label
          className="mb-2 block text-sm font-medium text-default-900"
          htmlFor="lName"
        >
          ID/Passport
        </label>
        <div className="relative max-w-full">
          <input
            type="text"
            placeholder="Enter Your User Name"
            {...register("idpass", { required: true })}
            className="form-input w-full rounded-lg border border-default-200 px-4 py-3 dark:bg-default-50"
          />
        </div>
      </div>
      <div className="relative max-w-full">
        <label
          className="mb-2 block text-sm font-medium text-default-900"
          htmlFor="email"
        >
          Email
        </label>
        <div className="relative max-w-full">
          <input
            type="email"
            placeholder="<EMAIL>"
            {...register("email", { required: true })}
            className="form-input w-full rounded-lg border border-default-200 px-4 py-3 dark:bg-default-50"
          />
        </div>
      </div>
      <div className="relative max-w-full">
        <label
          className="mb-2 block text-sm font-medium text-default-900"
          htmlFor="phoneNo"
        >
          Phone Number
        </label>
        <div className="relative max-w-full">
          <input
            type="text"
            placeholder="******-XXX-4567"
            {...register("phone", { required: true })}
            className="form-input w-full rounded-lg border border-default-200 px-4 py-3 dark:bg-default-50"
          />
        </div>
      </div>
      <div>
        <label
          htmlFor="billing-country2"
          className="mb-2 block text-sm font-medium text-default-900"
        >
          Country
        </label>
        <div className="relative">
          <div
            className="css-b62m3t-container block w-full cursor-pointer rounded-lg border border-default-200 bg-transparent focus-within:border focus-within:border-primary dark:bg-default-50"
            id="billing-country2"
          >
            <Select />
          </div>
        </div>
      </div>
      <div>
        <label
          htmlFor="billing-state-province2"
          className="mb-2 block text-sm font-medium text-default-900"
        >
          State/Province
        </label>
        <div className="relative">
          <div
            className="css-b62m3t-container block w-full cursor-pointer rounded-lg border border-default-200 bg-transparent focus-within:border focus-within:border-primary dark:bg-default-50"
            id="billing-state-province2"
          >
            <Select />
          </div>
        </div>
      </div>
      <div>
        <label
          htmlFor="billing-zip-code2"
          className="mb-2 block text-sm font-medium text-default-900"
        >
          ZIP/Postal Code
        </label>
        <div className="relative">
          <div
            className="css-b62m3t-container block w-full cursor-pointer rounded-lg border border-default-200 bg-transparent focus-within:border focus-within:border-primary dark:bg-default-50"
            id="billing-zip-code2"
          >
            <Select />
          </div>
        </div>
      </div>
      <div>
        <button
          type="submit"
          className="flex items-center justify-center gap-2 rounded-lg border border-primary bg-primary px-6 py-3 text-center text-sm font-semibold text-white shadow-sm transition-all duration-200 hover:bg-default-500"
        >
          Save Changes
        </button>
      </div>
    </form>
  );
}
