"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { useState } from "react";

export default function ProfileNav() {
  const path = usePathname();
  const tabs: Record<string, string> = {
    profile: "Profile",
    billing: "Billing",
    shipping: "Shipping",
    security: "Security",
    notifications: "Notifications",
  };

  return (
    <div>
      <ul className="flex flex-wrap border-b border-default-200 text-center text-sm font-medium text-default-500 dark:border-default-700 dark:text-default-400">
        {Object.keys(tabs).map((tab) => (
          <li className="me-2" key={tab}>
            <Link
              href={tab === "profile" ? "/profile" : `/profile/${tab}`}
              aria-current="page"
              className={
                "inline-block p-4 " +
                (path === (tab === "profile" ? "/profile" : `/profile/${tab}`)
                  ? "border-b-2 border-primary text-primary"
                  : "")
              }
            >
              {tabs[tab]}
            </Link>
          </li>
        ))}
      </ul>
    </div>
  );
}
