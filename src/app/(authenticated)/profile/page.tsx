import { api } from "@/lib/api";
import ProfileFormsAvatar from "./forms/avatar";
import ProfileFormsDetails from "./forms/details";
import ProfileFormsPassword from "./security/password";
import ProfileFormsAddress from "./forms/address";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "My Profile",
};

export default async function page() {
  const user = await api.get<User>("auth/me");

  const updateProfile = async (data: User) => {
    "use server";

    await api.put(`/users/${user?.id}`, data);
  };

  return (
    <div className="p-6">
      <div className="grid gap-6 xl:grid-cols-5">
        <div className="xl:col-span-1">
          <div className="mx-auto">
            {user && <ProfileFormsAvatar user={user} />}
          </div>
        </div>
        <div className="xl:col-span-4">
          {user && <ProfileFormsDetails user={user} />}
        </div>
      </div>
    </div>
  );
}
