import { api } from "@/lib/api";
import ProfileFormPassword from "./password";
import { cache } from "react";
import { Metadata } from "next";

const fetchUser = cache(() => api.get<User>("auth/me"));

export const metadata: Metadata = {
  title: "Change Password",
};

export default async function page() {
  const user = await fetchUser();
  return (
    <div className="p-6">
      {user && (
        <ProfileFormPassword
          defaultValues={user}
          updateProfile={async (data: User) => {
            "use server";

            await api.put(`/users/${user?.id}`, data);
          }}
        />
      )}
    </div>
  );
}
