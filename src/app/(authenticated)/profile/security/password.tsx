"use client";

import Select from "react-select";

export default function ProfileFormPassword({
  defaultValues,
  updateProfile,
}: {
  defaultValues: User;
  updateProfile: (data: User) => Promise<void>;
}) {
  return (
    <form>
      <div className="relative mb-4 w-full">
        <label
          className="mb-2 block text-sm font-medium text-default-900"
          htmlFor="currentPassword"
        >
          Current Password
        </label>
        <div className="flex w-full">
          <div className="relative w-full">
            <input
              placeholder="Enter your password"
              name="currentPassword"
              type="password"
              className="w-full rounded-e-none rounded-s-lg border border-default-200 px-4 py-3 focus:border-primary dark:bg-default-50"
            />
          </div>
          <button
            className="password-toggle ms-[1px] inline-flex items-center justify-center rounded-e-lg border border-s-0 border-default-200 bg-white px-4 py-3 dark:bg-default-50"
            type="button"
          >
            <svg
              stroke="currentColor"
              fill="none"
              strokeWidth="2"
              viewBox="0 0 24 24"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-default-600"
              height="20"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path>
              <circle cx="12" cy="12" r={3}></circle>
            </svg>
          </button>
        </div>
      </div>
      <div className="relative mb-4 w-full">
        <label
          className="mb-2 block text-sm font-medium text-default-900"
          htmlFor="newPassword"
        >
          New Password
        </label>
        <div className="flex w-full">
          <div className="relative w-full">
            <input
              placeholder="Enter your new password"
              name="newPassword"
              type="password"
              className="w-full rounded-e-none rounded-s-lg border border-default-200 px-4 py-3 focus:border-primary dark:bg-default-50"
            />
          </div>
          <button
            className="password-toggle ms-[1px] inline-flex items-center justify-center rounded-e-lg border border-s-0 border-default-200 bg-white px-4 py-3 dark:bg-default-50"
            type="button"
          >
            <svg
              stroke="currentColor"
              fill="none"
              strokeWidth="2"
              viewBox="0 0 24 24"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-default-600"
              height="20"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path>
              <circle cx="12" cy="12" r={3}></circle>
            </svg>
          </button>
        </div>
      </div>
      <div className="relative mb-4 w-full">
        <label
          className="mb-2 block text-sm font-medium text-default-900"
          htmlFor="confirmPassword"
        >
          Confirm Password
        </label>
        <div className="flex w-full">
          <div className="relative w-full">
            <input
              placeholder="Enter your confirm password"
              name="confirmPassword"
              type="password"
              className="w-full rounded-e-none rounded-s-lg border border-default-200 px-4 py-3 focus:border-primary dark:bg-default-50"
            />
          </div>
          <button
            className="password-toggle ms-[1px] inline-flex items-center justify-center rounded-e-lg border border-s-0 border-default-200 bg-white px-4 py-3 dark:bg-default-50"
            type="button"
          >
            <svg
              stroke="currentColor"
              fill="none"
              strokeWidth="2"
              viewBox="0 0 24 24"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-default-600"
              height="20"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path>
              <circle cx="12" cy="12" r={3}></circle>
            </svg>
          </button>
        </div>
      </div>
      <div>
        <button
          type="submit"
          className="flex items-center justify-center gap-2 rounded-lg border border-primary bg-primary px-6 py-3 text-center text-sm font-semibold text-white shadow-sm transition-all duration-200  hover:bg-default-500"
        >
          Save Changes
        </button>
      </div>
    </form>
  );
}
