import { api } from "@/lib/api";
import ProfileShippingAddress from "./address";
import { cache } from "react";
import exp from "constants";
import { Metadata } from "next";

const fetchUser = cache(() => api.get<User>("auth/me"));

export const metadata: Metadata = {
  title: "Shipping Address",
};

export default async function page() {
  const user = await fetchUser();
  return (
    <div className="p-6">
      {user && (
        <ProfileShippingAddress
          defaultValues={user}
          updateProfile={async (data: User) => {
            "use server";

            await api.put(`/users/${user?.id}`, data);
          }}
        />
      )}
    </div>
  );
}
