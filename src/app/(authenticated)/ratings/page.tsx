import { auth } from "@/auth";
import { Metadata } from "next";
import Link from "next/link";

export const metadata: Metadata = {
  title: "Ratings",
};

export default async function page() {
  const session = await auth();

  const sections = [
    {
      id: "vendor",
      name: "Business",
      details: "Vendor ratings",
      href: `/ratings/vendors/${session?.vendor?.id}`,
    },
  ];

  return (
    <div className="grid grid-cols-3 gap-3 p-5">
      {sections.map((section) => (
        <div
          key={section.id}
          className="flex items-center justify-between rounded-lg border border-default-200 p-4"
        >
          <div>
            <h2 className="text-2xl font-bold">{section.name}</h2>
            <p>{section.details}</p>
          </div>

          <Link
            href={section.href}
            className="mt-5 rounded-lg bg-primary px-5 py-3 text-white"
          >
            View ratings
          </Link>
        </div>
      ))}
    </div>
  );
}
