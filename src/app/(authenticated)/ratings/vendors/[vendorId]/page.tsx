import { auth } from "@/auth";
import PaginatedTable from "@/components/table";
import { api } from "@/lib/api";
import Link from "next/link";

import { cache } from "react";
import { Metadata } from "next";
import VendorStars from "./stars";

const fetchVendor = cache((id: string) => api.get<Vendor>(`vendors/${id}`));

export const generateMetadata = async ({
  params,
}: {
  params: {
    vendorId: string;
  };
}): Promise<Metadata> => {
  const vendor = await fetchVendor(params.vendorId);

  return {
    title: `${vendor?.name} Ratings`,
    description: "Ratings",
  };
};

export default async function page() {
  const session = await auth();

  const ratings = await api.get<PaginatedData<VendorRating>>(`vendor-ratings`, {
    vendor: String(session?.vendor?.id),
  });

  return (
    <div>
      {ratings && (
        <PaginatedTable<VendorRating>
          records={ratings}
          columns={[
            {
              id: "customer",
              title: "Customer",
              render: (rating) => (
                <Link href={`/customers/${rating.customerId}`}>
                  {rating.customer?.name}
                </Link>
              ),
            },
            {
              id: "points",
              title: "Rating",
              render: (rating) => <VendorStars initialValue={rating.points} />,
              class: "[&>span>span>span]:flex",
            },
            {
              id: "createdAt",
              title: "Rated on",
            },
          ]}
          title="Ratings"
          path="/ratings"
        />
      )}
    </div>
  );
}
