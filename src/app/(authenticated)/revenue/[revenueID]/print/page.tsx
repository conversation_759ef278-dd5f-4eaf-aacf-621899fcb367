import { cache } from "react";
import PrintPage from "./print";
import { api } from "@/lib/api";
import Image from "next/image";

const fetchPayment = cache((id: string) => api.get<Payment>(`/payments/${id}`));

export default async function page({
  params,
}: {
  params: {
    paymentId: string;
  };
}) {
  const payment = await fetchPayment(params.paymentId);

  return (
    <div className="flex justify-center p-6">
      {/* {JSON.stringify(payment, null, 2)} */}

      {payment && (
        <div className="mx-auto w-1/3 space-y-5">
          <h1>Payment {payment.ref}</h1>
          <h2>KES {payment.amount}</h2>

          {/* receipt */}
          <h3>Receipt: {payment.receipt}</h3>

          <Image src={payment.barcode as string} height={200} className="w-full" alt="" />
        </div>
      )}
      <PrintPage />
    </div>
  );
}
