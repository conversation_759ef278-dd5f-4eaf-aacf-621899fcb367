"use client";

import React from "react";
import { useState } from "react";
import toast from "react-hot-toast";
import Select from "react-select";

export default function AdminBranchStaffAssignForm({
  lots,
  user,
  onAssign,
}: {
  lots: Lot[];
  user: User & { lots: Lot[] };
  onAssign: (tables: string[], userId: string) => Promise<void>;
}) {
  const [selectedLots, setSelectedLots] = useState<string[]>(
    user.lots.map((l) => l.id),
  );
  const onSubmit = () => {
    toast.promise(onAssign(selectedLots, user.id), {
      loading: "Assigning tables...",
      success: "Tables assigned!",
      error: "Error assigning tables",
    });
  };

  return (
    <form
      id={`assign-tables-${user.id}`}
      onSubmit={(e) => {
        e.preventDefault();
        onSubmit();
      }}
      className="flex space-x-2"
    >
      <Select
        options={lots}
        defaultValue={user.lots}
        getOptionLabel={(lot) => lot.name}
        getOptionValue={(lot) => lot.id}
        isMulti
        isSearchable
        classNames={{
          container: () => "w-2/3 ml-2 rounded-lg",
          control: () => "!rounded-lg !border-default-600",
        }}
        onChange={(l) => setSelectedLots(l.map((l) => l.id))}
      />

      <button
        type="submit"
        className="rounded-lg bg-primary px-4 py-2 text-white"
      >
        Assign
      </button>
    </form>
  );
}
