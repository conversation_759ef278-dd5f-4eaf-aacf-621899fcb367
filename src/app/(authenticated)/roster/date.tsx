"use client";

import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { useState } from "react";
import ReactDatePicker from "react-datepicker";

export default function PickDate() {
  const searchParams = useSearchParams();

  const [startDate, setStartDate] = useState(
    searchParams.has("start")
      ? new Date(searchParams.get("start")!)
      : new Date(),
  );
  const [endDate, setEndDate] = useState(
    searchParams.has("end") ? new Date(searchParams.get("end")!) : new Date(),
  );

  return (
    <div className="flex space-x-2">
      <ReactDatePicker
        selected={startDate}
        onChange={(date) => setStartDate(date!)}
        showTimeSelect
        className="rounded-lg border border-primary px-4 py-2"
        dateFormat="dd/MM/yyyy h:mm aa"
      />
      
      <ReactDatePicker
        selected={endDate}
        onChange={(date) => setEndDate(date!)}
        showTimeSelect
        className="rounded-lg border border-primary px-4 py-2"
        dateFormat="dd/MM/yyyy h:mm aa"
      />

      <Link
        className="rounded-lg bg-primary px-4 py-2 text-white"
        href={`/roster?start=${startDate.toISOString()}&end=${endDate.toISOString()}`}
      >
        Set dates
      </Link>
    </div>
  );
}
