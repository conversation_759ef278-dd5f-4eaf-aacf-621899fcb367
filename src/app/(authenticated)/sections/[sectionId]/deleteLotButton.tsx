"use client";

import { removeLot, removeSection } from "@/actions/sections";
import { toast } from "react-hot-toast";

interface DeleteButtonProps {
  id: string;
  name: string
  sectionRevalidatePath: string
//   deleteHandler: ({itemToDelete, sectionRevalidatePath}:{itemToDelete: string, sectionRevalidatePath: string}) => Promise<any>
}

const DeleteButton = ({ id, sectionRevalidatePath, name }: DeleteButtonProps) => {
  const handleDelete = async () => {
    toast.promise(
        name === "lot"
            ?
                removeLot({itemToDelete: id, sectionRevalidatePath: sectionRevalidatePath})
            : 
                removeSection({itemToDelete: id, sectionRevalidatePath: sectionRevalidatePath}),
      {
        loading: `Deleting ${name}...`,
        success: `${name[0].toUpperCase()+ name.slice(1,name.length)} has been deleted 👌`,
        error: `Could not delete ${name} 🤯`
      },
      {
        position: "bottom-center",
      }
    );
  };

  return (
    <button
      onClick={handleDelete}
      className="flex items-center space-x-2 p-2 hover:bg-default-800 rounded-lg text-default-600 text-sm hover:cursor-pointer"
      type="button"
    >
      Delete
    </button>
  );
};

export default DeleteButton;
