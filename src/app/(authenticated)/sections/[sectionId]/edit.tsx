"use client";

import { useState, useEffect, ChangeEvent } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Controller, useForm } from "react-hook-form";
import { toast } from "react-hot-toast";
import Image from "next/image";
import React from "react";

export default function SectionLotEditForm({
  updateLot,
  defaultValues,
}: {
  updateLot: (data: FormData) => Promise<void>;
  defaultValues: Lot;
}) {
  const [creating, setCreating] = useState(false);
  const [preview, setPreview] = useState<string>();

  const {
    watch,
    handleSubmit,
    register,
    reset,
    control,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<
    Lot & {
      upload: File;
      taskId: string;
    }
  >({
    defaultValues,
  });

  const lot = watch();

  const handleUploadedFile = (event: ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;

    if (files) {
      const file = files[0];
      setValue("upload", file);
      const urlImage = URL.createObjectURL(file);

      setPreview(urlImage);
    }
  };

  const editLot: SubmitHandler<Lot & { upload: File }> = (
    lot: Lot & { upload: File },
  ) => {
    const data = new FormData();

    data.append("id", lot.id);
    data.append("name", lot.name);
    data.append("details", lot.details);

    if (lot.upload) {
      data.append("image", lot.upload);
    }

    toast
      .promise(
        updateLot(data),
        {
          loading: "Updating lot...",
          success: "Lot has been updated 👌",
          error: "Could not update lot 🤯",
        },
        {
          position: "bottom-center",
        },
      )
      .then(() => {
        reset();

        setCreating(false);
      })
      .catch((e) => {
        console.error(e);
      });
  };

  useEffect(() => {
    // imagePath(lot.image?.url).then(setPreview);
  }, []);

  return (
    <>
      <a
        href="#"
        onClick={() => setCreating(!creating)}
        id="editLotButton"
        className="rounded-lg px-5 py-2 text-sm font-medium text-default-600 hover:bg-default-800 focus:ring-4 focus:ring-default-300"
        type="button"
      >
        Edit
      </a>

      {creating && (
        <div
          id="drawer-edit-lot-default"
          className="fixed right-0 top-0 z-40 h-screen w-full max-w-xs overflow-y-scroll bg-white p-4 transition-transform dark:bg-default-800"
          tabIndex={-1}
          aria-labelledby="drawer-label"
          aria-hidden="true"
        >
          <h5
            id="drawer-label"
            className="mb-6 inline-flex items-center text-sm font-semibold uppercase text-default-500 dark:text-default-400"
          >
            {lot.name}
          </h5>
          <button
            onClick={() => setCreating(!creating)}
            type="button"
            data-drawer-dismiss="drawer-edit-lot-default"
            aria-controls="drawer-edit-lot-default"
            className="absolute right-2.5 top-2.5 inline-flex items-center rounded-lg bg-transparent p-1.5 text-sm text-default-400 hover:bg-default-200 hover:text-default-900 dark:hover:bg-default-600 dark:hover:text-white"
          >
            <svg
              aria-hidden="true"
              className="h-5 w-5"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fillRule="evenodd"
                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                clipRule="evenodd"
              ></path>
            </svg>
            <span className="sr-only">Close menu</span>
          </button>

          <form
            action="#"
            className="overflow-y-auto"
            onSubmit={handleSubmit(editLot)}
          >
            <div className="space-y-4">
              <div>
                <input
                  type="text"
                  id="name"
                  {...register("name")}
                  className="block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
                  placeholder="Type lot name"
                  required
                />
              </div>

              <div>
                <label
                  htmlFor="logo"
                  className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
                >
                  Lot image
                </label>
                <label className="flex h-24 w-full items-center justify-center rounded-lg border border-dashed border-default-300 bg-default-50 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600">
                  <input
                    type="file"
                    name="logo"
                    id="logo"
                    className="hidden"
                    onChange={handleUploadedFile}
                  />
                  {preview ? (
                    <Image
                      src={preview}
                      alt="preview"
                      width={100}
                      height={50}
                      className="w-1/2"
                    />
                  ) : (
                    <p>Click to select file</p>
                  )}
                </label>
              </div>

              <div>
                <label
                  htmlFor="description"
                  className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
                >
                  Description
                </label>
                <textarea
                  id="description"
                  rows={4}
                  {...register("details")}
                  className=".5 block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
                  placeholder="Enter event description here"
                />
              </div>

              <div className="bottom-0 left-0 z-50 flex w-full justify-center space-x-4 pb-4 md:absolute md:px-4">
                <button
                  type="submit"
                  className="w-full justify-center rounded-lg bg-primary px-5 py-3 text-center text-sm font-medium text-white hover:bg-default-800 focus:outline-none focus:ring-4 focus:ring-default-300 dark:bg-primary dark:hover:bg-primary dark:focus:ring-default-800"
                >
                  Save lot details
                </button>
              </div>
            </div>
          </form>
        </div>
      )}
    </>
  );
}
