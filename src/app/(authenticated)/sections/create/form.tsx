"use client";

import { useState, ChangeEvent } from "react";
import { SubmitHandler, useForm } from "react-hook-form";
import { toast } from "react-hot-toast";
import Image from "next/image";
import { useClickOutside } from "@/hooks/useClickOutside";
import { useRouter } from "next/navigation";
import React from "react";

export default function BranchSectionsCreateForm({
  storeSection,
}: {
  storeSection: (data: FormData) => Promise<void>;
}) {
  const router = useRouter();
  const [preview, setPreview] = useState<string>();

  const {
    setValue,
    watch,
    handleSubmit,
    register,
    reset,
    control,
    formState: { errors, isSubmitting },
  } = useForm<Section & { upload: File }>();

  const section = watch();

  const { ref: registerRef, ...rest } = register("image");

  const handleUploadedFile = (event: ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;

    if (files) {
      const file = files[0];
      setValue("upload", file);
      const urlImage = URL.createObjectURL(file);

      setPreview(urlImage);
    }
  };

  const createSection: SubmitHandler<Section & { upload: File }> = (
    section: Section & { upload: File },
  ) => {
    const data = new FormData();

    data.append("name", section.name!);
    data.append("details", section.details!);
    data.append("image", section.upload!);

    toast
      .promise(
        storeSection(data),
        {
          loading: "Creating section...",
          success: "Section has been saved 👌",
          error: "Could not save section 🤯",
        },
        {
          position: "bottom-center",
        },
      )
      .then(() => {
        reset();
        router.push("/sections");
      });
  };

  return (
    <form onSubmit={handleSubmit(createSection)}>
      <div className="space-y-4">
        <div>
          <label
            htmlFor="name"
            className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
          >
            Name
          </label>
          <input
            type="text"
            {...register("name")}
            id="name"
            className="block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
            placeholder="Type section name"
            required
          />
        </div>

        <div>
          <label
            htmlFor="image"
            className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
          >
            Image or icon
          </label>
          <label className="flex h-40 w-full items-center justify-center rounded-lg border border-dashed border-default-300 bg-default-50 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600">
            <input
              type="file"
              name="image"
              id="image"
              className="hidden"
              onChange={handleUploadedFile}
            />
            {preview ? (
              <Image
                src={preview}
                alt="preview"
                width={100}
                height={100}
                className="w-1/2"
              />
            ) : (
              <p>Click to select file</p>
            )}
          </label>
        </div>

        <div>
          <label
            htmlFor="description"
            className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
          >
            Description
          </label>
          <textarea
            id="description"
            rows={4}
            {...register("details")}
            className=".5 block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
            placeholder="Enter section description here"
          />
        </div>

        <div className="flex justify-between">
          <button
            type="reset"
            onClick={() => {
              reset();
              setPreview("");
            }}
            className="w-60 justify-center rounded-lg bg-default-100 px-5 py-3 text-center text-sm font-medium text-default-900 hover:bg-default-200 focus:outline-none focus:ring-4 focus:ring-default-300 dark:bg-default-700 dark:hover:bg-default-800 dark:focus:ring-default-800"
          >
            Reset
          </button>

          <button
            type="submit"
            disabled={isSubmitting}
            className="w-60 justify-center rounded-lg bg-primary px-5 py-3 text-center text-sm font-medium text-white hover:bg-default-800 focus:outline-none focus:ring-4 focus:ring-default-300 dark:bg-primary dark:hover:bg-primary dark:focus:ring-default-800"
          >
            Save section details
          </button>
        </div>
      </div>
    </form>
  );
}
