import { auth } from "@/auth";
import BranchSectionsCreateForm from "./form";
import Link from "next/link";
import { api } from "@/lib/api";
import { revalidatePath } from "next/cache";
import React from "react";

export default async function page() {
  const session = await auth();

  const storeSection = async (data: FormData) => {
    "use server";

    data.append("branchId", session?.branch?.id as string);

    await api.post("sections", data);

    revalidatePath("/sections");
  };

  return (
    <div className="p-4">
      <div className="flex justify-end">
        <Link
          href="/sections"
          className="rounded-lg bg-primary px-5 py-2 text-white"
        >
          <span>Back to sections</span>
        </Link>
      </div>
      <BranchSectionsCreateForm storeSection={storeSection} />
    </div>
  );
}
