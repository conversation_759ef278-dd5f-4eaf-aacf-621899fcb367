import { auth } from "@/auth";
import PaginatedTable from "@/components/table";
import { api } from "@/lib/api";
import Link from "next/link";
import React from "react";
import { cache } from "react";
import { revalidatePath } from "next/cache";
import { Icon } from "@/components/icon";
import toast from "react-hot-toast";

const fetchBranch = cache((id: string) => api.get<Branch>(`branches/${id}`));

export const generateMetadata = async (): Promise<Record<string, any>> => {
  const session = await auth();

  return {
    title: `${session?.branch?.name} Sections`,
    description: "Sections",
    keywords: ["sections"],
  };
};

export default async function page({
  searchParams,
}: {
  searchParams: Record<string, string>;
}) {
  const session = await auth();

  const branch = await fetchBranch(session?.branch?.id!);
  const sections = await api.get<PaginatedData<Section>>(
    `branches/${branch?.id}/sections`,
    {
      page: 1,
      ...searchParams,
    },
  );

  const deleteSection = async (formData: FormData) => {
    "use server";
    try {
      const res = await api.destroy(formData.get("id")?.toString()!, `sections`);
      console.log("DELETE SECTION RES:", res)
      toast.success("Section deleted successfully")
      revalidatePath(`/sections`);
    } catch (error) {
      toast.error("Failed to delete");
    }
  };

  return (
    <div className="py-4">
      {sections && (
        <PaginatedTable<Section>
          records={sections}
          columns={[
            {
              id: "name",
              title: "Name",
              render: (section) => (
                <Link href={`/sections/${section.id}`}>{section.name}</Link>
              ),
            },
            {
              id: "details",
              title: "Description",
            },
            {
              id: "actions",
              title: "Actions",
              render: (section) => (
                <div className="flex space-x-2">
                  <Link href={`/sections/${section.id}`}>
                    <Icon
                      name="icon-[mingcute--eye-line]"
                      classNames="text-primary"
                    />
                  </Link>

                  <Link href={`/sections/${section.id}/edit`}>
                    <Icon
                      name="icon-[mage--edit-pen-fill]"
                      classNames="text-primary"
                    />
                  </Link>
                  <form action={deleteSection}>
                    <button
                      name="id"
                      value={section.id}
                      type="submit"
                      className="text-red-500"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        strokeWidth={1.5}
                        stroke="currentColor"
                        className="size-6"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"
                        />
                      </svg>
                    </button>
                  </form>
                </div>
              ),
            },
          ]}
          path="/sections"
          tools={
            <div className="mb-4 flex items-center justify-between px-4">
              <p>Sections in {branch?.name}</p>
              <div className="flex">
                <Link
                  href="/sections/create"
                  className="rounded-lg bg-primary px-5 py-2 text-white"
                >
                  <span>Create Section</span>
                </Link>
              </div>
            </div>
          }
        />
      )}
    </div>
  );
}
