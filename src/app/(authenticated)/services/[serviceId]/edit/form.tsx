"use client";

import { imagePath } from "@/lib/api";
import Image from "next/image";
import { useState } from "react";
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import toast from "react-hot-toast";

export default function ServiceEditForm({
	defaultValues,
	storeService,
	onUpdate
}: {
	defaultValues: Service;
	storeService: (service: FormData) => Promise<Service | undefined>;
	onUpdate: () => void;
}) {
	const [preview, setPreview] = useState<string | undefined>(
		imagePath(defaultValues.image?.url)
	);

	const { handleSubmit, register, setValue, control } = useForm<
		Service & { upload: File }
	>({
		defaultValues,
	});

	const handleUpload = (e: any) => {
		// @ts-ignore
		const files = e.target?.files;

		if (files) {
			const file = files[0];
			setValue("upload", file);
			const urlImage = URL.createObjectURL(file);

			setPreview(urlImage);
		}
	};

	const onSubmit: SubmitHandler<Service & { upload: File }> = async (
		data: Service & { upload: File }
	) => {
		const formData = new FormData();
		formData.append("name", data.name);
		formData.append("slug", data.slug);
		formData.append("details", data.details);

		if (data.upload) {
			formData.append("image", data.upload);
		}

		toast.promise(storeService(formData), {
			loading: "Updating service...",
			success: "Service stored!",
			error: "Failed to store service",
		}).then(() => {
			onUpdate();
		});
	};

	return (
		<form onSubmit={handleSubmit(onSubmit)} className="space-y-4 p-5">
			<div className="grid grid-cols-12 gap-4">
				<div className="col-span-12 lg:col-span-5 space-y-2">
					<label className="block">Image</label>
					<div className="block">
						<Controller
							name="upload"
							control={control}
							render={({ field }) => (
								<label
									role="button"
									className="border-2 border-primary border-dashed h-[380px] flex justify-center items-center rounded-lg w-full"
								>
									{preview ? (
										<Image
											src={preview}
											alt={defaultValues.name}
											className="h-[380px] object-cover rounded-lg m-3"
										/>
									) : (
										<p>Click to upload</p>
									)}

									<input
										type="file"
										className="hidden"
										onChange={handleUpload}
									/>
								</label>
							)}
						/>
					</div>
				</div>

				<div className="col-span-12 lg:col-span-7 space-y-3">
					<div className="space-y-2">
						<label className="block">Name</label>
						<div className="block">
							<input
								type="text"
								className="border border-primary rounded-lg py-3 px-3 w-full"
								{...register("name")}
							/>
						</div>
					</div>

					<div className="space-y-2">
						<label className="block">Slug</label>
						<div className="block">
							<input
								type="text"
								className="border border-primary rounded-lg py-3 px-3 w-full"
								{...register("slug")}
							/>
						</div>
					</div>

					<div className="space-y-2">
						<label className="block">Details</label>
						<div className="block">
							<textarea
								className="border border-primary rounded-lg py-3 px-3 w-full"
								rows={4}
								{...register("details")}
							/>
						</div>
					</div>

					<div className="space-y-2">
						<div className="col-span-12 flex justify-end">
							<button
								type="submit"
								className="bg-primary text-white rounded-lg py-3 px-5 hover:bg-primary-900"
							>
								Update service
							</button>
						</div>
					</div>
				</div>
			</div>
		</form>
	);
}
