import { api } from "@/lib/api";
import ServiceEditForm from "./form";
import { cache } from "react";
import Link from "next/link";
import { revalidatePath } from "next/cache";

const fetchService = cache((id: string) => api.get<Service>(`services/${id}`));

export const generateMetadata = async ({
  params,
}: {
  params: {
    serviceId: string;
  };
}) => {
  const service = await fetchService(params.serviceId);
  return {
    title: `Edit ${service?.name}`,
    description: "Task",
    keywords: ["task"],
  };
};

export default async function page({
  params,
}: {
  params: {
    serviceId: string;
  };
}) {
  const service = await fetchService(params.serviceId);

  const storeService = async (service: FormData) => {
    "use server";

    const res = await api.put<Service>(`services/${params.serviceId}`, service);

    return res;
  };

  const reloadPage = async () => {
    "use server";

    revalidatePath(`/services/${params.serviceId}/edit`);
  };

  return (
    <div className="p-4">
      {service && (
        <ServiceEditForm
          storeService={storeService}
          defaultValues={service}
          onUpdate={reloadPage}
        />
      )}
    </div>
  );
}
