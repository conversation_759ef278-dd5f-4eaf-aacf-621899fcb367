import Link from "next/link";
import { ReactNode, cache } from "react";
import ServiceSections from "./sections";
import { auth } from "@/auth";
import { api } from "@/lib/api";

const fetchService = cache((id: string) => api.get<Service>(`services/${id}`));

export default async function layout({
  children,
  params,
}: {
  children: ReactNode;
  params: {
    serviceId: string;
  };
}) {
  const session = await auth();
  const service = await fetchService(params.serviceId);

  return (
    <div className="pt-4 w-full">
      {service && (
        <ServiceSections service={service} branch={session?.branch} />
      )}

      {children}
    </div>
  );
}
