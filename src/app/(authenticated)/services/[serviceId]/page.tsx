import { api } from "@/lib/api";
import { cache } from "react";
import { auth } from "@/auth";
import ServicesProductsTable from "@/components/tables/services-products-table";

const fetchService = cache((id: string) => api.get<Service>(`services/${id}`));

export const generateMetadata = async ({
  params,
}: {
  params: {
    serviceId: string;
  };
}): Promise<Record<string, any>> => {
  const service = await fetchService(params.serviceId);

  return {
    title: service?.name,
    description: "Service",
    keywords: ["service"],
  };
};

export default async function page({
  params,
  searchParams,
}: {
  params: {
    serviceId: string;
  };
  searchParams: Record<string, string>;
}) {
  const session = await auth();
  const service = await fetchService(params.serviceId);

  const products = session?.branch
    ? await api.get<PaginatedData<Product>>(
        `branches/${session.branch.id}/products`,
        { per: 30, service: params.serviceId, ...searchParams },
      )
    : await api.get<PaginatedData<Product>>(
        `services/${params.serviceId}/products`,
        { per: 30, ...searchParams },
      );

  return (
    <div className="flex bg-white p-5 h-full min-h-screen">
      {products && (
        <ServicesProductsTable
          data={products.data}
          meta={products.meta}
          title={`Products in ${service?.name}`}
          serviceId={params.serviceId}
        />
      )}
    </div>
  );
}
