import { api } from "@/lib/api";
import { revalidatePath } from "next/cache";
import ServiceProductTypesTable from "@/components/tables/services.product-types-table";

export const metadata = {
  title: "Product Types",
};

export default async function page({
  params: { serviceId },
  searchParams,
}: {
  params: { serviceId: string };
  searchParams: Record<string, string>;
}) {
  const types = await api.get<PaginatedData<ProductType>>(
    `services/${serviceId}/product-types`,
    searchParams,
  );

  const storeProductType = async (data: FormData) => {
    "use server";

    await api.post(`services/${serviceId}/product-types`, data);

    revalidatePath(`/services/${serviceId}/product-types`);
  };

  const updateProductType = async (producttype: FormData) => {
    "use server";

    await api.put(`product-types/${producttype.get("id")}`, producttype);

    revalidatePath(`/services/${serviceId}/product-types`);
  };

  const deleteRecord = async (id: string) => {
    "use server";

    await api.destroy(id, "product-types");

    revalidatePath(`/services/${serviceId}/product-types`);
  };

  return (
    <div className="flex bg-white p-5">
      {types && (
        <ServiceProductTypesTable
          data={types.data}
          meta={types.meta}
          serviceId={serviceId}
          storeProductType={storeProductType}
          updateProductType={updateProductType}
          deleteRecord={deleteRecord}
        />
      )}
    </div>
  );
}
