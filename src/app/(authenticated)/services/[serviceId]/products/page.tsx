import { api } from "@/lib/api";
import { cache } from "react";
import ServicesProductsTable from "@/components/tables/services-products-table";

const fetchService = cache((id: string) => api.get<Service>(`services/${id}`));

export const generateMetadata = async ({
  params,
}: {
  params: {
    serviceId: string;
  };
}): Promise<Record<string, any>> => {
  const service = await fetchService(params.serviceId);

  return {
    title: `Products In ${service?.name}`,
    description: "Service",
    keywords: ["service"],
  };
};

export default async function page({
  params,
  searchParams,
}: {
  params: {
    serviceId: string;
  };
  searchParams: Record<string, string>;
}) {
  const products = await api.get<PaginatedData<Product>>(
    `services/${params.serviceId}/products`,
    searchParams,
  );

  return (
    <div className="flex bg-white p-10">
      {products && (
        <ServicesProductsTable
          data={products.data}
          meta={products.meta}
          title={``}
          serviceId={params.serviceId}
        />
      )}
    </div>
  );
}
