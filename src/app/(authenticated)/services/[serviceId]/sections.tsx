"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";

export default function ServiceSections({
  service,
  branch,
}: {
  service: Service;
  branch?: Branch;
}) {
  const tabs: Record<string, string> = branch
    ? {
        // products: "Products",
        "product-types": `Product types in ${service?.name}`,
      }
    : {
        "vendor-types": "Vendor types",
        "product-types": "Product types",
        vendors: "Vendors",
        // products: "Products",
      };

  const path = usePathname();

  return (
    <div className="mb-4 flex w-full items-center justify-between px-4">
      {/* <p>{service?.details}</p> */}

      <ul className="flex flex-wrap border-b border-default-200 text-center text-sm font-medium text-default-500 dark:border-default-700 dark:text-default-400">
        <li className="me-2">
          <Link
            href={`/services/${service.id}`}
            aria-current="page"
            className={
              "inline-block p-4 " +
              (path === `/services/${service.id}`
                ? "border-b-2 border-primary text-base text-primary"
                : "")
            }
          >
            Overview
          </Link>
        </li>

        {Object.keys(tabs).map((tab) => (
          <li className="me-2" key={tab}>
            <Link
              href={`/services/${service.id}/${tab}`}
              aria-current="page"
              className={
                "inline-block p-4 " +
                (path === `/services/${service.id}/${tab}`
                  ? "border-b-2 border-primary text-base text-primary"
                  : "")
              }
            >
              {tabs[tab]}
            </Link>
          </li>
        ))}
      </ul>

      <div className="flex items-center gap-2">
        <Link
          className="rounded-lg border border-primary px-5 py-2 text-sm font-medium text-primary hover:bg-default-800 dark:border-primary dark:bg-default-800 dark:text-primary"
          href={`/services/${service?.id}/edit`}
        >
          Edit
        </Link>

        <Link
          className="rounded-lg bg-primary px-5 py-2 text-white"
          href={`/tasks/${service?.taskId}`}
        >
          Back to <span>{service?.task?.name || "task"}</span>
        </Link>
      </div>
    </div>
  );
}
