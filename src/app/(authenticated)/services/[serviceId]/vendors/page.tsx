import { api } from "@/lib/api";
import { cache } from "react";
import ServiceVendorsTable from "@/components/tables/services-vendors-table";

const fetchService = cache((id: string) => api.get<Service>(`services/${id}`));

export const generateMetadata = async ({
  params,
}: {
  params: {
    serviceId: string;
  };
}): Promise<Record<string, any>> => {
  const service = await fetchService(params.serviceId);

  return {
    title: `Vendors In ${service?.name}`,
    description: "Service",
    keywords: ["service"],
  };
};

export default async function page({
  params,
  searchParams,
}: {
  params: {
    serviceId: string;
  };
  searchParams: Record<string, string>;
}) {
  const vendors = await api.get<PaginatedData<Vendor>>(
    `services/${params.serviceId}/vendors`,
    searchParams,
  );

  return (
    <div className="flex bg-white p-10">
      {vendors && (
        <ServiceVendorsTable
          data={vendors.data}
          meta={vendors.meta}
          serviceId={params.serviceId}
        />
      )}
    </div>
  );
}
