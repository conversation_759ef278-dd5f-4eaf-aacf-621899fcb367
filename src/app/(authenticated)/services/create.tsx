"use client";

import { useState } from "react";
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import { toast } from "react-hot-toast";
import { useClickOutside } from "@/hooks/useClickOutside";
import AsyncSelect from "react-select/async";

export default function VendorServiceCreateForm({
  vendorId,
  assignService,
}: {
  vendorId?: string;
  assignService: (data: FormData) => Promise<void>;
}) {
  const [creating, setCreating] = useState(false);
  const [preview, setPreview] = useState<string>();

  const ref = useClickOutside(() => setCreating(false));

  const {
    setValue,
    watch,
    handleSubmit,
    register,
    reset,
    control,
    formState: { errors, isSubmitting },
  } = useForm<{ services: string[] }>();

  const loadServices = async (inputValue: string) => {
    return fetch(`/api/services?s=${inputValue}`).then((res) => res.json());
  };

  const createService: SubmitHandler<{ services: string[] }> = ({
    services,
  }) => {
    const data = new FormData();

    if (vendorId) {
      data.append("vendorId", vendorId);
    }

    services.forEach((service) => {
      data.append("services[]", service);
    });

    toast.promise(
      assignService(data),
      {
        loading: "Assigning service...",
        success: "Service has been saved 👌",
        error: "Could not save services 🤯",
      },
      {
        position: "bottom-center",
      },
    );

    reset();

    setCreating(false);
  };

  return (
    <>
      <button
        onClick={() => setCreating(!creating)}
        id="createServiceButton"
        className="flex items-center justify-center rounded-lg bg-primary px-5 py-2 text-white hover:bg-default-800 dark:bg-primary"
        type="button"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="currentColor"
          className="bi bi-plus-lg h-5 w-5"
          viewBox="0 0 16 16"
        >
          <path
            fillRule="evenodd"
            d="M8 2a.5.5 0 0 1 .5.5v5h5a.5.5 0 0 1 0 1h-5v5a.5.5 0 0 1-1 0v-5h-5a.5.5 0 0 1 0-1h5v-5A.5.5 0 0 1 8 2Z"
          />
        </svg>

        <span>Assign services</span>
      </button>

      {creating && (
        <div
          ref={ref}
          id="drawer-create-service-default"
          className="fixed right-0 top-0 z-40 h-screen w-full max-w-xs overflow-y-auto bg-white p-4 transition-transform dark:bg-default-800"
          tabIndex={-1}
          aria-labelledby="drawer-label"
          aria-hidden="true"
        >
          <h5
            id="drawer-label"
            className="mb-6 inline-flex items-center text-sm font-semibold uppercase text-default-500 dark:text-default-400"
          >
            New Service
          </h5>
          <button
            onClick={() => setCreating(!creating)}
            type="button"
            data-drawer-dismiss="drawer-create-service-default"
            aria-controls="drawer-create-service-default"
            className="absolute right-2.5 top-2.5 inline-flex items-center rounded-lg bg-transparent p-1.5 text-sm text-default-400 hover:bg-default-200 hover:text-default-900 dark:hover:bg-default-600 dark:hover:text-white"
          >
            <svg
              aria-hidden="true"
              className="h-5 w-5"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fillRule="evenodd"
                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                clipRule="evenodd"
              ></path>
            </svg>
            <span className="sr-only">Close menu</span>
          </button>
          <form onSubmit={handleSubmit(createService)}>
            <div className="space-y-4">
              <div>
                <label
                  htmlFor="name"
                  className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
                >
                  Services
                </label>
                <Controller
                  name="services"
                  control={control}
                  render={({ field }) => (
                    <AsyncSelect
                      isMulti
                      cacheOptions
                      defaultOptions
                      loadOptions={loadServices}
                      getOptionLabel={(service: Service) => service.name}
                      getOptionValue={(service: Service) => service.id}
                      placeholder="Search in services..."
                      onChange={(v) =>
                        setValue(
                          "services",
                          v.map((service) => service.id),
                        )
                      }
                      components={{
                        IndicatorSeparator: () => null,
                      }}
                      classNames={{
                        control: () => "w-full !rounded-lg !py-[0.8px]",
                      }}
                    />
                  )}
                />
              </div>

              <div className="bottom-0 left-0 flex w-full justify-center space-x-4 pb-4 md:absolute md:px-4">
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full justify-center rounded-lg bg-primary px-5 py-3 text-center text-sm font-medium text-white hover:bg-default-800 focus:outline-none focus:ring-4 focus:ring-default-300 dark:bg-primary dark:hover:bg-primary dark:focus:ring-default-800"
                >
                  Save service details
                </button>
              </div>
            </div>
          </form>
        </div>
      )}
    </>
  );
}
