"use client";

import { useRouter, useSearchParams } from "next/navigation";
import { useState } from "react";
import { useForm } from "react-hook-form";
import toast from "react-hot-toast";

export default function ServiceCreateForm({
  storeService,
}: {
  storeService: (service: FormData) => Promise<Service | undefined>;
}) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [serviceImage, setServiceImage] = useState<File | null>(null);

  const {
    watch,
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<Service>({
    defaultValues: {
      name: "",
      details: "",
      taskId: searchParams.get("task") ?? "",
    },
  });

  const service = watch();

  const onSubmit = async (data: Service) => {
    const payload = new FormData();
    payload.append("name", data.name);
    payload.append("details", data.details);
    payload.append("taskId", data.taskId);

    if (serviceImage) {
      payload.append("image", serviceImage);
    }

    const s = await toast.promise(storeService(payload), {
      loading: "Saving...",
      success: "Service saved",
      error: "Error saving service",
    });

    if (s) {
      router.push(`/services/${s.id}`);
    }
  };
  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-3">
      <div className="space-y-1">
        <label htmlFor="name">Name</label>
        <input
          type="text"
          {...register("name", { required: true })}
          className="w-full rounded-md border border-default-300 p-2 transition-all duration-200 ease-in-out focus:border-primary focus:outline-none"
        />
        {errors.name && (
          <span className="text-red-500">This field is required</span>
        )}
      </div>

      <div className="flex space-x-3 ">
        <div className="flex-1 space-y-1">
          <label htmlFor="details">Details</label>
          <textarea
            {...register("details", { required: true })}
            className="h-20 w-full rounded-md border border-default-300 p-2 transition-all duration-200 ease-in-out focus:border-primary focus:outline-none"
          />
          {errors.details && (
            <span className="text-red-500">This field is required</span>
          )}
        </div>

        <div className="w-1/3 space-y-1">
          <label>Image</label>
          <label
            className="flex h-20 w-full items-center justify-center rounded-lg border border-dashed border-primary"
            role="button"
          >
            <input
              type="file"
              className="hidden"
              onChange={(e) => {
                const file = e.target.files?.[0];
                if (file) {
                  setServiceImage(file);
                }
              }}
            />
            <p className="text-center">Upload image</p>
          </label>
        </div>
      </div>

      <button
        type="submit"
        className="hover:bg-default-dark rounded-md bg-primary px-4 py-2 text-white transition-all duration-200 ease-in-out"
      >
        Save
      </button>
    </form>
  );
}
