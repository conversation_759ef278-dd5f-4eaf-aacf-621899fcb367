import { api } from "@/lib/api";
import ServiceCreateForm from "./form";

export const dynamic = "force-dynamic";
export const fetchCache = "force-no-store";

export const metadata = {
  title: "Create new service",
  description: "Task",
  keywords: ["task"],
};

export default async function page() {
  const storeService = async (service: FormData) => {
    "use server";

    const res = await api.post<Service>("services", service);

    return res;
  };

  return (
    <div className="p-4">
      <ServiceCreateForm storeService={storeService} />
    </div>
  );
}
