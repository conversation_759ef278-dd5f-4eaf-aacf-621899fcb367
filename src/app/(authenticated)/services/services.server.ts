"use server";

import { api } from "@/lib/api";
import { revalidatePath } from "next/cache";

export const assignService = async (vendorId: string, data: FormData) => {
  await api.post(`vendors/${vendorId}/services`, data);
  revalidatePath(`/vendors/${vendorId}/services`);
};

export const toggleService = async (vendorId: string | null, service: Service) => {
  const path = vendorId
    ? `vendors/${vendorId}/services/${service.id}`
    : `services/${service.id}`;
    
  return await api.put<Service>(path, { active: !service.active });
};

export const deleteService = async (serviceId: string) => {
  await api.destroy(serviceId, `services`);
  revalidatePath(`/services`);
};
