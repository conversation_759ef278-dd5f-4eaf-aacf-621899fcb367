"use client";

import Link from "next/link";
import logoImg from "@/images/logo.png";
import { imagePath } from "@/lib/api";
import Image from "next/image";
import { usePathname } from "next/navigation";
import { useEffect, useState } from "react";
import React from "react";

interface User {
  id: string;
  title: string;
  firstName: string;
  lastName: string;
  gender: string | null;
  dob: string | null;
  email: string;
  phone: string;
  idpass: string;
  rememberMeToken: string | null;
  details: string | null;
  location: Record<string, any> | null;
  geom: string | null;
  avatar: AttachmentContract | null;
  createdAt: string;
  updatedAt: string;
  name: string;
  status: string;
  avatarUrl: string;
  initials: string;
  devices: Device[];
  roles: Role[];
  permissions: Permission[];
  notifications: DatabaseNotification[];
  identifier: string;
  online: boolean;
  vendorId: string;
}

export default function PortalSidebar({
  user,
  tasks,
  branch,
  vendor,
  logoutUser,
}: {
  user: User | undefined;
  tasks?: PaginatedData<Task>;
  branch?: Branch;
  vendor?: Vendor;
  logoutUser: () => Promise<void>;
}) {
  const pathname = usePathname();
  const [activeMenu, setActiveMenu] = useState<string>("");

  useEffect(() => {
    // Save branch vendorId to local storage if it exists
    if (branch && branch.vendorId) {
      localStorage.setItem("branchVendorId", branch.vendorId);
    }
  }, [branch]);

  // Log the branch vendorId

  // Rest of your component code...
  const DropCursor = ({ menu = "", path = "" }) => (
    <>
      {activeMenu === menu ? (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          strokeWidth={1.5}
          stroke="currentColor"
          className="ms-auto h-4 w-4"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="m4.5 15.75 7.5-7.5 7.5 7.5"
          />
        </svg>
      ) : (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          strokeWidth={1.5}
          stroke="currentColor"
          className="ms-auto h-4 w-4"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="m8.25 4.5 7.5 7.5-7.5 7.5"
          />
        </svg>
      )}
    </>
  );


  const AdminMenu = () => (
    <div
      data-simplebar="init"
      className=" h-[calc(100%-160px)] overflow-y-scroll pb-8 scrollbar-thin scrollbar-webkit"
    >
      <ul className="hs-accordion-group mb-5 flex w-full flex-col gap-1.5 overflow-y-auto p-4">
        <li className="">
          <Link
            className={
              "active flex items-center gap-x-3.5 rounded-md px-4 py-3 text-sm text-default-700 hover:bg-default-100 dark:text-white " +
              (pathname === "/" ? "bg-default-100 text-primary" : "")
            }
            data-menu-key="dashboard-page"
            href="/"
          >
            <svg
              stroke="currentColor"
              fill="none"
              strokeWidth="2"
              viewBox="0 0 24 24"
              strokeLinecap="round"
              strokeLinejoin="round"
              height="20"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <rect width="7" height="7" x="3" y="3" rx="1"></rect>
              <rect width="7" height="7" x="14" y="3" rx="1"></rect>
              <rect width="7" height="7" x="14" y="14" rx="1"></rect>
              <rect width="7" height="7" x="3" y="14" rx="1"></rect>
            </svg>
            Dashboard
          </Link>
        </li>

        <li className="">
          <Link
            className={
              "active flex items-center gap-x-3.5 rounded-md px-4 py-3 text-sm text-default-700 hover:bg-default-100 dark:text-white " +
              (pathname.startsWith("/tasks") || activeMenu === "tasks"
                ? "bg-default-100 text-default-700"
                : "")
            }
            data-menu-key="dashboard-page"
            href="/tasks"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="size-6"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M6 6.878V6a2.25 2.25 0 0 1 2.25-2.25h7.5A2.25 2.25 0 0 1 18 6v.878m-12 0c.235-.083.487-.128.75-.128h10.5c.263 0 .515.045.75.128m-12 0A2.25 2.25 0 0 0 4.5 9v.878m13.5-3A2.25 2.25 0 0 1 19.5 9v.878m0 0a2.246 2.246 0 0 0-.75-.128H5.25c-.263 0-.515.045-.75.128m15 0A2.25 2.25 0 0 1 21 12v6a2.25 2.25 0 0 1-2.25 2.25H5.25A2.25 2.25 0 0 1 3 18v-6c0-.98.626-1.813 1.5-2.122"
              />
            </svg>
            Task management
          </Link>
        </li>

        <li className="">
          <Link
            className={
              "active flex items-center gap-x-3.5 rounded-md px-4 py-3 text-sm text-default-700 hover:bg-default-100 dark:text-white " +
              (pathname.startsWith("/services") || activeMenu === "services"
                ? "bg-default-100 text-primary"
                : "")
            }
            data-menu-key="dashboard-page"
            href="/services"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="size-6"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M17.593 3.322c1.1.128 1.907 1.077 1.907 2.185V21L12 17.25 4.5 21V5.507c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0 1 11.186 0Z"
              />
            </svg>
            Service management
          </Link>
        </li>

        <li className="hs-accordion group">
          <button
            className={
              "hs-accordion-toggle flex w-full items-center gap-x-3.5 rounded-md px-4 py-3 text-sm text-default-700 hover:bg-default-100 dark:text-white " +
              (pathname.startsWith("/orders") ||
              pathname.startsWith("/temp-orders") ||
              activeMenu === "orders"
                ? "bg-default-100 text-primary"
                : "")
            }
            aria-expanded="false"
            data-menu-key="orders"
            onClick={() =>
              setActiveMenu(activeMenu === "orders" ? "" : "orders")
            }
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="size-6"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M2.25 3h1.386c.51 0 .955.343 1.087.835l.383 1.437M7.5 14.25a3 3 0 0 0-3 3h15.75m-12.75-3h11.218c1.121-2.3 2.1-4.684 2.924-7.138a60.114 60.114 0 0 0-16.536-1.84M7.5 14.25 5.106 5.272M6 20.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0Zm12.75 0a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0Z"
              />
            </svg>
            Orders
            <DropCursor menu="orders" />
          </button>

          <div
            className={
              "hs-accordion-content w-full overflow-hidden transition-[height] " +
              (pathname.startsWith("/orders") ||
              activeMenu === "orders" ||
              pathname.startsWith("/temp-orders")
                ? "block"
                : "hidden")
            }
          >
            <ul className="mt-2 space-y-2">
              <li>
                <Link
                  className="flex items-center gap-x-3.5 rounded-md px-2.5 py-2 text-sm font-medium text-default-700 hover:bg-default-100 group-[.active]:bg-default-100 group-[.active]:text-primary dark:text-white"
                  data-menu-key="orders-list"
                  href="/orders"
                >
                  <svg
                    stroke="currentColor"
                    fill="none"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    height="24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <circle cx="12.1" cy="12.1" r="1"></circle>
                  </svg>
                  Orders
                </Link>
              </li>

              <li>
                <Link
                  className="flex items-center gap-x-3.5 rounded-md px-2.5 py-2 text-sm font-medium text-default-700 hover:bg-default-100 group-[.active]:bg-default-100 group-[.active]:text-primary dark:text-white"
                  data-menu-key="temp-orders-list"
                  href="/temp-orders"
                >
                  <svg
                    stroke="currentColor"
                    fill="none"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    height="24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <circle cx="12.1" cy="12.1" r="1"></circle>
                  </svg>
                  Pending requests
                </Link>
              </li>

              <li>
                <Link
                  className="flex items-center gap-x-3.5 rounded-md px-2.5 py-2 text-sm font-medium text-default-700 hover:bg-default-100 group-[.active]:bg-default-100 group-[.active]:text-primary dark:text-white"
                  data-menu-key="orders-details"
                  href="/orders/create"
                >
                  <svg
                    stroke="currentColor"
                    fill="none"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    height="24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <circle cx="12.1" cy="12.1" r="1"></circle>
                  </svg>
                  New order
                </Link>
              </li>
            </ul>
          </div>
        </li>

        <li className="hs-accordion group">
          <button
            className={
              "hs-accordion-toggle flex w-full items-center gap-x-3.5 rounded-md px-4 py-3 text-sm hover:bg-default-100 " +
              (pathname.startsWith("/customers") || activeMenu === "customers"
                ? "bg-default-100 text-primary"
                : "text-default-700")
            }
            aria-expanded="false"
            data-menu-key="customers"
            onClick={() =>
              setActiveMenu(activeMenu === "customers" ? "" : "customers")
            }
          >
            <svg
              stroke="currentColor"
              fill="none"
              strokeWidth="2"
              viewBox="0 0 24 24"
              strokeLinecap="round"
              strokeLinejoin="round"
              height="20"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
              <circle cx="9" cy="7" r="4"></circle>
              <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
              <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
            </svg>
            Customers
            <DropCursor menu="customers" />
          </button>
          <div
            className={
              "hs-accordion-content w-full overflow-hidden transition-[height] " +
              (pathname.startsWith("/customers") || activeMenu === "customers"
                ? "block"
                : "hidden")
            }
          >
            <ul className="mt-2 space-y-2">
              <li>
                <Link
                  className="flex items-center gap-x-3.5 rounded-md px-2.5 py-2 text-sm font-medium text-default-700 hover:bg-default-100 group-[.active]:bg-default-100 group-[.active]:text-primary dark:text-white"
                  data-menu-key="customers-list"
                  href="/customers"
                >
                  <svg
                    stroke="currentColor"
                    fill="none"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    height="24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <circle cx="12.1" cy="12.1" r="1"></circle>
                  </svg>
                  Customers
                </Link>
              </li>
              <li>
                <Link
                  className="flex items-center gap-x-3.5 rounded-md px-2.5 py-2 text-sm font-medium text-default-700 hover:bg-default-100 group-[.active]:bg-default-100 group-[.active]:text-primary dark:text-white"
                  data-menu-key="customers-add"
                  href="/customers/create"
                >
                  <svg
                    stroke="currentColor"
                    fill="none"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    height="24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <circle cx="12.1" cy="12.1" r="1"></circle>
                  </svg>
                  Add Customer
                </Link>
              </li>
            </ul>
          </div>
        </li>

        <li className="hs-accordion group">
          <button
            className={
              "hs-accordion-toggle flex w-full items-center gap-x-3.5 rounded-md px-4 py-3 text-sm hover:bg-default-100 " +
              (pathname.startsWith("/products") || activeMenu === "products"
                ? "bg-default-100 text-primary"
                : "text-default-700")
            }
            aria-expanded="false"
            data-menu-key="products"
            onClick={() =>
              setActiveMenu(activeMenu === "products" ? "" : "products")
            }
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="size-6"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M15.75 10.5V6a3.75 3.75 0 1 0-7.5 0v4.5m11.356-1.993 1.263 12c.07.665-.45 1.243-1.119 1.243H4.25a1.125 1.125 0 0 1-1.12-1.243l1.264-12A1.125 1.125 0 0 1 5.513 7.5h12.974c.576 0 1.059.435 1.119 1.007ZM8.625 10.5a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm7.5 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"
              />
            </svg>
            Products
            <DropCursor menu="products" />
          </button>
          <div
            className={
              "hs-accordion-content w-full overflow-hidden transition-[height] " +
              (pathname.startsWith("/products") || activeMenu === "products"
                ? "block"
                : "hidden")
            }
          >
            <ul className="mt-2 space-y-2">
              <li>
                <Link
                  className="flex items-center gap-x-3.5 rounded-md px-2.5 py-2 text-sm font-medium text-default-700 hover:bg-default-100 group-[.active]:bg-default-100 group-[.active]:text-primary dark:text-white"
                  data-menu-key="products-list"
                  href="/products"
                >
                  <svg
                    stroke="currentColor"
                    fill="none"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    height="24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <circle cx="12.1" cy="12.1" r="1"></circle>
                  </svg>
                  Products
                </Link>
              </li>

              <li>
                <Link
                  className="flex items-center gap-x-3.5 rounded-md px-2.5 py-2 text-sm font-medium text-default-700 hover:bg-default-100 group-[.active]:bg-default-100 group-[.active]:text-primary dark:text-white"
                  data-menu-key="products-add"
                  href="/products/create"
                >
                  <svg
                    stroke="currentColor"
                    fill="none"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    height="24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <circle cx="12.1" cy="12.1" r="1"></circle>
                  </svg>
                  Add Product
                </Link>
              </li>

              <li>
                <Link
                  className="flex items-center gap-x-3.5 rounded-md px-2.5 py-2 text-sm font-medium text-default-700 hover:bg-default-100 group-[.active]:bg-default-100 group-[.active]:text-primary dark:text-white"
                  data-menu-key="products-tag"
                  href="/tags"
                >
                  <svg
                    stroke="currentColor"
                    fill="none"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    height="24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <circle cx="12.1" cy="12.1" r="1"></circle>
                  </svg>
                  Tags
                </Link>
              </li>

              <li>
                <Link
                  className="flex items-center gap-x-3.5 rounded-md px-2.5 py-2 text-sm font-medium text-default-700 hover:bg-default-100 group-[.active]:bg-default-100 group-[.active]:text-primary dark:text-white"
                  data-menu-key="products-add"
                  href="/products/forms/templates"
                >
                  <svg
                    stroke="currentColor"
                    fill="none"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    height="24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <circle cx="12.1" cy="12.1" r="1"></circle>
                  </svg>
                  Form templates
                </Link>
              </li>

              <li>
                <Link
                  className="flex items-center gap-x-3.5 rounded-md px-2.5 py-2 text-sm font-medium text-default-700 hover:bg-default-100 group-[.active]:bg-default-100 group-[.active]:text-primary dark:text-white"
                  data-menu-key="products-list"
                  href="/product-templates"
                >
                  <svg
                    stroke="currentColor"
                    fill="none"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    height="24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <circle cx="12.1" cy="12.1" r="1"></circle>
                  </svg>
                  Product templates
                </Link>
              </li>
            </ul>
          </div>
        </li>
        <li className="hs-accordion group">
          <button
            className={
              "hs-accordion-toggle flex w-full items-center gap-x-3.5 rounded-md px-4 py-3 text-sm hover:bg-default-100 " +
              (pathname.startsWith("/vendors") || activeMenu === "vendors"
                ? "bg-default-100 text-primary"
                : "text-default-700")
            }
            aria-expanded="false"
            data-menu-key="vendors"
            onClick={() =>
              setActiveMenu(activeMenu === "vendors" ? "" : "vendors")
            }
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="size-6"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M20.25 14.15v4.25c0 1.094-.787 2.036-1.872 2.18-2.087.277-4.216.42-6.378.42s-4.291-.143-6.378-.42c-1.085-.144-1.872-1.086-1.872-2.18v-4.25m16.5 0a2.18 2.18 0 0 0 .75-1.661V8.706c0-1.081-.768-2.015-1.837-2.175a48.114 48.114 0 0 0-3.413-.387m4.5 8.006c-.194.165-.42.295-.673.38A23.978 23.978 0 0 1 12 15.75c-2.648 0-5.195-.429-7.577-1.22a2.016 2.016 0 0 1-.673-.38m0 0A2.18 2.18 0 0 1 3 12.489V8.706c0-1.081.768-2.015 1.837-2.175a48.111 48.111 0 0 1 3.413-.387m7.5 0V5.25A2.25 2.25 0 0 0 13.5 3h-3a2.25 2.25 0 0 0-2.25 2.25v.894m7.5 0a48.667 48.667 0 0 0-7.5 0M12 12.75h.008v.008H12v-.008Z"
              />
            </svg>
            Vendors
            <DropCursor menu="vendors" />
          </button>
          <div
            className={
              "hs-accordion-content w-full overflow-hidden transition-[height] " +
              (pathname.startsWith("/vendors") || activeMenu === "vendors"
                ? "block"
                : "hidden")
            }
          >
            <ul className="mt-2 space-y-2">
              <li>
                <Link
                  className="flex items-center gap-x-3.5 rounded-md px-2.5 py-2 text-sm font-medium text-default-700 hover:bg-default-100 group-[.active]:bg-default-100 group-[.active]:text-primary dark:text-white"
                  data-menu-key="vendors-list"
                  href="/vendors"
                >
                  <svg
                    stroke="currentColor"
                    fill="none"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    height="24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <circle cx="12.1" cy="12.1" r="1"></circle>
                  </svg>
                  Vendors
                </Link>
              </li>
              <li>
                <Link
                  className="flex items-center gap-x-3.5 rounded-md px-2.5 py-2 text-sm font-medium text-default-700 hover:bg-default-100 group-[.active]:bg-default-100 group-[.active]:text-primary dark:text-white"
                  data-menu-key="vendors-add"
                  href="/vendors/create"
                >
                  <svg
                    stroke="currentColor"
                    fill="none"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    height="24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <circle cx="12.1" cy="12.1" r="1"></circle>
                  </svg>
                  Add Vendor
                </Link>
              </li>

              <li>
                <Link
                  className="flex items-center gap-x-3.5 rounded-md px-2.5 py-2 text-sm font-medium text-default-700 hover:bg-default-100 group-[.active]:bg-default-100 group-[.active]:text-primary dark:text-white"
                  data-menu-key="specialization-add"
                  href="/specialization"
                >
                  <svg
                    stroke="currentColor"
                    fill="none"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    height="24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <circle cx="12.1" cy="12.1" r="1"></circle>
                  </svg>
                  Specialization
                </Link>
              </li>
            </ul>
          </div>
        </li>

        {tasks?.data?.map((task) => (
          <li key={task.id} className={`hs-accordion group group/${task.id}`}>
            <button
              className={
                "hs-accordion-toggle flex w-full items-center gap-x-3.5 rounded-md px-4 py-3 text-sm hover:bg-default-100 " +
                (pathname.startsWith(`/tasks/${task.id}`) ||
                activeMenu === task.id
                  ? "bg-default-100 text-primary"
                  : "text-default-700")
              }
              data-menu-key="extra-pages-starter"
              onClick={() =>
                setActiveMenu(activeMenu === task.id ? "" : task.id)
              }
            >
              {task.name}

              <DropCursor menu={task.id} />
            </button>

            <div
              className={
                `hs-accordion-content ml-4 w-full overflow-y-scroll transition-[height] ` +
                (pathname.startsWith(`/tasks/${task.id}`) ||
                activeMenu === task.id
                  ? "block"
                  : "hidden")
              }
            >
              <ul className="mt-2 space-y-2 overflow-y-scroll">
                <li className="">
                  <Link
                    className="flex items-center gap-x-3.5 rounded-md px-2.5 py-2 text-sm font-medium text-default-700 hover:bg-default-100 group-[.active]:bg-default-100 group-[.active]:text-primary dark:text-white"
                    data-menu-key="extra-pages-starter"
                    href={`/tasks/${task.id}`}
                  >
                    Overview
                  </Link>
                </li>

                {task.services?.map((service) => (
                  <li key={service.id} className="group">
                    <Link
                      className="flex items-center gap-x-3.5 rounded-md px-2.5 py-2 text-sm font-medium text-default-700 hover:bg-default-100 group-[.active]:bg-default-100 group-[.active]:text-primary dark:text-white"
                      data-menu-key="extra-pages-starter"
                      href={`/services/${service.id}`}
                    >
                      {service.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          </li>
        ))}
      </ul>
    </div>
  );


  const VendorMenu = () => (
    <div
      data-simplebar="init"
      className="h-[calc(100%-160px)] overflow-y-scroll pb-8 scrollbar-thin scrollbar-webkit"
    >
      <ul className="hs-accordion-group mb-5 flex w-full flex-col gap-1.5 overflow-y-auto p-4">
        <li className="">
          <Link
            className={
              "active flex items-center gap-x-3.5 rounded-md px-4 py-3 text-sm text-default-700 hover:bg-default-100 dark:text-white " +
              (pathname === "/" ? "bg-default-100 text-primary" : "")
            }
            data-menu-key="dashboard-page"
            href="/"
          >
            <svg
              stroke="currentColor"
              fill="none"
              strokeWidth="2"
              viewBox="0 0 24 24"
              strokeLinecap="round"
              strokeLinejoin="round"
              height="20"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <rect width="7" height="7" x="3" y="3" rx="1"></rect>
              <rect width="7" height="7" x="14" y="3" rx="1"></rect>
              <rect width="7" height="7" x="14" y="14" rx="1"></rect>
              <rect width="7" height="7" x="3" y="14" rx="1"></rect>
            </svg>
            Dashboard
          </Link>
        </li>

        <li className="">
          <Link
            className={
              "active flex items-center gap-x-3.5 rounded-md px-4 py-3 text-sm text-default-700 hover:bg-default-100 dark:text-white " +
              (pathname.startsWith("/tasks")
                ? "bg-default-100 text-primary"
                : "")
            }
            data-menu-key="dashboard-page"
            href="/tasks"
          >
            <svg
              stroke="currentColor"
              fill="none"
              strokeWidth="2"
              viewBox="0 0 24 24"
              strokeLinecap="round"
              strokeLinejoin="round"
              height="20"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"></path>
              <polyline points="14 2 14 8 20 8"></polyline>
            </svg>
            Task management
          </Link>
        </li>

        <li className="">
          <Link
            className={
              "active flex items-center gap-x-3.5 rounded-md px-4 py-3 text-sm text-default-700 hover:bg-default-100 dark:text-white " +
              (pathname.startsWith("/services") || activeMenu === "services"
                ? "bg-default-100 text-primary"
                : "")
            }
            data-menu-key="dashboard-page"
            href="/services"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="size-6"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M17.593 3.322c1.1.128 1.907 1.077 1.907 2.185V21L12 17.25 4.5 21V5.507c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0 1 11.186 0Z"
              />
            </svg>
            Service management
          </Link>
        </li>

        {branch !== undefined && (
            <li className="">
              <Link
                className={
                  "active flex items-center gap-x-3.5 rounded-md px-4 py-3 text-sm text-default-700 hover:bg-default-100 dark:text-white " +
                  (pathname.startsWith("/section")
                    ? "bg-default-100 text-primary"
                    : "")
                }
                data-menu-key="dashboard-page"
                href="/sections"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={1.5}
                  stroke="currentColor"
                  className="size-6"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M2.25 7.125C2.25 6.504 2.754 6 3.375 6h6c.621 0 1.125.504 1.125 1.125v3.75c0 .621-.504 1.125-1.125 1.125h-6a1.125 1.125 0 0 1-1.125-1.125v-3.75ZM14.25 8.625c0-.621.504-1.125 1.125-1.125h5.25c.621 0 1.125.504 1.125 1.125v8.25c0 .621-.504 1.125-1.125 1.125h-5.25a1.125 1.125 0 0 1-1.125-1.125v-8.25ZM3.75 16.125c0-.621.504-1.125 1.125-1.125h5.25c.621 0 1.125.504 1.125 1.125v2.25c0 .621-.504 1.125-1.125 1.125h-5.25a1.125 1.125 0 0 1-1.125-1.125v-2.25Z"
                  />
                </svg>
                Sections & lots
              </Link>
            </li>
          )}
        
        {branch !== undefined && (
          <li className="menu-item">
            <Link
              className={
                "flex items-center gap-x-3.5 rounded-md px-4 py-3 text-sm text-default-700 hover:bg-default-100 dark:text-white " +
                (pathname.startsWith("/campaigns")
                  ? "bg-default-100 text-primary"
                  : "")
              }
              data-menu-key="dashboard-page"
              href="/campaigns"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="size-6"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M10.34 15.84c-.688-.06-1.386-.09-2.09-.09H7.5a4.5 4.5 0 1 1 0-9h.75c.704 0 1.402-.03 2.09-.09m0 9.18c.253.962.584 1.892.985 2.783.247.55.06 1.21-.463 1.511l-.657.38c-.551.318-1.26.117-1.527-.461a20.845 20.845 0 0 1-1.44-4.282m3.102.069a18.03 18.03 0 0 1-.59-4.59c0-1.586.205-3.124.59-4.59m0 9.18a23.848 23.848 0 0 1 8.835 2.535M10.34 6.66a23.847 23.847 0 0 0 8.835-2.535m0 0A23.74 23.74 0 0 0 18.795 3m.38 1.125a23.91 23.91 0 0 1 1.014 5.395m-1.014 8.855c-.118.38-.245.754-.38 1.125m.38-1.125a23.91 23.91 0 0 0 1.014-5.395m0-3.46c.495.413.811 1.035.811 1.73 0 .695-.316 1.317-.811 1.73m0-3.46a24.347 24.347 0 0 1 0 3.46"
                />
              </svg>
              Campaigns
            </Link>
          </li>
        )}

        <li className="menu-item">
          <Link
            className={
              "flex items-center gap-x-3.5 rounded-md px-4 py-3 text-sm text-default-700 hover:bg-default-100 dark:text-white " +
              (pathname.startsWith("/ratings")
                ? "bg-default-100 text-primary"
                : "")
            }
            href="/ratings"
            data-menu-key="dashboard-page"
            onClick={() => {
              setActiveMenu(activeMenu === "ratings" ? "" : "ratings");
            }}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="size-6"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M11.48 3.499a.562.562 0 0 1 1.04 0l2.125 5.111a.563.563 0 0 0 .475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 0 0-.182.557l1.285 5.385a.562.562 0 0 1-.84.61l-4.725-2.885a.562.562 0 0 0-.586 0L6.982 20.54a.562.562 0 0 1-.84-.61l1.285-5.386a.562.562 0 0 0-.182-.557l-4.204-3.602a.562.562 0 0 1 .321-.988l5.518-.442a.563.563 0 0 0 .475-.345L11.48 3.5Z"
              />
            </svg>
            Ratings
          </Link>
        </li>

        <li className="hs-accordion group">
          <button
            className={
              "hs-accordion-toggle flex w-full items-center gap-x-3.5 rounded-md px-4 py-3 text-sm text-default-700 hover:bg-default-100 dark:text-white " +
              (pathname.startsWith("/hours") ||
              activeMenu === "hours" ||
              pathname.startsWith("/roster")
                ? "bg-default-100 text-primary"
                : "")
            }
            aria-expanded="false"
            data-menu-key="hours"
            onClick={() => {
              setActiveMenu(activeMenu === "hours" ? "" : "hours");
            }}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="size-6"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
              />
            </svg>
            Hours
            <DropCursor menu="hours" />
          </button>
          <div
            className={
              "hs-accordion-content w-full overflow-hidden transition-[height] " +
              (pathname.startsWith("/roster") ||
              pathname.startsWith("/hours") ||
              activeMenu === "hours"
                ? "block"
                : "hidden")
            }
          >
            <ul className="mt-2 space-y-2">
              <li>
                <Link
                  className={
                    "active flex items-center gap-x-3.5 rounded-md px-4 py-3 text-sm text-default-700 hover:bg-default-100 dark:text-white " +
                    (pathname.startsWith("/roster")
                      ? "bg-default-100 text-primary"
                      : "")
                  }
                  data-menu-key="dashboard-page"
                  href="/roster"
                >
                  Duty roster
                </Link>
              </li>
              <li>
                <Link
                  className={
                    "active flex items-center gap-x-3.5 rounded-md px-4 py-3 text-sm text-default-700 hover:bg-default-100 dark:text-white " +
                    (pathname.startsWith("/hours")
                      ? "bg-default-100 text-primary"
                      : "")
                  }
                  data-menu-key="dashboard-page"
                  href="/hours"
                >
                  Work hours
                </Link>
              </li>
            </ul>
          </div>
        </li>

        <li className="hs-accordion group">
          <button
            className={
              "hs-accordion-toggle flex w-full items-center gap-x-3.5 rounded-md px-4 py-3 text-sm text-default-700 hover:bg-default-100 dark:text-white " +
              (pathname.startsWith("/orders") ||
              activeMenu === "orders" ||
              pathname.startsWith("/temp-orders")
                ? "bg-default-100 text-primary"
                : "")
            }
            aria-expanded="false"
            data-menu-key="orders"
            onClick={() => {
              setActiveMenu(activeMenu === "orders" ? "" : "orders");
            }}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="size-6"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M2.25 3h1.386c.51 0 .955.343 1.087.835l.383 1.437M7.5 14.25a3 3 0 0 0-3 3h15.75m-12.75-3h11.218c1.121-2.3 2.1-4.684 2.924-7.138a60.114 60.114 0 0 0-16.536-1.84M7.5 14.25 5.106 5.272M6 20.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0Zm12.75 0a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0Z"
              />
            </svg>
            Orders
            <DropCursor menu="orders" />
          </button>
          <div
            className={
              "hs-accordion-content w-full overflow-hidden transition-[height] " +
              (pathname.startsWith("/orders") ||
              pathname.startsWith("/temp-orders") ||
              activeMenu === "orders"
                ? "block"
                : "hidden")
            }
          >
            <ul className="mt-2 space-y-2">
              <li>
                <Link
                  className="flex items-center gap-x-3.5 rounded-md px-2.5 py-2 text-sm font-medium text-default-700 hover:bg-default-100 group-[.active]:bg-default-100 group-[.active]:text-primary dark:text-white"
                  data-menu-key="orders-list"
                  href="/orders"
                >
                  <svg
                    stroke="currentColor"
                    fill="none"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    height="24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <circle cx="12.1" cy="12.1" r="1"></circle>
                  </svg>
                  Orders
                </Link>
              </li>
              <li>
                <Link
                  className="flex items-center gap-x-3.5 rounded-md px-2.5 py-2 text-sm font-medium text-default-700 hover:bg-default-100 group-[.active]:bg-default-100 group-[.active]:text-primary dark:text-white"
                  data-menu-key="temp-orders-list"
                  href="/temp-orders"
                >
                  <svg
                    stroke="currentColor"
                    fill="none"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    height="24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <circle cx="12.1" cy="12.1" r="1"></circle>
                  </svg>
                  Pending requests
                </Link>
              </li>
              <li>
                <Link
                  className="flex items-center gap-x-3.5 rounded-md px-2.5 py-2 text-sm font-medium text-default-700 hover:bg-default-100 group-[.active]:bg-default-100 group-[.active]:text-primary dark:text-white"
                  data-menu-key="payments-list"
                  href="/payments"
                >
                  <svg
                    stroke="currentColor"
                    fill="none"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    height="24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <circle cx="12.1" cy="12.1" r="1"></circle>
                  </svg>
                  Payments
                </Link>
              </li>
              <li>
                <Link
                  className="flex items-center gap-x-3.5 rounded-md px-2.5 py-2 text-sm font-medium text-default-700 hover:bg-default-100 group-[.active]:bg-default-100 group-[.active]:text-primary dark:text-white"
                  data-menu-key="orders-details"
                  href="/orders/create"
                >
                  <svg
                    stroke="currentColor"
                    fill="none"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    height="24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <circle cx="12.1" cy="12.1" r="1"></circle>
                  </svg>
                  New order
                </Link>
              </li>
            </ul>
          </div>
        </li>

                <li className="hs-accordion group">
          <button
            className={
              "hs-accordion-toggle flex w-full items-center gap-x-3.5 rounded-md px-4 py-3 text-sm text-default-700 hover:bg-default-100 dark:text-white " +
              (pathname.startsWith("/billing") ||
              activeMenu === "billing" ||
              pathname.startsWith("/temp-orders")
                ? "bg-default-100 text-primary"
                : "")
            }
            aria-expanded="false"
            data-menu-key="billing"
            onClick={() => {
              setActiveMenu(activeMenu === "billing" ? "" : "billing");
            }}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="size-6"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M2.25 3h1.386c.51 0 .955.343 1.087.835l.383 1.437M7.5 14.25a3 3 0 0 0-3 3h15.75m-12.75-3h11.218c1.121-2.3 2.1-4.684 2.924-7.138a60.114 60.114 0 0 0-16.536-1.84M7.5 14.25 5.106 5.272M6 20.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0Zm12.75 0a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0Z"
              />
            </svg>
            Bills
          </button>

          <div
            className={
              "hs-accordion-content w-full overflow-hidden transition-[height] " +
              (pathname.startsWith("/orders") ||
              pathname.startsWith("/temp-orders") ||
              activeMenu === "orders"
                ? "block"
                : "hidden")
            }
          >
            <ul className="mt-2 space-y-2">
              <li>
                <Link
                  className="flex items-center gap-x-3.5 rounded-md px-2.5 py-2 text-sm font-medium text-default-700 hover:bg-default-100 group-[.active]:bg-default-100 group-[.active]:text-primary dark:text-white"
                  data-menu-key="orders-list"
                  href="/billing"
                >
                  <svg
                    stroke="currentColor"
                    fill="none"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    height="24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <circle cx="12.1" cy="12.1" r="1"></circle>
                  </svg>
                  My Bills
                </Link>
              </li>
            </ul>
          </div>


          
        </li>



        <li className="hs-accordion group">
          <button
            className={
              "hs-accordion-toggle flex w-full items-center gap-x-3.5 rounded-md px-4 py-3 text-sm hover:bg-default-100 " +
              (pathname.startsWith("/users") || activeMenu === "users"
                ? "bg-default-100 text-primary"
                : "text-default-700 dark:text-white")
            }
            aria-expanded="false"
            data-menu-key="customers"
            onClick={() => setActiveMenu(activeMenu === "users" ? "" : "users")}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="size-6"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m.94 3.198.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0 1 12 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 0 1 6 18.719m12 0a5.971 5.971 0 0 0-.941-3.197m0 0A5.995 5.995 0 0 0 12 12.75a5.995 5.995 0 0 0-5.058 2.772m0 0a3 3 0 0 0-4.681 2.72 8.986 8.986 0 0 0 3.74.477m.94-3.197a5.971 5.971 0 0 0-.94 3.197M15 6.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm6 3a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Zm-13.5 0a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Z"
              />
            </svg>
            User Accounts
            <DropCursor menu="users" />
          </button>
          <div
            className={
              "hs-accordion-content w-full overflow-hidden transition-[height] " +
              (pathname.startsWith("/users") || activeMenu === "users"
                ? "block"
                : "hidden")
            }
          >
            <ul className="mt-2 space-y-2">
              <li>
                <Link
                  className="flex items-center gap-x-3.5 rounded-md px-2.5 py-2 text-sm font-medium text-default-700 hover:bg-default-100 group-[.active]:bg-default-100 group-[.active]:text-primary dark:text-white"
                  data-menu-key="customers-list"
                  href="/staff"
                >

                {/* <Link
                  className="flex items-center gap-x-3.5 rounded-md px-2.5 py-2 text-sm font-medium text-default-700 hover:bg-default-100 group-[.active]:bg-default-100 group-[.active]:text-primary dark:text-white"
                  data-menu-key="customers-list"
                  href="/users?role=staff"
                > */}
                  <svg
                    stroke="currentColor"
                    fill="none"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    height="24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <circle cx="12.1" cy="12.1" r="1"></circle>
                  </svg>
                  Staff
                </Link>
              </li>
              <li>
                <Link
                  className="flex items-center gap-x-3.5 rounded-md px-2.5 py-2 text-sm font-medium text-default-700 hover:bg-default-100 group-[.active]:bg-default-100 group-[.active]:text-primary dark:text-white"
                  data-menu-key="customers-list"
                  href="/customers"
                >

              {/* <Link
                  className="flex items-center gap-x-3.5 rounded-md px-2.5 py-2 text-sm font-medium text-default-700 hover:bg-default-100 group-[.active]:bg-default-100 group-[.active]:text-primary dark:text-white"
                  data-menu-key="customers-list"
                  href="/users?role=customer"
                > */}
                  <svg
                    stroke="currentColor"
                    fill="none"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    height="24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <circle cx="12.1" cy="12.1" r="1"></circle>
                  </svg>
                  Customers
                </Link>
              </li>
            </ul>
          </div>
        </li> 

        <li className="hs-accordion group">
          <button
            className={
              "hs-accordion-toggle flex w-full items-center gap-x-3.5 rounded-md px-4 py-3 text-sm hover:bg-default-100 " +
              (pathname.startsWith("/products") || activeMenu === "products"
                ? "bg-default-100 text-primary"
                : "text-default-700 dark:text-white")
            }
            aria-expanded="false"
            data-menu-key="products"
            onClick={() =>
              setActiveMenu(activeMenu === "products" ? "" : "products")
            }
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="size-6"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M15.75 10.5V6a3.75 3.75 0 1 0-7.5 0v4.5m11.356-1.993 1.263 12c.07.665-.45 1.243-1.119 1.243H4.25a1.125 1.125 0 0 1-1.12-1.243l1.264-12A1.125 1.125 0 0 1 5.513 7.5h12.974c.576 0 1.059.435 1.119 1.007ZM8.625 10.5a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm7.5 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"
              />
            </svg>
            Products
            <DropCursor menu="products" />
          </button>
          <div
            className={
              "hs-accordion-content w-full overflow-hidden transition-[height] " +
              (pathname.startsWith("/products") || activeMenu === "products"
                ? "block"
                : "hidden")
            }
          >
            <ul className="mt-2 space-y-2">
              <li>
                <Link
                  className="flex items-center gap-x-3.5 rounded-md px-2.5 py-2 text-sm font-medium text-default-700 hover:bg-default-100 group-[.active]:bg-default-100 group-[.active]:text-primary dark:text-white"
                  data-menu-key="products-list"
                  href="/products"
                >
                  <svg
                    stroke="currentColor"
                    fill="none"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    height="24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <circle cx="12.1" cy="12.1" r="1"></circle>
                  </svg>
                  Products
                </Link>
              </li>

              <li>
                <Link
                  className="flex items-center gap-x-3.5 rounded-md px-2.5 py-2 text-sm font-medium text-default-700 hover:bg-default-100 group-[.active]:bg-default-100 group-[.active]:text-primary dark:text-white"
                  data-menu-key="products-add"
                  href="/products/create"
                >
                  <svg
                    stroke="currentColor"
                    fill="none"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    height="24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <circle cx="12.1" cy="12.1" r="1"></circle>
                  </svg>
                  Add Product
                </Link>
              </li>

              <li>
                <Link
                  className="flex items-center gap-x-3.5 rounded-md px-2.5 py-2 text-sm font-medium text-default-700 hover:bg-default-100 group-[.active]:bg-default-100 group-[.active]:text-primary dark:text-white"
                  data-menu-key="products-tag"
                  href="/tags"
                >
                  <svg
                    stroke="currentColor"
                    fill="none"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    height="24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <circle cx="12.1" cy="12.1" r="1"></circle>
                  </svg>
                  Tags
                </Link>
              </li>

              <li>
                <Link
                  className="flex items-center gap-x-3.5 rounded-md px-2.5 py-2 text-sm font-medium text-default-700 hover:bg-default-100 group-[.active]:bg-default-100 group-[.active]:text-primary dark:text-white"
                  data-menu-key="products-list"
                  href="/products/forms/templates"
                >
                  <svg
                    stroke="currentColor"
                    fill="none"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    height="24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <circle cx="12.1" cy="12.1" r="1"></circle>
                  </svg>
                  Form templates
                </Link>
              </li>
            </ul>
          </div>
        </li>

        <li className="hs-accordion group">
          <button
            className={
              "hs-accordion-toggle flex w-full items-center gap-x-3.5 rounded-md px-4 py-3 text-sm hover:bg-default-100 " +
              (pathname.startsWith("/message") || activeMenu === "messages"
                ? "bg-default-100 text-primary"
                : "text-default-700 dark:text-white")
            }
            aria-expanded="false"
            data-menu-key="messages"
            onClick={() =>
              setActiveMenu(activeMenu === "messages" ? "" : "messages")
            }
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="size-6"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M2.25 13.5h3.86a2.25 2.25 0 0 1 2.012 1.244l.256.512a2.25 2.25 0 0 0 2.013 1.244h3.218a2.25 2.25 0 0 0 2.013-1.244l.256-.512a2.25 2.25 0 0 1 2.013-1.244h3.859m-19.5.338V18a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18v-4.162c0-.224-.034-.447-.1-.661L19.24 5.338a2.25 2.25 0 0 0-2.15-1.588H6.911a2.25 2.25 0 0 0-2.15 1.588L2.35 13.177a2.25 2.25 0 0 0-.1.661Z"
              />
            </svg>
            Message center
            <DropCursor menu="messages" />
          </button>

          <div
            className={
              "hs-accordion-content w-full overflow-hidden transition-[height] " +
              (pathname.startsWith("/message") || activeMenu === "messages"
                ? "block"
                : "hidden")
            }
          >
            <ul className="mt-2 space-y-2">
              <li>
                <Link
                  className="flex items-center gap-x-3.5 rounded-md px-2.5 py-2 text-sm font-medium text-default-700 hover:bg-default-100 group-[.active]:bg-default-100 group-[.active]:text-primary dark:text-white"
                  data-menu-key="messages-list"
                  href="/messages"
                >
                  <svg
                    stroke="currentColor"
                    fill="none"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    height="24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <circle cx="12.1" cy="12.1" r="1"></circle>
                  </svg>
                  Messages
                </Link>
              </li>

              <li>
                <Link
                  className="flex items-center gap-x-3.5 rounded-md px-2.5 py-2 text-sm font-medium text-default-700 hover:bg-default-100 group-[.active]:bg-default-100 group-[.active]:text-primary dark:text-white"
                  data-menu-key="messages-add"
                  href="/messages/templates"
                >
                  <svg
                    stroke="currentColor"
                    fill="none"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    height="24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <circle cx="12.1" cy="12.1" r="1"></circle>
                  </svg>
                  Message templates
                </Link>
              </li>
            </ul>
          </div>
        </li>

        {tasks?.data?.map((task) => (
          <li key={task.id} className={`hs-accordion group group/${task.id}`}>
            <button
              className={`
                hs-accordion-toggle flex w-full items-center gap-x-3.5 rounded-md px-4 py-3 text-sm hover:bg-default-100 ${
                  pathname.startsWith(`/tasks/${task.id}`) ||
                  activeMenu === task.id
                    ? "bg-default-100 text-primary"
                    : "text-default-700 dark:text-white"
                }`}
              data-menu-key="extra-pages-starter"
              onClick={() =>
                setActiveMenu(activeMenu === task.id ? "" : task.id)
              }
            >
              {task.name}

              <DropCursor menu={task.id} />
            </button>

            <div
              className={`hs-accordion-content ml-4 w-full overflow-y-scroll transition-[height] ${
                pathname.startsWith(`/tasks/${task.id}`) ||
                activeMenu === task.id
                  ? "block"
                  : "hidden"
              }`}
            >
              <ul className="mt-2 space-y-2 overflow-y-scroll">
                <li className="">
                  <Link
                    className="flex items-center gap-x-3.5 rounded-md px-2.5 py-2 text-sm font-medium text-default-700 hover:bg-default-100 group-[.active]:bg-default-100 group-[.active]:text-primary dark:text-white"
                    data-menu-key="extra-pages-starter"
                    href={`/tasks/${task.id}`}
                  >
                    Overview
                  </Link>
                </li>

                {task.services?.map((service) => (
                  <li key={service.id} className="group">
                    <Link
                      className="flex items-center gap-x-3.5 rounded-md px-2.5 py-2 text-sm font-medium text-default-700 hover:bg-default-100 group-[.active]:bg-default-100 group-[.active]:text-primary dark:text-white"
                      data-menu-key="extra-pages-starter"
                      href={`/services/${service.id}`}
                    >
                      {service.name}
                    </Link>
                  </li>
                ))}


            {/* {task.services
              ?.filter((service) => service.vendorId === specificVendorId) // Replace 'specificVendorId' with the desired vendor ID
              .map((service) => (
                <li key={service.id} className="group">
                  <Link
                    className="flex items-center gap-x-3.5 rounded-md px-2.5 py-2 text-sm font-medium text-default-700 hover:bg-default-100 group-[.active]:bg-default-100 group-[.active]:text-primary dark:text-white"
                    data-menu-key="extra-pages-starter"
                    href={`/services/${service.id}`}
                  >
                    {service.name}
                  </Link>
                </li>
              ))} */}


              </ul>
            </div>
          </li>
        ))}
      </ul>
    </div>
  );



  // Assign the initial role to the first role in the array
let userRole = user?.roles[0].name;
const userRoleLength = user?.roles.length;
// Check if the user has multiple roles
if (userRoleLength && userRoleLength > 1) {
  // Loop through each role to check for "vendor" or "admin"
  for (let i = 0; i < userRoleLength; i++) {
    if (user?.roles[i].name === "vendor") {
      userRole = "vendor"; // Assign "vendor" as the role if found
      break; // Exit the loop if "vendor" is found
    } 
    else if (user?.roles[i].name === "admin") {
      userRole = "admin"; // Assign "admin" as the role if "vendor" isn't found
    }
  }
}


  return (
    <div
      id="application-sidebar"
      className="hs-overlay hs-overlay-open:translate-x-0 hide-in-print fixed inset-y-0 start-0 z-[50] hidden w-64 -translate-x-full transform border-e border-default-200 bg-white transition-all duration-300 dark:bg-default-900 dark:text-white lg:bottom-0 lg:right-auto lg:block lg:translate-x-0"
    >
      <div className="sticky top-0 flex h-20 items-center justify-center border-b border-dashed border-default-200 px-6">
        <Link href="/">
          <Image
            src={vendor ? imagePath(vendor.logo?.url) : logoImg}
            height={60}
            width={100}
            alt="logo"
            className="flex w-20 dark:hidden"
          />

          <Image
            src={vendor ? imagePath(vendor.logo?.url) : logoImg}
            height={60}
            width={100}
            alt="logo"
            className="hidden w-20 dark:flex"
          />
        </Link>
      </div>

      {branch && (
        <div className="gap-1.5 px-8">
          <p className="mt-4 text-primary">{branch.name}</p>
        </div>
      )}

      {/* {userRole=== "vendor" ? <VendorMenu  /> : <AdminMenu />} */}
      {userRole === "vendor" ? <VendorMenu /> : <AdminMenu /> }

      <ul className="fixed bottom-0 z-[150] flex w-64 flex-col gap-2 rounded-t-lg border-e border-default-200 bg-default-50 px-4 shadow-md">
        {branch && (
          <li>
            <Link
              className="flex items-center gap-x-3.5 rounded-md px-4 py-3 text-sm text-default-700 hover:bg-default-100 group-[.active]:bg-default-100 group-[.active]:text-primary"
              data-menu-key="manage-page"
              href="/preferences"
            >
              <svg
                stroke="currentColor"
                fill="none"
                strokeWidth="2"
                viewBox="0 0 24 24"
                strokeLinecap="round"
                strokeLinejoin="round"
                height="20"
                width="20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M20 7h-9"></path>
                <path d="M14 17H5"></path>
                <circle cx="17" cy="17" r="3"></circle>
                <circle cx="7" cy="7" r="3"></circle>
              </svg>
              Preferences
            </Link>
          </li>
        )}

        <li className="menu-item">
          <Link
            className="flex items-center gap-x-3.5 rounded-md px-4 py-3 text-sm text-default-700 hover:bg-default-100"
            href="/profile"
          >
            <svg
              stroke="currentColor"
              fill="none"
              strokeWidth="2"
              viewBox="0 0 24 24"
              strokeLinecap="round"
              strokeLinejoin="round"
              height="20"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path d="M18 20a6 6 0 0 0-12 0"></path>
              <circle cx="12" cy="10" r="4"></circle>
              <circle cx="12" cy="12" r="10"></circle>
            </svg>
            My Profile
          </Link>
        </li>
        <li className="menu-item ">
          <a
            className="flex items-center gap-x-3.5 rounded-md px-4 py-3 text-sm text-red-500 hover:bg-red-400/10 hover:text-red-600"
            href="/logout"
            onClick={logoutUser}
          >
            <svg
              stroke="currentColor"
              fill="none"
              strokeWidth="2"
              viewBox="0 0 24 24"
              strokeLinecap="round"
              strokeLinejoin="round"
              height="20"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
              <polyline points="16 17 21 12 16 7"></polyline>
              <line x1="21" x2="9" y1="12" y2="12"></line>
            </svg>
            Logout
          </a>
        </li>
      </ul>
    </div>
  );
}
