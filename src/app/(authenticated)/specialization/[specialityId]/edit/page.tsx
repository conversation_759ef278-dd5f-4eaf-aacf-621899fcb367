import { api } from "@/lib/api";
import SpecializationUpdateForm from "./form";
import { cache } from "react";

const fetchSpecialization = cache((id: string) =>
  api.get<Speciality>(`specialities/${id}`),
);

export const generateMetadata = async ({
  params: { specialityId },
}: {
  params: { specialityId: string };
}) => {
  const specialization = await fetchSpecialization(specialityId);

  return {
    title: specialization?.name,
    description: specialization?.details,
  };
};

export default async function page({
  params: { specialityId },
}: {
  params: { specialityId: string };
}) {
  const specialization = await fetchSpecialization(specialityId);

  const storeSpecialization = async (specialization: FormData) => {
    "use server";

    await api.put<Speciality>(`specialities/${specialityId}`, specialization);
  };

  return (
    <div>
      {specialization && (
        <SpecializationUpdateForm
          defaultValues={specialization}
          updateSpeciality={storeSpecialization}
        />
      )}
    </div>
  );
}
