"use client";

import AsyncSelect from "react-select/async";
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import toast from "react-hot-toast";
import { useRouter } from "next/navigation";

export default function SpecialityCreateForm({
  createSpeciality,
}: {
  createSpeciality: (speciality: FormData) => Promise<void>;
}) {
  const router = useRouter();
  const { handleSubmit, register, setValue, control } = useForm<
    Speciality & { upload: File }
  >({
    defaultValues: {
      name: "",
      details: "",
      serviceId: "",
    },
  });

  const handleUpload = (e: any) => {
    // @ts-ignore
    const files = e.target?.files;

    if (files) {
      const file = files[0];
      setValue("upload", file);
      const urlImage = URL.createObjectURL(file);
    }
  };

  const loadServices = async (inputValue: string) => {
    const response = await fetch(`/api/services?s=${inputValue}`);
    const data = await response.json();
    return data;
  };

  const onSubmit: SubmitHandler<Speciality & { upload: File }> = async (
    data: Speciality & { upload: File },
  ) => {
    const formData = new FormData();
    formData.append("name", data.name);
    formData.append("details", data.details);
    formData.append("serviceId", data.serviceId);

    if (data.upload) {
      formData.append("image", data.upload);
    }

    toast
      .promise(createSpeciality(formData), {
        loading: "Creating speciality...",
        success: "Speciality created!",
        error: "Failed to create speciality",
      })
      .then(() => {
        router.push("/specialization");
      });
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4 p-5">
      <div className="grid grid-cols-12">
        <label className="col-span-5">Name</label>
        <div className="col-span-7">
          <input
            type="text"
            className="w-full rounded-lg border border-primary px-3 py-2"
            {...register("name")}
          />
        </div>
      </div>

      <div className="grid grid-cols-12">
        <label className="col-span-5">Details</label>
        <div className="col-span-7">
          <textarea
            className="w-full rounded-lg border border-primary px-3 py-2"
            {...register("details")}
          />
        </div>
      </div>

      <div className="grid grid-cols-12">
        <label className="col-span-5">Service</label>
        <div className="col-span-7">
          <Controller
            name="serviceId"
            control={control}
            render={({ field }) => (
              <AsyncSelect
                loadOptions={loadServices}
                placeholder="Select service"
                getOptionLabel={(service: Service) => service.name}
                getOptionValue={(service: Service) => service.id}
                onChange={(v) => setValue("serviceId", v?.id!)}
                defaultOptions
                components={{
                  IndicatorSeparator: () => null,
                }}
                classNames={{
                  control: () =>
                    "w-full !rounded-lg !py-[4px] !border !border-primary !text-default-900 !text-sm",
                }}
                isClearable
              />
            )}
          />
        </div>
      </div>

      <div className="grid grid-cols-12">
        <label className="col-span-5">Image</label>
        <div className="col-span-7">
          <Controller
            name="upload"
            control={control}
            render={({ field }) => (
              <div className="flex h-20 w-full items-center justify-center rounded-lg border border-dashed border-primary">
                <p>Click to upload</p>

                <input type="file" className="hidden" onChange={handleUpload} />
              </div>
            )}
          />
        </div>
      </div>

      <div className="grid grid-cols-12">
        <div className="col-span-12 flex justify-end">
          <button
            type="submit"
            className="rounded-lg bg-primary px-3 py-2 text-white"
          >
            Create speciality
          </button>
        </div>
      </div>
    </form>
  );
}
