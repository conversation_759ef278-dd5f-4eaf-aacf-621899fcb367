"use client";

import { Submit<PERSON><PERSON><PERSON>, use<PERSON><PERSON>, Controller } from "react-hook-form";
import { toast } from "react-hot-toast";
import PhoneInput from "react-phone-number-input";

export default function StaffStaffCreateForm({
  defaultValues,
  storeStaff,
}: {
  defaultValues: User;
  storeStaff: (data: FormData) => Promise<void>;
}) {
  const {
    watch,
    handleSubmit,
    register,
    reset,
    setValue,
    control,
    formState: { errors, isSubmitting },
  } = useForm<User & { identifier: string; userId: string; vendorId: string }>({
    defaultValues,
  });

  const createStaff: SubmitHandler<
    User & { identifier: string; userId: string; vendorId: string }
  > = (
    staff: User & { identifier: string; userId: string; vendorId: string },
  ) => {
    const data = new FormData();

    data.append("identifier", staff.identifier);

    toast.promise(
      storeStaff(data),
      {
        loading: "Updating staff...",
        success: "Staff has been saved 👌",
        error: "Could not save staff 🤯",
      },
      {
        position: "bottom-center",
      },
    );

    reset();
  };

  return (
    <form onSubmit={handleSubmit(createStaff)}>
      <div className="space-y-4">
        <div className="grid grid-cols-12">
          <label
            htmlFor="name"
            className="col-span-5 mb-2 block text-sm font-medium text-default-900 dark:text-white"
          >
            Staff name
          </label>
          <div className="col-span-7 flex">
            <input
              type="text"
              {...register("firstName")}
              id="name"
              className="block w-full rounded-l-lg border border-r-0 border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
              placeholder="First name"
              required
            />
            <input
              type="text"
              {...register("lastName")}
              id="name"
              className="block w-full rounded-r-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
              placeholder="Last name"
              required
            />
          </div>
        </div>

        <div className="grid grid-cols-12">
          <label
            htmlFor="name"
            className="col-span-5 mb-2 block text-sm font-medium text-default-900 dark:text-white"
          >
            Staff email
          </label>
          <input
            type="email"
            {...register("email")}
            id="email"
            className="col-span-7 block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
            placeholder="Type staff email"
            required
          />
        </div>

        <div className="grid grid-cols-12">
          <label
            htmlFor="phone"
            className="col-span-5 mb-2 block text-sm font-medium text-default-900 dark:text-white"
          >
            Staff phone
          </label>
          <div className="form-input relative w-full max-w-full rounded-lg border border-default-200 bg-white px-4 py-3 dark:bg-default-50">
            <Controller
              name="phone"
              control={control}
              render={({ field: { onChange, value } }) => (
                <PhoneInput
                  placeholder="Enter phone number"
                  value={value}
                  onChange={onChange}
                  defaultCountry="KE"
                />
              )}
            />
          </div>
        </div>

        <div className="grid grid-cols-12">
          <label
            htmlFor="idpass"
            className="col-span-5 mb-2 block text-sm font-medium text-default-900 dark:text-white"
          >
            Staff Passport/National ID
          </label>
          <input
            type="text"
            {...register("idpass")}
            id="email"
            className="col-span-7 block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
            placeholder="Type national ID/Passport number"
            required
          />
        </div>

        <div className="grid grid-cols-12">
          <label
            htmlFor="name"
            className="col-span-5 mb-2 block text-sm font-medium text-default-900 dark:text-white"
          >
            Staff ID
          </label>
          <input
            type="text"
            {...register("identifier")}
            id="name"
            className="col-span-7 block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
            placeholder="Type staff ID"
            required
          />
        </div>

        <div className="flex w-full justify-end">
          <button
            type="submit"
            disabled={isSubmitting}
            className="w-60 justify-center rounded-lg bg-primary px-5 py-3 text-center text-sm font-medium text-white hover:bg-default-800 focus:outline-none focus:ring-4 focus:ring-default-300 dark:bg-primary dark:hover:bg-primary dark:focus:ring-default-800"
          >
            {isSubmitting ? "Saving..." : "Save staff details"}
          </button>
        </div>
      </div>
    </form>
  );
}
