import { api } from "@/lib/api";
import { revalidatePath } from "next/cache";
import StaffStaffCreateForm from "./form";
import { Metadata } from "next";
import { cache } from "react";
import Link from "next/link";
import { auth } from "@/auth";

const fetchStaff = cache((branchId: string, staffId: string) =>
  api.get<User & { identifier: string; vendorId: string }>(
    `/branches/${branchId}/staff/${staffId}`,
  ),
);

export const generateMetadata = async ({
  params,
}: {
  params: {
    staffId: string;
  };
}): Promise<Metadata> => {
  const session = await auth();
  const staff = await fetchStaff(session?.branch?.id!, params.staffId);

  return {
    title: staff?.name,
    description: `Edit Staff ${staff?.details}`,
  };
};

export default async function page({
  params,
}: {
  params: {
    staffId: string;
  };
}) {
  const session = await auth();
  const staff = await fetchStaff(session?.branch?.id!, params.staffId);

  const createStaff = async (formData: FormData) => {
    "use server";

    if (staff) {
      formData.append("vendorId", staff.vendorId);
      formData.append("userId", staff.id);
    }

    await api.put(
      `/branches/${session?.branch?.id}/staff/${params.staffId}`,
      formData,
    );

    revalidatePath(`/staff/${params.staffId}`);
  };

  return (
    <div className="p-4">
      <div className="mb-4 flex items-center justify-between">
        <h1 className="text-2xl text-primary">{staff?.details}</h1>
        <Link
          href={`/branches/${session?.branch?.id}/staff`}
          className="flex items-center space-x-2 text-default-500"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
            className="h-6 w-6"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M6.75 15.75 3 12m0 0 3.75-3.75M3 12h18"
            />
          </svg>

          <span>Back to Staff</span>
        </Link>
      </div>

      {staff && (
        <StaffStaffCreateForm storeStaff={createStaff} defaultValues={staff} />
      )}
    </div>
  );
}
