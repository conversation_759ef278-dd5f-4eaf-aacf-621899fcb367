import { auth } from "@/auth";
import { api } from "@/lib/api";
import Link from "next/link";
import { cache } from "react";

const fetchStaff = cache((branchId: string, staffId: string) =>
  api.get<User>(`/branches/${branchId}/staff/${staffId}`),
);

export const generateMetadata = async ({
  params,
}: {
  params: {
    staffId: string;
  };
}) => {
  const session = await auth();
  const staff = await fetchStaff(session?.branch?.id!, params.staffId);

  return {
    title: staff?.name,
    description: `Edit Staff ${staff?.details}`,
  };
};

export default async function page({
  params,
}: {
  params: {
    branchId: string;
    staffId: string;
  };
}) {
  const session = await auth();
  const staff = await fetchStaff(session?.branch?.id!, params.staffId);

  return (
    <div className="p-4">
      <h1>{staff?.name}</h1>

      <div>{staff?.phone}</div>

      <Link
        href={`/branches/${session?.branch?.id}/staff/${params.staffId}/edit`}
        className="font-medium text-default-600 hover:underline dark:text-blue-500"
      >
        Edit
      </Link>
    </div>
  );
}
