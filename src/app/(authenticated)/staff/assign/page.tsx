"use client";

// import { useState } from "react";
// import { Controller, useForm } from "react-hook-form";
// import { toast } from "react-hot-toast";
// import AsyncSelect from "react-select/async";
// import makeAnimated from "react-select/animated";

// type FormOptions = {
//   value: string;
//   label: string;
// };

// export default function AssignRoleForm() {
//   const [selectedRole, setSelectedRole] = useState(""); // State to hold selected role
//   const { control, setValue, handleSubmit } = useForm();

  

//   const animatedComponents = makeAnimated();

//   // Fetch staff members based on search input
//   const fetchUsers = async (inputValue: string) => {
//     if (inputValue.length > 3) {
//       const response = await fetch(`/api/users?s=${inputValue}`);
//       const data = await response.json();
//       return data.map((user: any) => ({
//         label: user.name,
//         value: user.id,
//       }));
//     }
//     return [];
//   };

//   // Submit handler for assigning role
//   const onSubmit = async (data: any) => {
//     if (!data.userId || !selectedRole) {
//       toast.error("Please select a staff member and a role.");
//       return;
//     }

//     const payload = {
//       user_id: data.userId,
//       role: selectedRole,
//     };

//     try {
//       const response = await fetch("https://aia-dev-api.hiisit.me/v1/auth/addroletouser", {
//         method: "POST",
//         headers: {
//           "Content-Type": "application/json",
//           Authorization: `Bearer ${localStorage.getItem("token")}`, // Use actual token here
//         },
//         body: JSON.stringify(payload),
//       });

//       if (response.ok) {
//         toast.success("Role assigned successfully!");
//       } else {
//         toast.error("Failed to assign role.");
//       }
//     } catch (error) {
//       console.error("Error:", error);
//       toast.error("Error assigning role.");
//     }
//   };

//   return (
//     <form onSubmit={handleSubmit(onSubmit)} className="p-4 max-w-md mx-auto">
//       <h2 className="mb-4 text-xl font-semibold">Assign Role</h2>

//       {/* Staff selection using AsyncSelect */}
//       <div className="mb-6">
//         <label className="block mb-2 font-medium text-default-900 dark:text-white">
//           Select Staff Member
//         </label>
//         <Controller
//           name="userId"
//           control={control}
//           render={({ field }) => (
//             <AsyncSelect
//               {...field}
//               loadOptions={fetchUsers}
//               onChange={(option) => setValue("userId", option?.value || "")}
//               components={animatedComponents}
//               isClearable
//               placeholder="Search and select staff"
//               className="w-full"
//             />
//           )}
//         />
//       </div>

//       {/* Role selection dropdown */}
//       <div className="mb-6">
//         <label className="block mb-2 font-medium text-default-900 dark:text-white">
//           Select Role
//         </label>
//         <select
//           className="w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 dark:bg-default-700 dark:border-default-600 dark:text-white"
//           onChange={(e) => setSelectedRole(e.target.value)}
//           defaultValue=""
//         >
//           <option value="" disabled>Select role</option>
//           <option value="employee">Employee</option>
//           <option value="accounts">Accounts</option>
//           <option value="customer">Customer</option>
//           <option value="student">Student</option>
//           <option value="unknown">Unknown</option>
//           <option value="rider">Rider</option>
//           <option value="waiter">Waiter</option>
//           <option value="cashier">Cashier</option>
//           <option value="bartender">Bartender</option>
//           <option value="chef">Chef</option>
//         </select>
//       </div>

//       {/* Submit button */}
//       <button
//         type="submit"
//         className="w-full bg-primary text-white py-2 rounded-lg hover:bg-primary-dark transition-colors"
//       >
//         Assign Role
//       </button>
//     </form>
//   );
// }


import { useState } from "react";
import { useForm, Controller } from "react-hook-form";
import AsyncSelect from "react-select/async";
import { toast } from "react-hot-toast";
import makeAnimated from "react-select/animated";

// Define the type for the option object
type OptionType = {
  value: string;
  label: string;
};

export default function AssignRoleForm() {
  const [selectedRole, setSelectedRole] = useState(""); // State to hold selected role
  const { control, setValue, handleSubmit } = useForm();

  const animatedComponents = makeAnimated();

  // Fetch staff members based on search input
  const fetchUsers = async (inputValue: string) => {
    if (inputValue.length > 3) {
      const response = await fetch(`/api/users?s=${inputValue}`);
      const data = await response.json();
      return data.map((user: any) => ({
        label: user.name,
        value: user.id,
      }));
    }
    return [];
  };

  // Submit handler for assigning role
  const onSubmit = async (data: any) => {
    if (!data.userId || !selectedRole) {
      toast.error("Please select a staff member and a role.");
      return;
    }

    const payload = {
      user_id: data.userId,
      role: selectedRole,
    };

    try {
      const response = await fetch("https://aia-dev-api.hiisit.me/v1/auth/addroletouser", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("token")}`, // Use actual token here
        },
        body: JSON.stringify(payload),
      });

      if (response.ok) {
        toast.success("Role assigned successfully!");
      } else {
        toast.error("Failed to assign role.");
      }
    } catch (error) {
      console.error("Error:", error);
      toast.error("Error assigning role.");
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="p-4 max-w-md mx-auto">
      <h2 className="mb-4 text-xl font-semibold">Assign Role</h2>

      {/* Staff selection using AsyncSelect */}
      <div className="mb-6">
        <label className="block mb-2 font-medium text-default-900 dark:text-white">
          Select Staff Member
        </label>
        <Controller
          name="userId"
          control={control}
          render={({ field }) => (
            <AsyncSelect
              {...field}
              loadOptions={fetchUsers}
              onChange={(newValue: unknown) => {
                const option = newValue as OptionType | null; // Explicitly cast to OptionType
                setValue("userId", option?.value || "");
              }}
              components={animatedComponents}
              isClearable
              placeholder="Search and select staff"
              className="w-full"
            />
          )}
        />
      </div>

      {/* Role selection dropdown */}
      <div className="mb-6">
        <label className="block mb-2 font-medium text-default-900 dark:text-white">
          Select Role
        </label>
        <select
          className="w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 dark:bg-default-700 dark:border-default-600 dark:text-white"
          onChange={(e) => setSelectedRole(e.target.value)}
          defaultValue=""
        >
          <option value="" disabled>Select role</option>
          <option value="employee">Employee</option>
          <option value="accounts">Accounts</option>
          <option value="customer">Customer</option>
          <option value="student">Student</option>
          <option value="unknown">Unknown</option>
          <option value="rider">Rider</option>
          <option value="waiter">Waiter</option>
          <option value="cashier">Cashier</option>
          <option value="bartender">Bartender</option>
          <option value="chef">Chef</option>
        </select>
      </div>

      {/* Submit button */}
      <button
        type="submit"
        className="w-full bg-primary text-white py-2 rounded-lg hover:bg-primary-dark transition-colors"
      >
        Assign Role
      </button>
    </form>
  );
}
