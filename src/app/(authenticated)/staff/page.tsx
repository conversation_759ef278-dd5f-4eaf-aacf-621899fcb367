import { deleteStaff, fetchStaff } from "@/actions/users";
import { auth } from "@/auth";
import StaffTable from "@/components/tables/staff-table";
import { revalidatePath } from "next/cache";

export default async function page({
  searchParams
}: {
  searchParams?: Record<string, string>;
}) {
  const session = await auth()

  const staff = await fetchStaff({ branchId: session?.branch?.id, searchParams: searchParams })

  const deleteUser = async(data:FormData) =>{
    "use server";

    try {
      await deleteStaff(data)
      revalidatePath(`/staff`)
      return true
    } catch (error) {
      console.log("ERROR DELETING USER:(branch/staff/details)", error)
      return false
    }
  }
  
  return (
    <div className="page-content p-5">
      <StaffTable
        data={staff?.data as User[]}
        meta={staff?.meta as PaginationMeta}
        params={{branchId: session?.branch?.id as string}}
        deleteStaff={deleteUser}
      />
    </div>
  );
}
