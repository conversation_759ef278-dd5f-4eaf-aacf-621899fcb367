import { api } from "@/lib/api";
import { Metadata } from "next";
import { cache } from "react";

const fetchTag = cache((id: string) => api.get<Tag>(`tags/${id}`));

export const generateMetadata = async ({
	params: { tagId },
}: {
	params: { tagId: string };
}): Promise<Metadata> => {
	const tag = await fetchTag(tagId);
	return {
		title: tag?.name,
	};
};

export default async function page({
	params: { tagId },
}: {
	params: { tagId: string };
}) {
	const tag = await fetchTag(tagId);

	return (
		<div className="p-6">
			<h1>{tag?.name}</h1>

			{tag?.details && (
				<article dangerouslySetInnerHTML={{ __html: tag.details }} />
			)}
		</div>
	);
}
