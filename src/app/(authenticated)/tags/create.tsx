"use client";

import Select from "react-select";
import AsyncSelect from "react-select/async";
import { useState, ChangeEvent } from "react";
import makeAnimated from "react-select/animated";
import { Submit<PERSON><PERSON><PERSON>, Controller, useForm } from "react-hook-form";
import { toast } from "react-hot-toast";
import Image from "next/image";
import { useClickOutside } from "@/hooks/useClickOutside";

export default function AdminTagsCreateForm({
    storeTag,
    classNames="text-white bg-primary hover:bg-default-800 rounded-lg py-2 px-6 dark:bg-primary flex justify-center items-center space-x-3"
}: {
    storeTag: (data: FormData) => Promise<void>;
    classNames?: string
}) {
    const [creating, setCreating] = useState(false);

    const ref = useClickOutside(() => setCreating(false));

    const {
        setValue,
        watch,
        handleSubmit,
        register,
        reset,
        control,
        formState: { errors, isSubmitting },
    } = useForm<Tag>({
        defaultValues: {
            name: "",
            details: "",
        }
    });

    const tag = watch();

    const createTag: SubmitHandler<Tag> = (tag: Tag) => {
        const data = new FormData();

        data.append("name", tag.name!);
        data.append("details", tag.details!);

        toast.promise(
            storeTag(data),
            {
                loading: "Creating tag...",
                success: "Tag has been saved 👌",
                error: "Could not save tag 🤯",
            },
            {
                position: "bottom-center",
            }
        );

        reset();

        setCreating(false);
    };

    return (
        <>
            <button
                onClick={() => setCreating(!creating)}
                id="createTagButton"
                className={classNames}
                type="button"
            >
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="currentColor"
                    className="bi bi-plus-lg h-5 w-5"
                    viewBox="0 0 16 16"
                >
                    <path
                        fillRule="evenodd"
                        d="M8 2a.5.5 0 0 1 .5.5v5h5a.5.5 0 0 1 0 1h-5v5a.5.5 0 0 1-1 0v-5h-5a.5.5 0 0 1 0-1h5v-5A.5.5 0 0 1 8 2Z"
                    />
                </svg>
                <span>Add tag</span>
            </button>

            {creating && (
                <div
                    ref={ref}
                    id="drawer-create-tag-default"
                    className="fixed top-0 right-0 z-40 w-full h-screen max-w-xs p-4 overflow-y-auto transition-transform bg-white dark:bg-default-800"
                    tabIndex={-1}
                    aria-labelledby="drawer-label"
                    aria-hidden="true"
                >
                    <h5
                        id="drawer-label"
                        className="inline-flex items-center mb-6 text-sm font-semibold text-default-500 uppercase dark:text-default-400"
                    >
                        New Tag
                    </h5>
                    <button
                        onClick={() => setCreating(!creating)}
                        type="button"
                        data-drawer-dismiss="drawer-create-tag-default"
                        aria-controls="drawer-create-tag-default"
                        className="text-default-400 bg-transparent hover:bg-default-200 hover:text-default-900 rounded-lg text-sm p-1.5 absolute top-2.5 right-2.5 inline-flex items-center dark:hover:bg-default-600 dark:hover:text-white"
                    >
                        <svg
                            aria-hidden="true"
                            className="w-5 h-5"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                fillRule="evenodd"
                                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                clipRule="evenodd"
                            ></path>
                        </svg>
                        <span className="sr-only">Close menu</span>
                    </button>
                    <form onSubmit={handleSubmit(createTag)}>
                        <div className="space-y-4">
                            <div>
                                <label
                                    htmlFor="name"
                                    className="block mb-2 text-sm font-medium text-default-900 dark:text-white"
                                >
                                    Name
                                </label>
                                <input
                                    type="text"
                                    {...register("name")}
                                    id="name"
                                    className="bg-default-50 border border-default-300 text-default-900 text-sm rounded-lg focus:ring-default-600 focus:border-default-600 block w-full p-3 dark:bg-default-700 dark:border-default-600 dark:placeholder-default-400 dark:text-white dark:focus:ring-default-600 dark:focus:border-default-500"
                                    placeholder="Type tag name"
                                    required
                                />
                            </div>

                            <div>
                                <label
                                    htmlFor="description"
                                    className="block mb-2 text-sm font-medium text-default-900 dark:text-white"
                                >
                                    Description
                                </label>
                                <textarea
                                    id="description"
                                    rows={4}
                                    {...register("details")}
                                    className="block p-3 .5 w-full text-sm text-default-900 bg-default-50 rounded-lg border border-default-300 focus:ring-default-600 focus:border-default-600 dark:bg-default-700 dark:border-default-600 dark:placeholder-default-400 dark:text-white dark:focus:ring-default-600 dark:focus:border-default-500"
                                    placeholder="Enter tag description here"
                                />
                            </div>

                            <div className="bottom-0 left-0 flex justify-center w-full pb-4 space-x-4 md:px-4 md:absolute">
                                <button
                                    type="submit"
                                    disabled={isSubmitting}
                                    className="text-white w-full justify-center bg-primary hover:bg-default-800 focus:ring-4 focus:outline-none focus:ring-default-300 font-medium rounded-lg text-sm px-5 py-3 text-center dark:bg-primary dark:hover:bg-primary dark:focus:ring-default-800"
                                >
                                    Save tag details
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            )}
        </>
    );
}
