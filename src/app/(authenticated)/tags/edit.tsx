'use client'

import { useState, useRef, ChangeEvent, useEffect } from "react"
import { SubmitHandler, useForm } from "react-hook-form";
import { toast } from "react-hot-toast";
import Image from "next/image";
import { useClickOutside } from "@/hooks/useClickOutside";

export default function AdminTagsEditForm({ defaultValues, updateTag }: { defaultValues: Tag, updateTag: (data: FormData) => Promise<void> }) {
    const [editing, setEditing] = useState(false)
    const [preview, setPreview] = useState<string>();

    const ref = useClickOutside(() => setEditing(false));

    const {
        watch, handleSubmit, register, reset, setValue,
        formState: { errors, isSubmitting }
    } = useForm<Partial<Tag & { upload: File }>>({
        defaultValues
    })

    const tag = watch()

    const handleUploadedFile = (event: ChangeEvent<HTMLInputElement>) => {
        const files = event.target.files

        if (files) {
            const file = files[0];
            setValue('upload', file)
            const urlImage = URL.createObjectURL(file);

            setPreview(urlImage);
        }
    };

    const editTag: SubmitHandler<Partial<Tag & { upload: File }>> = (tag: Partial<Tag & { upload: File }>) => {
        const data = new FormData

        data.append('name', tag.name!)
        data.append('details', tag.details!)
        data.append('image', tag.upload!)

        toast.promise(
            updateTag(data),
            {
                loading:'Updating tag...',
                success: 'Tag has been saved 👌',
                error: 'Could not save tag 🤯'
            },
            {
                position: 'bottom-center',
            }
        ).then(() => {
            reset({
                name: "",
                details: ""
            })

            setEditing(false)
        })
    }

    useEffect(() => {
        // const tagImage = imagePath(defaultValues.image?.url, defaultValues.imageUrl)
        // setPreview(tagImage)
    }, [])

    return <>
        <button
            onClick={() => setEditing(!editing)}
            id="editTagButton"
            className="z-5 text-white bg-primary hover:bg-default-800 focus:ring-4 focus:ring-default-300 font-medium text-sm h-8 w-8 rounded-full flex justify-center items-center dark:bg-primary dark:hover:bg-primary focus:outline-none dark:focus:ring-default-800"
            type="button"
        >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" className="bi bi-pencil-fill" viewBox="0 0 16 16">
                <path d="M12.854.146a.5.5 0 0 0-.707 0L10.5 1.793 14.207 5.5l1.647-1.646a.5.5 0 0 0 0-.708l-3-3zm.646 6.061L9.793 2.5 3.293 9H3.5a.5.5 0 0 1 .5.5v.5h.5a.5.5 0 0 1 .5.5v.5h.5a.5.5 0 0 1 .5.5v.5h.5a.5.5 0 0 1 .5.5v.207l6.5-6.5zm-7.468 7.468A.5.5 0 0 1 6 13.5V13h-.5a.5.5 0 0 1-.5-.5V12h-.5a.5.5 0 0 1-.5-.5V11h-.5a.5.5 0 0 1-.5-.5V10h-.5a.499.499 0 0 1-.175-.032l-.179.178a.5.5 0 0 0-.11.168l-2 5a.5.5 0 0 0 .65.65l5-2a.5.5 0 0 0 .168-.11l.178-.178z" />
            </svg>
        </button>

        {editing && <div
            ref={ref}
            id="drawer-edite-tag-default"
            className="fixed top-0 right-0 z-40 w-full h-screen max-w-xs p-4 overflow-y-auto transition-transform bg-white dark:bg-default-800"
            tabIndex={-1}
            aria-labelledby="drawer-label"
            aria-hidden="true"
        >
            <h5
                id="drawer-label"
                className="inline-flex items-center mb-6 text-sm font-semibold text-default-500 uppercase dark:text-default-400"
            >
                {tag.name}
            </h5>
            <button
                onClick={() => setEditing(!editing)}
                type="button"
                data-drawer-dismiss="drawer-edite-tag-default"
                aria-controls="drawer-edite-tag-default"
                className="text-default-400 bg-transparent hover:bg-default-200 hover:text-default-900 rounded-lg text-sm p-1.5 absolute top-2.5 right-2.5 inline-flex items-center dark:hover:bg-default-600 dark:hover:text-white"
            >
                <svg
                    aria-hidden="true"
                    className="w-5 h-5"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path
                        fillRule="evenodd"
                        d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                        clipRule="evenodd"
                    ></path>
                </svg>
                <span className="sr-only">Close menu</span>
            </button>

            <form onSubmit={handleSubmit(editTag)}>
                <div className="space-y-4">
                    <div>
                        <label
                            htmlFor="name"
                            className="block mb-2 text-sm font-medium text-default-900 dark:text-white"
                        >
                            Name
                        </label>
                        <input
                            type="text"
                            {...register('name')}
                            id="name"
                            className="bg-default-50 border border-default-300 text-default-900 text-sm rounded-lg focus:ring-default-600 focus:border-default-600 block w-full p-3 dark:bg-default-700 dark:border-default-600 dark:placeholder-default-400 dark:text-white dark:focus:ring-default-600 dark:focus:border-default-500"
                            placeholder="Type tag name"
                            required
                        />
                    </div>

                    <div>
                        <label
                            htmlFor="image"
                            className="block mb-2 text-sm font-medium text-default-900 dark:text-white"
                        >
                            Image or icon
                        </label>
                        <label
                            className="bg-default-50 border border-dashed border-default-300 text-default-900 text-sm rounded-lg focus:ring-default-600 focus:border-default-600 w-full h-40 flex justify-center items-center dark:bg-default-700 dark:border-default-600 dark:placeholder-default-400 dark:text-white dark:focus:ring-default-600 dark:focus:border-default-500">
                            <input
                                type="file"
                                name="image"
                                id="image"
                                className="hidden"
                                onChange={handleUploadedFile}
                            />
                            {preview
                                ? <Image src={preview} alt="preview" width={100} height={100} className="w-1/2" />
                                : <p>Click to select file</p>}
                        </label>
                    </div>

                    <div>
                        <label
                            htmlFor="description"
                            className="block mb-2 text-sm font-medium text-default-900 dark:text-white"
                        >
                            Description
                        </label>
                        <textarea
                            id="description"
                            rows={4}
                            {...register("details")}
                            className="block p-3 .5 w-full text-sm text-default-900 bg-default-50 rounded-lg border border-default-300 focus:ring-default-600 focus:border-default-600 dark:bg-default-700 dark:border-default-600 dark:placeholder-default-400 dark:text-white dark:focus:ring-default-600 dark:focus:border-default-500"
                            placeholder="Enter tag description here"
                        />
                    </div>

                    <div className="bottom-0 left-0 flex justify-center w-full pb-4 space-x-4 md:px-4 md:absolute">
                        <button
                            type="submit"
                            disabled={isSubmitting}
                            className="text-white w-full justify-center bg-primary hover:bg-default-800 focus:ring-4 focus:outline-none focus:ring-default-300 font-medium rounded-lg text-sm px-5 py-3 text-center dark:bg-primary dark:hover:bg-primary dark:focus:ring-default-800"
                        >
                            Update tag details
                        </button>
                    </div>
                </div>
            </form>
        </div>}
    </>
}