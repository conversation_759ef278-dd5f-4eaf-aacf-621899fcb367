import { api, imagePath } from "@/lib/api";
import { Metadata } from "next";
import Link from "next/link";
import AdminTagsCreateForm from "./create";
import AdminTagsEditForm from "./edit";
import { revalidatePath } from "next/cache";
import Image from "next/image";
import TagSearchForm from "./search";

export const dynamic = "force-dynamic";
export const fetchCache = "force-no-store";

export const metadata: Metadata = {
	title: "Tags",
};

export default async function AdminTagIndex() {
	const tags = await api.get<PaginatedData<Tag>>("tags", { per: 30 });

	const storeTag = async (tag: FormData) => {
		"use server";

		await api.post("tags", tag);
		revalidatePath("/products/tags");
	};

	const updateTag = async (tag: FormData) => {
		"use server";

		await api.put(`tags/${tag.get("id")}`, tag);
		revalidatePath("/products/tags");
	};

	return (
		<div className="flex flex-col flex-1 p-6 space-y-5">
			<div className="flex justify-between items-center">
				<TagSearchForm storeTag={storeTag} />

				<AdminTagsCreateForm storeTag={storeTag} />
			</div>

			<div className={`flex flex-wrap gap-3`}>
				{tags?.data.map(tag => (
					<div
						className={`px-4 py-2 bg-primary text-white  rounded-lg group/tag${tag.id}`}
						key={tag.id}
					>
						<Link href={`/tags/${tag.id}`}>{tag.name}</Link>

						<article
							className={`hidden group-hover/tag${tag.id}:block`}
						>
							{tag.details}
						</article>
					</div>
				))}
			</div>
		</div>
	);
}
