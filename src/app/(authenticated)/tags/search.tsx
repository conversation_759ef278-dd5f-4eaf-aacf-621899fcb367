"use client";

import toast from "react-hot-toast";
import CreatableSelect from "react-select/creatable";

export default function TagSearchForm({
	storeTag,
}: {
	storeTag: (tag: FormData) => Promise<void>;
}) {
	const createTag = async (name: string) => {
		const data = new FormData();
		data.append("name", name);

		toast.promise(storeTag(data), {
			loading: "Creating tag...",
			success: "Tag has been saved 👌",
			error: "Could not save tag 🤯",
		});
	};

	return (
		<form>
			<CreatableSelect
				placeholder="Search in tags"
				classNames={{
					control: () =>
						"w-80 !rounded-lg !py-[4px] !border !border-default-300 !px-2",
				}}
				components={{
					IndicatorSeparator: () => null,
					DropdownIndicator: () => null,
				}}
				onCreateOption={createTag}
			/>
		</form>
	);
}
