"use client";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
	Sheet,
	SheetClose,
	SheetContent,
	She<PERSON>D<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>er,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	She<PERSON><PERSON>rigger,
} from "@/components/ui/sheet";
import { useParams } from "next/navigation";

import { Controller, SubmitHandler, useForm } from "react-hook-form";
import toast from "react-hot-toast";

export default function ServiceCreateForm({
	createService,
}: {
	createService: (service: FormData) => Promise<void>;
}) {
    const params = useParams();
	const { handleSubmit, register, setValue, control } = useForm<
		Service & { upload: File }
	>({
		defaultValues: {
			name: "",
			slug: "",
			details: "",
            taskId: String(params.taskId),
		},
	});

	const handleUpload = (e: any) => {
		// @ts-ignore
		const files = e.target?.files;

		if (files) {
			const file = files[0];
			setValue("upload", file);
			const urlImage = URL.createObjectURL(file);
		}
	};

	const onSubmit: SubmitHandler<Service & { upload: File }> = async (
		data: Service & { upload: File }
	) => {
		const formData = new FormData();
		formData.append("name", data.name);
		formData.append("slug", data.slug);
		formData.append("details", data.details);
        formData.append("taskId", data.taskId);

		if (data.upload) {
			formData.append("image", data.upload);
		}

		toast.promise(createService(formData), {
			loading: "Creating service...",
			success: "Service created!",
			error: "Failed to create service",
		});
	};

	return (
		<Sheet>
			<SheetTrigger asChild>
				<Button
					variant="outline"
					className="py-3 px-4 bg-primary text-white rounded-lg text-sm font-bold hover:bg-default-dark transition-all duration-200 ease-in-out"
				>
					Add new service
				</Button>
			</SheetTrigger>
			<SheetContent>
				<SheetHeader>
					<SheetTitle>Add service</SheetTitle>
					<SheetDescription>
						Add service dettails and save
					</SheetDescription>
				</SheetHeader>

				<form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
					<div className="space-y-2">
						<label className="col-span-5">Name</label>
						<div className="col-span-7">
							<input
								type="text"
								className="border border-primary rounded-lg py-2 px-3 w-full"
                                placeholder="Enter service name here"
								{...register("name")}
							/>
						</div>
					</div>

					<div className="space-y-2">
						<label className="col-span-5">Slug</label>
						<div className="col-span-7">
							<input
								type="text"
								className="border border-primary rounded-lg py-2 px-3 w-full"
                                placeholder="Enter service slug here"
								{...register("slug")}
							/>
						</div>
					</div>

					<div className="space-y-2">
						<label className="col-span-5">Details</label>
						<div className="col-span-7">
							<textarea
								className="border border-primary rounded-lg py-2 px-3 w-full"
                                placeholder="Enter service details here"
								{...register("details")}
							/>
						</div>
					</div>

					<div className="space-y-2">
						<label className="col-span-5">Image</label>
						<div className="col-span-7">
							<Controller
								name="upload"
								control={control}
								render={({ field }) => (
									<div className="border border-primary border-dashed h-40 flex justify-center items-center rounded-lg w-full">
										<p>Click to upload</p>

										<input
											type="file"
											className="hidden"
											onChange={handleUpload}
										/>
									</div>
								)}
							/>
						</div>
					</div>

					<button
						type="submit"
						className="bg-primary text-white rounded-lg py-2 px-3 w-full mt-5"
					>
						Create service
					</button>
				</form>
				{/* <SheetFooter>
					<SheetClose asChild>
						<Button type="submit">Save changes</Button>
					</SheetClose>
				</SheetFooter> */}
			</SheetContent>
		</Sheet>
	);
}
