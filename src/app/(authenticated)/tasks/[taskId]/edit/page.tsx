import { api } from "@/lib/api";
import { cache } from "react";
import TaskEditForm from "./form";
import { revalidatePath } from "next/cache";

const fetchTask = cache((taskId: string) => api.get<Task>(`tasks/${taskId}`));

export const generateMetadata = async ({
	params,
}: {
	params: {
		taskId: string;
	};
}) => {
	const task = await fetchTask(params.taskId);

	return {
		title: `Edit ${task?.name}`,
	};
};

export default async function page({
  params,
}: {
  params: {
    taskId: string;
  };
}) {
  const task = await fetchTask(params.taskId);

  const updateTask = async (formData: FormData) => {
    "use server";

    await api.put(`tasks/${task?.id}`, formData);

    revalidatePath(`/tasks/${params.taskId}`);
  };

  return (
    <div>{task && <TaskEditForm task={task} updateTask={updateTask} />}</div>
  );
}
