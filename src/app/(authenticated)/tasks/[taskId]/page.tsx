import { auth } from "@/auth";
import BackBtn from "@/components/back";
import ServicesTable from "@/components/tables/services-table";
import { api } from "@/lib/api";
import { revalidatePath } from "next/cache";
import { cache } from "react";

const fetchTask = cache((id: string) => api.get<Task>(`tasks/${id}`));

export const generateMetadata = async ({
  params,
}: {
  params: {
    taskId: string;
  };
}): Promise<Record<string, any>> => {
  const task = await fetchTask(params.taskId);

  return {
    title: task?.name,
    description: "Task",
    keywords: ["task"],
  };
};

export default async function page({
  searchParams,
  params,
}: {
  params: {
    taskId: string;
  };
  searchParams: Record<string, string>;
}) {
  const session = await auth();
  const task = await fetchTask(params.taskId);

  // Get role of the logged in user
  const userDetail: unknown = session?.user ?  await api.get(`/users/${session?.user?.id}`) : "";

  const role = userDetail as User;





  const services = session?.vendor?.id
    ? await api.get<PaginatedData<Service>>(
        `vendors/${session?.vendor?.id}/services`,
        { per: 50, task: params.taskId, ...searchParams },
      )
    : await api.get<PaginatedData<Service>>(`tasks/${params.taskId}/services`, {
        per: 50,
        ...searchParams,
      });

  const toggleServiceStatus = async (service: Service) => {
    "use server";

    await api.patch(`services/${service.id}`, { active: !service.active });

    revalidatePath(`/tasks/${params.taskId}`);
  };

  const storeService = async (service: FormData) => {
    "use server";

    const res = await api.post<Service>("services", service);

    revalidatePath(`/tasks/${params.taskId}`);
  };

  const deleteService = async (formData: FormData) => {
    "use server";

    await api.destroy(formData.get("id")?.toString()!, `services`);

    revalidatePath(`/tasks/${params.taskId}`);
  };

  return (
    <div className="bg-white p-5 h-full min-h-screen">
      <BackBtn />

      {services && (
        <ServicesTable
          role={role}
          data={services.data}
          meta={services.meta}
          action={deleteService}
          toggle={toggleServiceStatus}
          taskId={params.taskId}
        />
      )}
    </div>
  );
}
