"use client";

import { createTask } from "@/actions/tasks";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
	Sheet,
	SheetClose,
	SheetContent,
	SheetDescription,
	SheetFooter,
	SheetHeader,
	SheetTitle,
	SheetTrigger,
} from "@/components/ui/sheet";

import { Controller, SubmitHandler, useForm } from "react-hook-form";
import toast from "react-hot-toast";

export default function TaskCreateForm() {
	const { handleSubmit, register, setValue, control } = useForm<
		Task & { upload: File }
	>({
		defaultValues: {
			name: "",
			slug: "",
			details: "",
		},
	});

	const handleUpload = (e: any) => {
		// @ts-ignore
		const files = e.target?.files;

		if (files) {
			const file = files[0];
			setValue("upload", file);
			const urlImage = URL.createObjectURL(file);
		}
	};

	const onSubmit: SubmitHandler<Task & { upload: File }> = async (
		data: Task & { upload: File }
	) => {
		const formData = new FormData();
		formData.append("name", data.name);
		formData.append("slug", data.slug);
		formData.append("details", data.details);

		if (data.upload) {
			formData.append("image", data.upload);
		}

		toast.promise(createTask(formData), {
			loading: "Updating task...",
			success: "Task created!",
			error: "Failed to create task",
		});
	};

	return (
		<Sheet>
			<SheetTrigger asChild>
				<Button
					variant="outline"
					className="bg-primary text-white px-6 py-2 rounded-lg text-base"
				>
					Add new task
				</Button>
			</SheetTrigger>
			<SheetContent>
				<SheetHeader>
					<SheetTitle>
                        Add task
                    </SheetTitle>
					<SheetDescription>
                        Add task dettails and save
					</SheetDescription>
				</SheetHeader>

				<form
					onSubmit={handleSubmit(onSubmit)}
					className="space-y-4"
				>
					<div className="space-y-2">
						<label className="col-span-5">Name</label>
						<div className="col-span-7">
							<input
								type="text"
								className="border border-primary rounded-lg py-2 px-3 w-full"
								{...register("name")}
							/>
						</div>
					</div>

					<div className="space-y-2">
						<label className="col-span-5">Slug</label>
						<div className="col-span-7">
							<input
								type="text"
								className="border border-primary rounded-lg py-2 px-3 w-full"
								{...register("slug")}
							/>
						</div>
					</div>

					<div className="space-y-2">
						<label className="col-span-5">Details</label>
						<div className="col-span-7">
							<textarea
								className="border border-primary rounded-lg py-2 px-3 w-full"
								{...register("details")}
							/>
						</div>
					</div>

					<div className="space-y-2">
						<label className="col-span-5">Image</label>
						<div className="col-span-7">
							<Controller
								name="upload"
								control={control}
								render={({ field }) => (
									<div className="border border-primary border-dashed h-40 flex justify-center items-center rounded-lg w-full">
										<p>Click to upload</p>

										<input
											type="file"
											className="hidden"
											onChange={handleUpload}
										/>
									</div>
								)}
							/>
						</div>
					</div>

							<button
								type="submit"
								className="bg-primary text-white rounded-lg py-2 px-3 w-full mt-5"
							>
								Create task
							</button>
				</form>
				{/* <SheetFooter>
					<SheetClose asChild>
						<Button type="submit">Save changes</Button>
					</SheetClose>
				</SheetFooter> */}
			</SheetContent>
		</Sheet>
	);
}
