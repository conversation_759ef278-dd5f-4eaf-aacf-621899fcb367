"use client";

import { Icon } from "@/components/icon";
import { <PERSON><PERSON>, <PERSON> } from "@nextui-org/react";
import {
  Sheet,
  <PERSON>et<PERSON>lose,
  Sheet<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON>rigger,
} from "@/components/ui/sheet";

import { ChangeEvent } from "preact/compat";
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import toast from "react-hot-toast";

export default function TaskUpdateForm({
  defaultValues,
  updateTask,
}: {
  defaultValues: Task;
  updateTask: (task: FormData) => Promise<void>;
}) {
  const { handleSubmit, register, setValue, control } = useForm<
    Task & { upload: File }
  >({
    defaultValues,
  });

  const handleUpload = (e: any) => {
    // @ts-ignore
    const files = e.target?.files;

    if (files) {
      const file = files[0];
      setValue("upload", file);
      const urlImage = URL.createObjectURL(file);
    }
  };

  const onSubmit: SubmitHandler<Task & { upload: File }> = async (
    data: Task & { upload: File },
  ) => {
    const formData = new FormData();
    formData.append("name", data.name);
    formData.append("slug", data.slug);
    formData.append("details", data.details);

    if (data.upload) {
      formData.append("image", data.upload);
    }

    toast.promise(updateTask(formData), {
      loading: "Updating task...",
      success: "Task updated!",
      error: "Failed to update task",
    });
  };

  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button className="rounded-lg">
          <Icon name="icon-[mage--edit-pen-fill]" classNames="text-primary" />
        </Button>
      </SheetTrigger>
      <SheetContent>
        <SheetHeader>
          <SheetTitle>Add task</SheetTitle>
          <SheetDescription>Add task dettails and save</SheetDescription>
        </SheetHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="space-y-2">
            <label className="col-span-5">Name</label>
            <div className="col-span-7">
              <input
                type="text"
                className="w-full rounded-lg border border-primary px-3 py-2"
                {...register("name")}
              />
            </div>
          </div>

          <div className="space-y-2">
            <label className="col-span-5">Slug</label>
            <div className="col-span-7">
              <input
                type="text"
                className="w-full rounded-lg border border-primary px-3 py-2"
                {...register("slug")}
              />
            </div>
          </div>

          <div className="space-y-2">
            <label className="col-span-5">Details</label>
            <div className="col-span-7">
              <textarea
                className="w-full rounded-lg border border-primary px-3 py-2"
                {...register("details")}
              />
            </div>
          </div>

          <div className="space-y-2">
            <label className="col-span-5">Image</label>
            <div className="col-span-7">
              <Controller
                name="upload"
                control={control}
                render={({ field }) => (
                  <div className="flex h-40 w-full items-center justify-center rounded-lg border border-dashed border-primary">
                    <p>Click to upload</p>

                    <input
                      type="file"
                      className="hidden"
                      onChange={handleUpload}
                    />
                  </div>
                )}
              />
            </div>
          </div>

          <button
            type="submit"
            className="mt-5 w-full rounded-lg bg-primary px-3 py-2 text-white"
          >
            Update task
          </button>
        </form>
        {/* <SheetFooter>
					<SheetClose asChild>
						<Button type="submit">Save changes</Button>
					</SheetClose>
				</SheetFooter> */}
      </SheetContent>
    </Sheet>
  );
}
