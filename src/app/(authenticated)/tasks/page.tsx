import { auth } from "@/auth";
import { Metadata } from "next";
import TaskTable from "./task-table";
import { fetchTasks } from "@/actions/tasks";

export const dynamic = "force-dynamic";
export const fetchCache = "force-no-store";

export const metadata: Metadata = {
  title: "Tasks",
  description: "Tasks",
  keywords: ["task"],
};

export default async function page({
  searchParams,
}: {
  searchParams: Record<string, string>;
}) {
  const session = await auth();

  const tasks = await fetchTasks(
    session?.vendor?.id,
    searchParams || { per: "20" },
  );

  // Get userRole

  return (
    <div className="h-full min-h-screen bg-white p-5 dark:bg-default-900">
      {tasks && (
        <TaskTable data={tasks.data} meta={tasks.meta} session={session} />
      )}
    </div>
  );
}
