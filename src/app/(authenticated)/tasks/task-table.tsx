"use client";

import { CustomTable } from "@/components/custom-table";
import { Icon } from "@/components/icon";
import { imagePath } from "@/lib/api";
import { <PERSON><PERSON>, <PERSON> } from "@nextui-org/react";
import { Session } from "next-auth";
import Image from "next/image";
import Link from "next/link";
import TaskStatus from "./status";
import toast from "react-hot-toast";
import { deleteTask, toggleTaskStatus } from "@/actions/tasks";
import TaskCreateForm from "./create";

export default function TaskTable({
  data,
  meta,
  session,
}: {
  data: Task[];
  meta?: PaginationMeta;
  session: Session | null;
}) {
  const onDeleteSubmit = async (id: string) => {
    const formData = new FormData();
    formData.append("id", id);

    toast.promise(deleteTask(formData), {
      loading: "Deleting task...",
      success: "Task deleted!",
      error: "Failed to delete task",
    });
  };



  return (
    <CustomTable
      title="tasks"
      columns={[
        {
          name: "Task",
          uid: "service",
          sortable: true,
          renderCell: (task: Task) => {
            return (
              <Link
                href={`/tasks/${task.id}`}
                className="flex items-center space-x-3"
              >
                <div className="rounded-md border p-1">
                  <Image
                    src={imagePath(task.image?.url, null)}
                    alt={task.name}
                    width={30}
                    height={30}
                  />
                </div>
                <div>
                  <p className="text-sm font-bold">{task.name}</p>
                  <p className="text-xs text-gray-600">{task.details}</p>
                </div>
              </Link>
            );
          },
        },
        {
          name: "Slug",
          uid: "slug",
          sortable: true,
          renderCell: (task) => (
            <Chip
              variant="flat"
              color="default"
              classNames={{
                base: "bg-default-50 text-xs",
              }}
            >
              {task.slug}
            </Chip>
          ),
        },
        {
          name: "Active",
          uid: "active",
          renderCell: (task) => (
            <TaskStatus task={task} toggleTaskStatus={toggleTaskStatus} />
          ),
        },
        {
          name: "Actions",
          uid: "actions",
          renderCell: (task: Task) => (
            <div className="flex w-fit items-center gap-5">
              {session?.branch ? (
                <Button as={Link} href={`/tasks/${task.id}`} className="w-fit ">
                  <Icon
                    name="icon-[mage--edit-pen-fill]"
                    classNames="text-primary"
                  />
                </Button>
              ) : (
                <>
                  <Link href={`/tasks/${task.id}/edit`}>
                    <Icon
                      name="icon-[mage--edit-pen-fill]"
                      classNames="text-primary"
                    />
                  </Link>
                  <form
                    onSubmit={(e) => {
                      e.preventDefault();
                      onDeleteSubmit(task.id);
                    }}
                  >
                    <button
                      name="id"
                      value={task.id}
                      type="submit"
                      className="text-red-500"
                    >
                      <Icon name="icon-[mingcute--delete-2-line]" />
                    </button>
                  </form>
                </>
              )}
            </div>
          ),
        },
      ]}
      data={data}
      meta={meta}
      filter={{
        column: "active",
        values: [
          { name: "Active", value: "true" },
          { name: "Inactive", value: "false" },
        ],
      }}
      action={session?.vendor?.id ? null : <TaskCreateForm />}
    />
  );
}
