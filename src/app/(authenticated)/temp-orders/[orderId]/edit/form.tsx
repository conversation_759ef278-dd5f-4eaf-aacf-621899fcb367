"use client";

import { Controller, useForm } from "react-hook-form";
import toast from "react-hot-toast";
import Select from "react-select";
import { useRouter } from "next/navigation";

export default function OrderForm({
  updateOrder,
  defaultValues,
}: {
  updateOrder: (data: Order) => Promise<void>;
  defaultValues: Order;
}) {
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    control,
    formState: { errors, isSubmitting },
  } = useForm({ defaultValues });

  const order = watch();
  const router = useRouter(); // Get the router for navigation

  const onSubmit = async (data: Order) => {
    // Show toast notification and update order
    toast.promise(updateOrder(data), {
      loading: "Updating order...",
      success: "Order updated successfully! 👌",
      error: "Error updating order 🤯",
    }).then(() => {
      // Redirect to /orders page after successful update
      router.push("/orders");
    });
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="grid grid-cols-2 gap-5">
      <input
        type="text"
        placeholder="Staff ID"
        {...register("staffId")}
        className="w-full rounded-lg border-2 border-primary-500 px-5 py-3"
      />

      {/* Status dropdown */}
      <Controller
        name="status"
        control={control}
        render={({ field }) => (
          <Select
            options={[
              { value: "Pending", label: "Pending" },
              { value: "Processing", label: "Processing" },
              { value: "Delivering", label: "Delivering" },
              { value: "Delivered", label: "Delivered" },
              { value: "Completed", label: "Completed" },
              { value: "Cancelled", label: "Cancelled" },
            ]}
            onChange={(v) => setValue("status", v?.value!)}
            defaultValue={{
              value: defaultValues.status,
              label: defaultValues.status,
            }}
            isSearchable
            placeholder="Select a status"
            classNames={{
              control: () =>
                "!border !border-primary-500 py-[6px] px-2 !rounded-lg w-full",
            }}
          />
        )}
      />

      <input
        type="text"
        placeholder="Ref"
        {...register("ref")}
        className="w-full rounded-lg border-2 border-primary-500 px-5 py-3"
      />

      <input
        type="datetime-local"
        placeholder="Accepted At"
        {...register("acceptedAt")}
        className="w-full rounded-lg border-2 border-primary-500 px-5 py-3"
      />

      <div></div>

      <button
        type="submit"
        disabled={isSubmitting}
        className="rounded-lg bg-primary px-5 py-3 text-white"
      >
        {isSubmitting ? "Updating..." : "Update Order"}
      </button>
    </form>
  );
}
