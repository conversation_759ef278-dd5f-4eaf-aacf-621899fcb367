import { api } from "@/lib/api";
import { cache } from "react";
import OrderForm from "./form";
import { revalidatePath } from "next/cache";
import BackBtn from "@/components/back";

// Fetch the order details
const fetchOrder = cache((id: string) => api.get<Order>(`temp-orders/${id}`));

export const metadata = {
  title: "Edit Order",
  description: "Edit an existing order",
};


export default async function page({ params }: { params: { orderId: string } }) {
  const order = await fetchOrder(params.orderId);

  // Update function to save order changes
  const updateOrder = async (order: Order) => {
    "use server";
    await api.put(`temp-orders/${order.id}`, order);
    revalidatePath(`/orders/${order.id}/edit`);
  };

  return (
    <div className="p-6">
      <BackBtn />

      {/* Render form for editing order */}
      {order && <OrderForm defaultValues={order} updateOrder={updateOrder} />}
    </div>
  );
}
