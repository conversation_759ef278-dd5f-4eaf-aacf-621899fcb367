"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import toast from "react-hot-toast";

interface FormDataType {
  vendorId?: string;
  branchId: string;
  sectionId: string;
  lotId: string;
  staffId?: string;
  action: string;
  type: string;
  delivery: string;
  status: string;
  ref: string;
  meta?: string;
  item1: string;
  item2: string;
}

// Sample data for the dropdown options
const branchOptions = [
  { label: "Branch 1", value: "01hvstmk20bsjxm07w0s1q769d" },
  { label: "Branch 2", value: "01hvstmk20bsjxm07w0s1q769d" },
  { label: "Branch 3", value: "01hvstmk20bsjxm07w0s1q769d" },
];

const sectionOptions = [
  { label: "Section 1", value: "01j0ww5kvzgq1nkj5432wdbq21" },
  { label: "Section 2", value: "01j0ww5kvzgq1nkj5432wdbq21" },
  { label: "Section 3", value: "01j0ww5kvzgq1nkj5432wdbq21" },
];

const lotOptions = [
  { label: "Lot 1", value: "01j0ww6mzhp00313sq78pxhpk1" },
  { label: "Lot 2", value: "01j0ww6mzhp00313sq78pxhpk1" },
  { label: "Lot 3", value: "01j0ww6mzhp00313sq78pxhpk1" },
];

export default function OrderCreateForm({
  storeOrder,
  loggedInUser,
}: {
  storeOrder: (data: FormData) => void;
  loggedInUser: { id: string; role: string; vendorId: string };
}) {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<FormDataType>();

  const [step, setStep] = useState(1);

  const nextStep = () => setStep((prev) => prev + 1);
  const prevStep = () => setStep((prev) => prev - 1);

  const onSubmit = async (data: FormDataType) => {
    const formData = new FormData();
    formData.append(
      "vendorId",
      loggedInUser.role === "vendor" ? loggedInUser.vendorId : data.vendorId || ""
    );
    formData.append("branchId", data.branchId);
    formData.append("sectionId", data.sectionId);
    formData.append("lotId", data.lotId);
    formData.append("userId", loggedInUser.id);
    if (data.staffId) formData.append("staffId", data.staffId);
    formData.append("action", data.action);
    formData.append("type", data.type);
    formData.append("delivery", data.delivery);
    formData.append("status", data.status);
    formData.append("ref", data.ref);
    formData.append("meta", data.meta || "{}");
    formData.append("item1", data.item1);
    formData.append("item2", data.item2);

    // Show toast while storing order
    toast.promise(
      // Ensure that `storeOrder` returns a promise by wrapping it in an `async` function
      (async () => await storeOrder(formData))(),
      {
        loading: "Creating order...",
        success: "Order created successfully 👌",
        error: "Could not create order 🤯",
      }
    );
  }; 
  
  
  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Stepper Indicators */}
      <div className="flex items-center justify-center mb-8">
        <div className="flex items-center space-x-4">
          <div
            className={`w-8 h-8 flex items-center justify-center rounded-full ${
              step >= 1 ? "bg-primary text-white" : "bg-default-200 text-default-600"
            }`}
          >
            1
          </div>
          <div
            className={`flex-auto border-t-2 transition duration-500 ease-in-out ${
              step >= 2 ? "border-primary" : "border-default-200"
            }`}
          ></div>
          <div
            className={`w-8 h-8 flex items-center justify-center rounded-full ${
              step >= 2 ? "bg-primary text-white" : "bg-default-200 text-default-600"
            }`}
          >
            2
          </div>
          <div
            className={`flex-auto border-t-2 transition duration-500 ease-in-out ${
              step >= 3 ? "border-primary" : "border-default-200"
            }`}
          ></div>
          <div
            className={`w-8 h-8 flex items-center justify-center rounded-full ${
              step >= 3 ? "bg-primary text-white" : "bg-default-200 text-default-600"
            }`}
          >
            3
          </div>
        </div>
      </div>

      {/* Step 1: Customer Info */}
      {step === 1 && (
        <div>
          <div className="grid grid-cols-1 gap-6">
            {loggedInUser.role !== "vendor" && (
              <div>
                <input
                  type="text"
                  placeholder="Vendor ID"
                  {...register("vendorId", { required: true })}
                  className="rounded-lg border border-primary px-5 py-2 w-full"
                />
                {errors.vendorId && (
                  <p className="text-red-500 text-sm mt-1">Vendor ID is required</p>
                )}
              </div>
            )}

            {/* Select for Branch ID */}
            <div>
              <label className="block text-gray-700">Branch</label>
              <select
                {...register("branchId", { required: true })}
                className="rounded-lg border border-primary px-5 py-2 w-full"
              >
                {branchOptions.map((branch) => (
                  <option key={branch.value} value={branch.value}>
                    {branch.label}
                  </option>
                ))}
              </select>
              {errors.branchId && (
                <p className="text-red-500 text-sm mt-1">Branch ID is required</p>
              )}
            </div>

            {/* Select for Section ID */}
            <div>
              <label className="block text-gray-700">Section</label>
              <select
                {...register("sectionId", { required: true })}
                className="rounded-lg border border-primary px-5 py-2 w-full"
              >
                {sectionOptions.map((section) => (
                  <option key={section.value} value={section.value}>
                    {section.label}
                  </option>
                ))}
              </select>
              {errors.sectionId && (
                <p className="text-red-500 text-sm mt-1">Section ID is required</p>
              )}
            </div>

            {/* Select for Lot ID */}
            <div>
              <label className="block text-gray-700">Lot</label>
              <select
                {...register("lotId", { required: true })}
                className="rounded-lg border border-primary px-5 py-2 w-full"
              >
                {lotOptions.map((lot) => (
                  <option key={lot.value} value={lot.value}>
                    {lot.label}
                  </option>
                ))}
              </select>
              {errors.lotId && (
                <p className="text-red-500 text-sm mt-1">Lot ID is required</p>
              )}
            </div>
          </div>

          <div className="flex justify-end mt-6">
            <button
              type="button"
              className="bg-primary text-white px-5 py-2 rounded-lg hover:bg-primary-dark"
              onClick={nextStep}
            >
              Next
            </button>
          </div>
        </div>
      )}

      {/* Step 2: Items Info */}
      {step === 2 && (
        <div>
          <div className="grid grid-cols-1 gap-6">
            <div>
              <input
                type="text"
                placeholder="Item 1"
                {...register("item1", { required: true })}
                className="rounded-lg border border-primary px-5 py-2 w-full"
              />
              {errors.item1 && (
                <p className="text-red-500 text-sm mt-1">Item 1 is required</p>
              )}
            </div>

            <div>
              <input
                type="text"
                placeholder="Item 2"
                {...register("item2", { required: true })}
                className="rounded-lg border border-primary px-5 py-2 w-full"
              />
              {errors.item2 && (
                <p className="text-red-500 text-sm mt-1">Item 2 is required</p>
              )}
            </div>
          </div>

          <div className="flex justify-between mt-6">
            <button
              type="button"
              className="bg-default-200 text-default-800 px-5 py-2 rounded-lg hover:bg-default-300"
              onClick={prevStep}
            >
              Previous
            </button>
            <button
              type="button"
              className="bg-primary text-white px-5 py-2 rounded-lg hover:bg-primary-dark"
              onClick={nextStep}
            >
              Next
            </button>
          </div>
        </div>
      )}

      {/* Step 3: Invoice and Payment Info */}
      {step === 3 && (
        <div>
          <div className="grid grid-cols-1 gap-6">
            <div>
              <label className="block text-gray-700">Action</label>
              <select
                {...register("action", { required: true })}
                className="rounded-lg border border-primary px-5 py-2 w-full"
              >
                <option value="Purchase">Purchase</option>
                <option value="Booking">Booking</option>
                <option value="Registration">Registration</option>
                <option value="Access">Access</option>
                <option value="Process">Process</option>
              </select>
              {errors.action && (
                <p className="text-red-500 text-sm mt-1">Action is required</p>
              )}
            </div>

            <div>
              <label className="block text-gray-700">Type</label>
              <select
                {...register("type", { required: true })}
                className="rounded-lg border border-primary px-5 py-2 w-full"
              >
                <option value="Preorder">Preorder</option>
                <option value="Instant">Instant</option>
              </select>
              {errors.type && (
                <p className="text-red-500 text-sm mt-1">Type is required</p>
              )}
            </div>

            <div>
              <label className="block text-gray-700">Delivery</label>
              <select
                {...register("delivery", { required: true })}
                className="rounded-lg border border-primary px-5 py-2 w-full"
              >
                <option value="Takeaway">Takeaway</option>
                <option value="Dinein">Dine-in</option>
                <option value="Delivery">Delivery</option>
                <option value="Selfpick">Self-pick</option>
              </select>
              {errors.delivery && (
                <p className="text-red-500 text-sm mt-1">Delivery method is required</p>
              )}
            </div>

            <div>
              <label className="block text-gray-700">Status</label>
              <select
                {...register("status", { required: true })}
                className="rounded-lg border border-primary px-5 py-2 w-full"
              >
                <option value="Pending">Pending</option>
                <option value="Placed">Placed</option>
                <option value="Processing">Processing</option>
                <option value="Delivering">Delivering</option>
                <option value="Delivered">Delivered</option>
                <option value="Completed">Completed</option>
                <option value="Cancelled">Cancelled</option>
              </select>
              {errors.status && (
                <p className="text-red-500 text-sm mt-1">Status is required</p>
              )}
            </div>

            <div>
              <input
                type="text"
                placeholder="Reference"
                {...register("ref", { required: true })}
                className="rounded-lg border border-primary px-5 py-2 w-full"
              />
              {errors.ref && (
                <p className="text-red-500 text-sm mt-1">Reference is required</p>
              )}
            </div>

            <div>
              <textarea
                placeholder="Meta data (optional)"
                {...register("meta")}
                className="rounded-lg border border-primary px-5 py-2 w-full"
                rows={3}
              ></textarea>
            </div>
          </div>

          <div className="flex justify-between mt-6">
            <button
              type="button"
              className="bg-default-200 text-default-800 px-5 py-2 rounded-lg hover:bg-default-300"
              onClick={prevStep}
            >
              Previous
            </button>
            <button
              type="submit"
              className="bg-primary text-white px-5 py-2 rounded-lg hover:bg-primary-dark"
            >
              Submit
            </button>
          </div>
        </div>
      )}
    </form>
  );
}
