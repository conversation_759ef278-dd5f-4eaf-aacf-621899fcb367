import OrderCreateForm from "./form";
import { api } from "@/lib/api";
import { auth } from "@/auth";
import BackBtn from "@/components/back";
import toast from "react-hot-toast";

export const metadata = {
  title: "Create Order",
  description: "Create a new order",
};

export default async function Page() {
  // Fetching the authenticated session
  const session = await auth();

  // Define loggedInUser based on session availability
  const loggedInUser = {
    id: "01j6rsfv2r375nss3533bp8wh2", // Hardcoded user ID
    role: "vendor", // Hardcoded role
    vendorId: "01hvstmhh5z859rf278r0b2ee8", // Hardcoded vendor ID
  };

  // Function to store order data
  const storeOrder = async (data: FormData) => {
    "use server";

    // Parse the meta field to JSON object if it's a valid JSON string, else keep it as an empty object
    let meta;
    try {
      meta = JSON.parse(data.get("meta") as string) || {};
    } catch (error) {
      meta = {};
    }

    // Converting the FormData to an object to send as JSON
    const orderData = {
      vendorId: data.get("vendorId"),
      branchId: data.get("branchId"),
      sectionId: data.get("sectionId"),
      lotId: data.get("lotId"),
      userId: loggedInUser.id,
      staffId: data.get("staffId"),
      action: data.get("action"),
      type: data.get("type"),
      delivery: data.get("delivery"),
      status: data.get("status"),
      ref: data.get("ref"),
      meta, // Use the parsed meta as an object
      items: [data.get("item1"), data.get("item2")],
    };

    try {
      // Using the api client to post the order data
      return await api.post("temp-orders", orderData);
    } catch (error) {
      console.error("Error submitting the order:", error);
      throw error;
    }


  };

  return (
    <div className="p-4">
      <div className="relative my-10">
        <div className="mx-20 -mb-6 hidden md:flex">
          <div className="flex h-1.5 w-full overflow-hidden rounded-full bg-default-200">
            <div className="flex w-1/2 flex-col justify-center overflow-hidden rounded-full bg-primary"></div>
          </div>
        </div>
        <div className="absolute inset-y-0 start-1/2 flex -translate-x-1/2 md:hidden">
          <div className="absolute inset-y-0 start-1/2 flex h-full w-1.5 -translate-x-1/2 overflow-hidden rounded-full bg-default-200">
            <div className="absolute bottom-1/2 start-1/2 top-0 flex w-1.5 -translate-x-1/2 flex-col justify-center overflow-hidden rounded-full bg-primary"></div>
          </div>
        </div>

        <OrderCreateForm storeOrder={storeOrder} loggedInUser={loggedInUser} />
      </div>
    </div>
  );
}
