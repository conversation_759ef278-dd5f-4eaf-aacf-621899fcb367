"use client";

import { usePathname } from "next/navigation";
import React from "react";

export default function BackToTop() {
  const pathname = usePathname();

  const toggleTheme = () => {
    const isDark = document.documentElement.classList.contains("dark");

    if (isDark) {
      document.documentElement.classList.remove("dark");
    } else {
      document.documentElement.classList.add("dark");
    }
  };

  return (
    <div className="bottom-18 fixed end-5 z-10 flex flex-col items-center rounded-full bg-primary/25 lg:bottom-5">
      <button className="z-10 flex h-0 w-8 translate-y-5 items-center justify-center opacity-0 transition-all duration-500">
        <svg
          stroke="currentColor"
          fill="none"
          strokeWidth="2"
          viewBox="0 0 24 24"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="mt-1 text-default-500"
          height="20"
          width="20"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path d="m18 15-6-6-6 6"></path>
        </svg>
      </button>

      {pathname === "/" && (
        <button
          onClick={toggleTheme}
          className="z-20 flex size-10 items-center justify-center rounded-full bg-primary text-white"
        >
          <svg
            stroke="currentColor"
            fill="none"
            strokeWidth="2"
            viewBox="0 0 24 24"
            strokeLinecap="round"
            strokeLinejoin="round"
            height="20"
            width="20"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z"></path>
          </svg>
        </button>
      )}
    </div>
  );
}
