// DeleteButton.tsx
"use client";

import { useRouter } from "next/navigation";
import { api } from "@/lib/api";
import { Icon } from "@/components/icon";

interface DeleteButtonProps {
  userId: string;
  branchId: string;
}

export default function DeleteButton({ userId, branchId }: DeleteButtonProps) {
  const router = useRouter();

  const handleDelete = async () => {
    try {
      const url = `branches/${branchId}/staff/${userId}`;
      const response = await api.delete(url);

      if (response.ok) {
        router.refresh(); // Refresh the page to update the user list
      } else {
        console.error("Failed to delete user:", response.statusText);
      }
    } catch (error) {
      console.error("Error deleting user:", error);
    }
  };

  return (
    <button
      onClick={handleDelete}
      type="button"
      className="text-red-500 hover:underline"
    >
      <Icon name="icon-[mingcute--delete-2-line]" />
    </button>
  );
}
