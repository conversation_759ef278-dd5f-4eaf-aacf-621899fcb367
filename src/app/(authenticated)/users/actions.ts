// src/app/(authenticated)/users/actions.ts
"use server";

import { api } from "@/lib/api";
import { revalidatePath } from "next/cache";
import { cache } from "react";

export async function updateStatus(payload: {
  identifier: string;
  userId: string;
  vendorId?: string;
  online: boolean;
}, sessionBranchId: string | undefined, searchParamsRole: string | undefined) {
  await api.put(`branches/${sessionBranchId}/staff/${payload.userId}`, payload);

  revalidatePath(`users?role=${searchParamsRole}`);
}