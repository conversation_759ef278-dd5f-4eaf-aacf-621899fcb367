"use client";
import { auth } from "@/auth";
import { api } from "@/lib/api";
import axios from "axios";
import { Session } from "next-auth";
import { useSearchParams } from "next/navigation";
import router from "next/router";
import { useEffect, useState } from "react";
import { useForm, Controller } from "react-hook-form";
import toast from "react-hot-toast";
import PhoneInput from "react-phone-number-input";

const fetchRoles = async ():Promise<Role[]> => {
  const res = await api.get<PaginatedData<Role>>(`/roles`)
  return (res && res.data)? res.data: []
};


export default function UserCreateForm({
  createUser,
}: {
  createUser: (data: Partial<User>) => Promise<void>;
}) {
  const searchParams = useSearchParams();

  const {
    register,
    handleSubmit,
    reset,
    watch,
    control,
    formState: { errors },
  } = useForm<Partial<User & { role: string; identifier?: string }>>({
    defaultValues: {
      role: searchParams.get("role") || "customer",
    },
  });

  const user = watch();
  const [loading, setLoading] = useState(true);
  let roles:Role[] = []
  fetchRoles().then(res=> roles = res)

  const onSubmit = (
    data: Partial<User & { role: string; identifier?: string }>,
  ) => {
    toast
      .promise(createUser(data), {
        loading: "Creating user...",
        success: "User created!",
        error: "Error creating user",
      })
      .then(() => {
        reset();
        // Redirect to the /users page
        router.push("/users");
      });
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4 bg-white p-6 rounded-lg h-full min-h-screen">
      <div className="mb-6 grid w-full gap-6 md:grid-cols-2">
        <div>
          <label
            htmlFor="firstName"
            className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
          >
            First name
          </label>
          <input
            type="text"
            {...register("firstName", {
              required: true,
            })}
            className="block w-full rounded-lg border border-primary bg-default-50 p-3 text-sm text-default-900 focus:border-primary focus:ring-primary dark:border-default-600 dark:bg-primary dark:text-white dark:placeholder-default-400 dark:focus:border-primary dark:focus:ring-primary"
            placeholder="John"
            required
          />
        </div>
        <div>
          <label
            htmlFor="lastName"
            className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
          >
            Last name
          </label>
          <input
            type="text"
            {...register("lastName", {
              required: true,
            })}
            className="block w-full rounded-lg border border-primary bg-default-50 p-3 text-sm text-default-900 focus:border-primary focus:ring-primary dark:border-default-600 dark:bg-primary dark:text-white dark:placeholder-default-400 dark:focus:border-primary dark:focus:ring-primary"
            placeholder="Doe"
            required
          />
        </div>

        <div>
          <label
            htmlFor="firstName"
            className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
          >
            Phone Number
          </label>
          <div className="form-input relative w-full max-w-full rounded-lg border border-default-200 bg-white px-4 py-3 dark:bg-default-50">
            <Controller
              name="phone"
              control={control}
              render={({ field: { onChange, value } }) => (
                <PhoneInput
                  placeholder="Enter phone number"
                  value={value}
                  onChange={onChange}
                  defaultCountry="KE"
                />
              )}
            />
          </div>
        </div>

        <div>
          <label
            htmlFor="idpass"
            className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
          >
            ID/Passport Number
          </label>
          <input
            type="text"
            {...register("idpass", {
              required: true,
            })}
            id="idpass"
            className="block w-full rounded-lg border border-primary bg-default-50 p-3 text-sm text-default-900 focus:border-primary focus:ring-primary dark:border-default-600 dark:bg-primary dark:text-white dark:placeholder-default-400 dark:focus:border-primary dark:focus:ring-primary"
            placeholder="1234567"
            required
          />
        </div>

        <div>
          <label
            htmlFor="gender"
            className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
          >
            Gender
          </label>
          <select
            {...register("gender", {
              required: true,
            })}
            id="gender"
            className="block w-full rounded-lg border border-primary bg-default-50 p-3 text-sm text-default-900 focus:border-primary focus:ring-primary dark:border-default-600 dark:bg-primary dark:text-white dark:placeholder-default-400 dark:focus:border-primary dark:focus:ring-primary"
            required
          >
            <option value="" disabled selected>
              Gender
            </option>
            <option value="Male">Male</option>
            <option value="Female">Female</option>
            <option value="Other">Other</option>
          </select>
        </div>
        <div>
          <label
            htmlFor="dob"
            className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
          >
            Date of Birth
          </label>
          <input
            type="date"
            {...register("dob", {
              required: true,
            })}
            id="dob"
            className="block w-full rounded-lg border border-primary bg-default-50 p-3 text-sm text-default-900 focus:border-primary focus:ring-primary dark:border-default-600 dark:bg-primary dark:text-white dark:placeholder-default-400 dark:focus:border-primary dark:focus:ring-primary"
            required
          />
        </div>
        <div>
          <label
            htmlFor="roles"
            className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
          >
            Select the role
          </label>
          <select
            {...register("role", {
              required: true,
            })}
            id="roles"
            className="block w-full rounded-lg border border-primary bg-default-50 p-3 text-sm text-default-900 focus:border-primary focus:ring-primary dark:border-default-600 dark:bg-primary dark:text-white dark:placeholder-default-400 dark:focus:border-primary dark:focus:ring-primary"
            required
          >
            <option value="" disabled selected>
              Choose the role
            </option>
            {roles.map(role => <option key={role.id} value={role.id}>{role.name}</option>)}
          </select>
          {/* <option value="admin">Admin</option> */}
          {/* <option value="vendor">Vendor</option> */}
          {/* <option value="employee">Employee</option> */}
          {/* <option value="accounts">Accounts</option> */}
          {/* <option value="customer">Customer</option> */}
          {/* <option value="student">Student</option> */}
          {/* <option value="unknown">Unknown</option> */}
          {/* <option value="manager">Manager</option> */}


          {/* <option value="rider">Rider</option>
            <option value="waiter">Waiter</option>
            <option value="cashier">Cashier</option>
            <option value="bartender">Bartender</option>
            <option value="chef">Chef</option>
            <option value="staff">Staff</option>
            <option value="barister">Barister</option> */}

          {/* <select
      {...register("role", {
        required: true,
      })}
      id="roles"
      className="block w-full rounded-lg border border-primary bg-default-50 p-3 text-sm text-default-900 focus:border-primary focus:ring-primary dark:border-default-600 dark:bg-primary dark:text-white dark:placeholder-default-400 dark:focus:border-primary dark:focus:ring-primary"
      required
    >
      <option value="" disabled selected>
        {loading ? "Loading roles..." : "Choose the role"}
      </option>
      {roles.map((role, index) => (
        <option key={index} value={role}>
          {role}
        </option>
      ))}
    </select> */}
        </div>

        <div>
          <label
            htmlFor="email"
            className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
          >
            Email
          </label>
          <input
            type="email"
            {...register("email", {
              required: true,
            })}
            id="email"
            className="block w-full rounded-lg border border-primary bg-default-50 p-3 text-sm text-default-900 focus:border-primary focus:ring-primary dark:border-default-600 dark:bg-primary dark:text-white dark:placeholder-default-400 dark:focus:border-primary dark:focus:ring-primary"
            placeholder="<EMAIL>"
            required
          />
        </div>
      </div>
      <div>
        <label
          htmlFor="details"
          className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
        >
          Bio
        </label>
        <textarea
          {...register("details")}
          id="details"
          className="block w-full rounded-lg border border-primary bg-default-50 p-3 text-sm text-default-900 focus:border-primary focus:ring-primary dark:border-default-600 dark:bg-primary dark:text-white dark:placeholder-default-400 dark:focus:border-primary dark:focus:ring-primary"
          placeholder="User details"
          rows={5}
        />
      </div>

      <div className="flex justify-between">
        <div>
          <>
            {/* identifier */}
            <label
              htmlFor="identifier"
              className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
            >
              Staff ID
            </label>
            <input
              type="text"
              {...register("identifier", {
                required: true,
              })}
              id="identifier"
              className="block w-full rounded-lg border border-primary bg-default-50 p-3 text-sm text-default-900 focus:border-primary focus:ring-primary dark:border-default-600 dark:bg-primary dark:text-white dark:placeholder-default-400 dark:focus:border-primary dark:focus:ring-primary"
              placeholder="Staff ID"
              required
            />
          </>
        </div>

        <button
          type="submit"
          className="h-14 w-full rounded-lg bg-primary px-14 text-center text-sm font-medium text-white hover:bg-default-800 focus:outline-none focus:ring-4 focus:ring-blue-300 dark:bg-default-600 dark:hover:bg-primary dark:focus:ring-primary sm:w-auto"
        >
          Create {searchParams.get("role") || "customer"}
        </button>
      </div>
    </form>
  );
}
