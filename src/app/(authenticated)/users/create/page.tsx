import { api } from "@/lib/api";
import UserCreateForm from "./form";
import { auth } from "@/auth";
import { Metadata } from "next";


interface CreateUserPayload {
  role: string
  firstName: string
  lastName: string
  idpass: string
  gender: string
  dob: string
  email: string
  details: string
  identifier: string
  phone: string
  vendorId: string
}

export const generateMetadata = async ({
  searchParams,
}: {
  searchParams?: Record<string, string>;
}): Promise<Metadata> => {
  return {
    title: `Create ${searchParams?.role || "customer"}`,
    description: "Create a new user",
  };
};

export default async function page({
  searchParams,
}: {
  searchParams?: Record<string, string>;
}) {
  const session = await auth();



  const createUser = async (data: Partial<User & { vendorId?: string }>) => {
    "use server";

    if (session?.vendor) {
      data.vendorId = session.vendor.id;
    }

    const path = searchParams?.role === "staff" ? "staff" : "customers";

    const payload: CreateUserPayload = {
      ...data,
      phone: (data.phone as string).slice(1, (data.phone as string).length)
    } as CreateUserPayload


    if (session?.branch?.id) {
      await api.post(`branches/${session?.branch?.id}/${path}`, payload);
    } else {
      await api.post<{ account: any }>("users", data);
    }
  };


  return (
    <div className="p-4">
      <UserCreateForm createUser={createUser} />
    </div>
  );
}
