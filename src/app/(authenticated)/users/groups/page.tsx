import PaginatedTable from "@/components/table";
import { api } from "@/lib/api";

import { revalidatePath } from "next/cache";
import AdminCustomerGroupCreateForm from "./create";
import { auth } from "@/auth";

export const generateMetadata = async () => {
  return {
    title: "Customer groups",
  };
};

export default async function CustomerGroups() {
  const session = await auth();
  const groups = await api.get<PaginatedData<CustomerGroup>>(
    `branches/${session?.branch?.id}/groups`,
  );

  const storeGroup = async (data: FormData) => {
    "use server";

    await api.post<CustomerGroup>(
      `/branches/${session?.branch?.id}/groups`,
      data,
    );

    revalidatePath(`/users/groups`);
  };

  return (
    <>
      <PaginatedTable<CustomerGroup>
        records={groups!}
        columns={[{ id: "name", title: "Name" }]}
        title="Customer groups"
        path="/users/groups"
        tools={
          <>
            <AdminCustomerGroupCreateForm storeGroup={storeGroup} />
          </>
        }
      />
    </>
  );
}
