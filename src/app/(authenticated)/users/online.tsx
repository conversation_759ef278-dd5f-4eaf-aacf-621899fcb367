"use client";

import toast from "react-hot-toast";

export default function OnlineSwitch({
  user,
  updateStatus,
}: {
  user: User;
  updateStatus: (payload: {
    identifier: string;
    userId: string;
    vendorId?: string;
    online: boolean;
  }) => Promise<void>;
}) {
  const onChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    toast.promise(
      updateStatus({
        identifier: user.identifier,
        userId: user.id,
        vendorId: user.vendorId,
        online: e.target.checked,
      }),
      {
        loading: "Updating status...",
        success: "Status updated",
        error: "Failed to update status",
      },
    );
  };

  return (
    <label
      htmlFor={user.id}
      className="relative inline-flex cursor-pointer items-center"
    >
      <input
        type="checkbox"
        id={user.id}
        className="peer sr-only"
        onChange={onChange}
        defaultChecked={user.online}
      />

      <div className="peer h-6 w-11 rounded-full bg-default-200 after:absolute after:left-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-default-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-primary peer-checked:after:translate-x-full peer-checked:after:border-white peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:border-default-600 dark:bg-default-700 dark:peer-focus:ring-primary"></div>
      <span className="ml-3 text-sm font-medium capitalize text-default-900 dark:text-default-300">
        {user.online ? "online" : "sit in"}
      </span>
    </label>
  );
}
