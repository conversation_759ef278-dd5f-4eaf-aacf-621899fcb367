import { api } from "@/lib/api";
import StaffTable from "@/components/tables/staff-table";
import { auth } from "@/auth";
import { deleteStaff, fetchStaff, fetchUserRoles } from "@/actions/users";
import { revalidatePath } from "next/cache";

export default async function Page({
  searchParams,
  params
  }: {
    searchParams?: Record<string, string>;
    params:{branchId:string}
}) {

  let session = await auth()
const sessionRoles: Role[] = await fetchUserRoles(session?.user.id as string)
let staff:User[];
let meta:any;
    switch(true){
      case sessionRoles.map(role=> role.name.toLowerCase()).includes('vendor'):
        let vendorRes = await fetchStaff({vendorId:session?.vendor.id as string, searchParams:searchParams as Record<string, string> || {per: "20"}});
        staff = vendorRes?.data.filter(rec=> !(rec.roles.length <=1 && rec.roles.map(role=> role.name).includes("customer"))) as User[]
        meta = vendorRes?.meta
        break;
      case !sessionRoles.map(role=> role.name.toLowerCase()).includes("vendor"):
        let staffRes = await fetchStaff({branchId:session?.branch.id as string, searchParams:searchParams as Record<string, string> || {per: "20"}});
        staff = staffRes?.data.filter(rec=> !rec.roles.map(role=> role.name).includes("customer")) as User[]
        meta = staffRes?.meta
        break;
      default:
        let defaultRes = await fetchStaff({branchId:session?.branch.id as string, searchParams:searchParams as Record<string, string> || {per: "20"}});
        staff = defaultRes?.data.filter(rec=> !rec.roles.map(role=> role.name).includes("customer")) as User[]
        meta = defaultRes?.meta
        break;
    }

    const deleteUser = async(data:FormData) =>{
      "use server";
  
      try {
        await deleteStaff(data)
        revalidatePath(`/staff`)
        return true
      } catch (error) {
        console.log("ERROR DELETING USER:(branch/staff/details)", error)
        return false
      }
    }
  return (
    <div className="page-content p-5">
  
    {staff ? (
      <StaffTable
        data={staff}
        meta={meta}
        params={params}
        deleteStaff={deleteUser}
      />
    ):<div><h1>No Staff to show</h1></div>}
  </div>
  )
}
