import Image from "next/image";

export default function page() {
  return (
    <div className="page-content space-y-6 p-6">
      <div className="flex w-full items-center justify-between">
        <h4 className="text-xl font-medium">Healthy Feast Corner</h4>
        <ol
          aria-label="Breadcrumb"
          className="hidden min-w-0 items-center gap-2 whitespace-nowrap md:flex"
        >
          <li className="text-sm">
            <a
              className="flex items-center gap-2 align-middle text-default-800 transition-all hover:text-default-500"
              href="/yum_r/admin/restaurants"
            >
              Restaurants
              <svg
                stroke="currentColor"
                fill="none"
                strokeWidth="2"
                viewBox="0 0 24 24"
                strokeLinecap="round"
                strokeLinejoin="round"
                height="16"
                width="16"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="m9 18 6-6-6-6"></path>
              </svg>
            </a>
          </li>
          <li
            aria-current="page"
            className="truncate text-sm font-medium text-primary hover:text-default-500"
          >
            Healthy Feast Corner
          </li>
        </ol>
      </div>
      <div className="mb-6 rounded-lg border border-default-200 p-6">
        <Image
          src="/yum_r/assets/bg-d1yDRYwS.png"
          className="hidden w-full md:flex"
          alt="background"
        />
        <div className="flex items-center gap-3 md:-mt-14 md:items-end">
          <Image
            src="/yum_r/assets/1-XXvJiOSc.png"
            className="h-28 w-28 rounded-full bg-default-50"
            alt="restaurant"
          />
          <div>
            <h4 className="mb-1 text-base font-medium text-default-800">
              Healthy Feast Corner
            </h4>
            <p className="text-sm text-default-600">Since 2013</p>
          </div>
        </div>
      </div>
      <div className="grid grid-cols-1 gap-6 xl:grid-cols-3">
        <div className="xl:col-span-2">
          <div className="mb-6 rounded-lg border border-default-200 p-6">
            <div className="flex justify-between">
              <h4 className="text-xl font-medium text-default-900">
                Cost &amp; Usage by instance type
              </h4>
              <div className="hs-dropdown relative inline-flex">
                <button
                  type="button"
                  className="hs-dropdown-toggle flex items-center gap-2 rounded-md bg-default-100 px-4 py-3 text-sm font-medium text-default-700 transition-all xl:px-5"
                >
                  {" "}
                  Last Day{" "}
                  <svg
                    stroke="currentColor"
                    fill="none"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    height="16"
                    width="16"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path d="m6 9 6 6 6-6"></path>
                  </svg>
                </button>
                <div className="hs-dropdown-menu hs-dropdown-open:opacity-100 z-20 mt-4 hidden min-w-[200px] rounded-lg border border-default-100 bg-white p-1.5 opacity-0 shadow-[rgba(17,_17,_26,_0.1)_0px_0px_16px] transition-[opacity,margin] dark:bg-default-50">
                  <ul className="flex flex-col gap-1">
                    <li>
                      <span className="flex items-center gap-3 rounded bg-default-100 px-3 py-2 font-normal text-default-700 transition-all hover:bg-default-100 hover:text-default-700">
                        Last Day
                      </span>
                    </li>
                    <li>
                      <span className="flex items-center gap-3 rounded px-3 py-2 font-normal text-default-600 transition-all hover:bg-default-100 hover:text-default-700">
                        Last 6 Month
                      </span>
                    </li>
                    <li>
                      <span className="flex items-center gap-3 rounded px-3 py-2 font-normal text-default-600 transition-all hover:bg-default-100 hover:text-default-700">
                        Last Year
                      </span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
            <div className="w-100"></div>
          </div>
          <div className="overflow-hidden rounded-lg border border-default-200">
            <div className="border-b border-b-default-200 p-6">
              <h4 className="mb-4 text-xl font-medium text-default-900">
                Menu
              </h4>
              <div className="flex flex-wrap items-center gap-4">
                <div className="hs-dropdown relative inline-flex">
                  <button
                    type="button"
                    className="hs-dropdown-toggle flex items-center gap-2 rounded-md bg-default-100 px-4 py-3 text-sm font-medium text-default-700 transition-all xl:px-5"
                  >
                    Popular : Best Seller{" "}
                    <svg
                      stroke="currentColor"
                      fill="none"
                      strokeWidth="2"
                      viewBox="0 0 24 24"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      height="16"
                      width="16"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="m6 9 6 6 6-6"></path>
                    </svg>
                  </button>
                  <div className="hs-dropdown-menu hs-dropdown-open:opacity-100 z-20 mt-4 hidden min-w-[200px] rounded-lg border border-default-100 bg-white p-1.5 opacity-0 shadow-[rgba(17,_17,_26,_0.1)_0px_0px_16px] transition-[opacity,margin] dark:bg-default-50">
                    <ul className="flex flex-col gap-1">
                      <li>
                        <span className="flex items-center gap-3 rounded bg-default-100 px-3 py-2 font-normal text-default-700 transition-all hover:bg-default-100 hover:text-default-700">
                          Best Seller
                        </span>
                      </li>
                      <li>
                        <span className="flex items-center gap-3 rounded px-3 py-2 font-normal text-default-600 transition-all hover:bg-default-100 hover:text-default-700">
                          High to Low
                        </span>
                      </li>
                      <li>
                        <span className="flex items-center gap-3 rounded px-3 py-2 font-normal text-default-600 transition-all hover:bg-default-100 hover:text-default-700">
                          Low to High
                        </span>
                      </li>
                    </ul>
                  </div>
                </div>
                <div className="hs-dropdown relative inline-flex">
                  <button
                    type="button"
                    className="hs-dropdown-toggle flex items-center gap-2 rounded-md bg-default-100 px-4 py-3 text-sm font-medium text-default-700 transition-all xl:px-5"
                  >
                    Upload Date : Newest{" "}
                    <svg
                      stroke="currentColor"
                      fill="none"
                      strokeWidth="2"
                      viewBox="0 0 24 24"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      height="16"
                      width="16"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="m6 9 6 6 6-6"></path>
                    </svg>
                  </button>
                  <div className="hs-dropdown-menu hs-dropdown-open:opacity-100 z-20 mt-4 hidden min-w-[200px] rounded-lg border border-default-100 bg-white p-1.5 opacity-0 shadow-[rgba(17,_17,_26,_0.1)_0px_0px_16px] transition-[opacity,margin] dark:bg-default-50">
                    <ul className="flex flex-col gap-1">
                      <li>
                        <span className="flex items-center gap-3 rounded bg-default-100 px-3 py-2 font-normal text-default-700 transition-all hover:bg-default-100 hover:text-default-700">
                          Newest
                        </span>
                      </li>
                      <li>
                        <span className="flex items-center gap-3 rounded px-3 py-2 font-normal text-default-600 transition-all hover:bg-default-100 hover:text-default-700">
                          Oldest
                        </span>
                      </li>
                    </ul>
                  </div>
                </div>
                <div className="hs-dropdown relative inline-flex">
                  <button
                    type="button"
                    className="hs-dropdown-toggle flex items-center gap-2 rounded-md bg-default-100 px-4 py-3 text-sm font-medium text-default-700 transition-all xl:px-5"
                  >
                    Rating : Average{" "}
                    <svg
                      stroke="currentColor"
                      fill="none"
                      strokeWidth="2"
                      viewBox="0 0 24 24"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      height="16"
                      width="16"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="m6 9 6 6 6-6"></path>
                    </svg>
                  </button>
                  <div className="hs-dropdown-menu hs-dropdown-open:opacity-100 z-20 mt-4 hidden min-w-[200px] rounded-lg border border-default-100 bg-white p-1.5 opacity-0 shadow-[rgba(17,_17,_26,_0.1)_0px_0px_16px] transition-[opacity,margin] dark:bg-default-50">
                    <ul className="flex flex-col gap-1">
                      <li>
                        <span className="flex items-center gap-3 rounded bg-default-100 px-3 py-2 font-normal text-default-700 transition-all hover:bg-default-100 hover:text-default-700">
                          Average
                        </span>
                      </li>
                      <li>
                        <span className="flex items-center gap-3 rounded px-3 py-2 font-normal text-default-600 transition-all hover:bg-default-100 hover:text-default-700">
                          Good
                        </span>
                      </li>
                      <li>
                        <span className="flex items-center gap-3 rounded px-3 py-2 font-normal text-default-600 transition-all hover:bg-default-100 hover:text-default-700">
                          Best
                        </span>
                      </li>
                    </ul>
                  </div>
                </div>
                <div className="ms-auto">
                  <a
                    className="inline-flex rounded-md bg-primary px-6 py-3 text-sm text-white hover:bg-default-500 "
                    href="/yum_r/admin/add-dish"
                  >
                    <svg
                      stroke="currentColor"
                      fill="none"
                      strokeWidth="2"
                      viewBox="0 0 24 24"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="me-2 inline-flex align-middle"
                      height="20"
                      width="20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="M5 12h14"></path>
                      <path d="M12 5v14"></path>
                    </svg>
                    Add Dish
                  </a>
                </div>
              </div>
            </div>
            <div className="overflow-x-auto">
              <div className="inline-block min-w-full align-middle">
                <div className="divide-y divide-default-200 rounded-lg">
                  <div className="overflow-hidden">
                    <table className="min-w-full divide-y divide-default-200">
                      <thead className="bg-default-100/75">
                        <tr>
                          <th scope="col" className="px-4 py-3 pr-0">
                            <div className="flex h-5 items-center">
                              <input
                                id="hs-table-search-checkbox-all"
                                type="checkbox"
                                className="form-checkbox h-5 w-5 rounded border border-default-300 bg-transparent text-primary focus:border-default-300 focus:ring focus:ring-default-200 focus:ring-opacity-50 focus:ring-offset-0"
                              />
                              <label
                                htmlFor="hs-table-search-checkbox-all"
                                className="sr-only"
                              >
                                Checkbox
                              </label>
                            </div>
                          </th>
                          <th
                            scope="col"
                            className="whitespace-nowrap px-6 py-3 text-start text-base font-medium text-default-500"
                          >
                            Dish
                          </th>
                          <th
                            scope="col"
                            className="whitespace-nowrap px-6 py-3 text-start text-base font-medium text-default-500"
                          >
                            Category
                          </th>
                          <th
                            scope="col"
                            className="whitespace-nowrap px-6 py-3 text-start text-base font-medium text-default-500"
                          >
                            Price
                          </th>
                          <th
                            scope="col"
                            className="whitespace-nowrap px-6 py-3 text-start text-base font-medium text-default-500"
                          >
                            Order ID
                          </th>
                          <th
                            scope="col"
                            className="whitespace-nowrap px-6 py-3 text-start text-base font-medium text-default-500"
                          >
                            Rating
                          </th>
                          <th
                            scope="col"
                            className="whitespace-nowrap px-6 py-3 text-start text-base font-medium text-default-500"
                          >
                            Actions
                          </th>
                        </tr>
                      </thead>
                      <tbody className="divide-y divide-default-200">
                        <tr>
                          <td className="py-3 pl-4">
                            <div className="flex h-5 items-center">
                              <input
                                id="hs-table-search-checkbox-1"
                                type="checkbox"
                                className="form-checkbox h-5 w-5 rounded border border-default-300 bg-transparent text-primary focus:border-default-300 focus:ring focus:ring-default-200 focus:ring-opacity-50 focus:ring-offset-0"
                              />
                              <label
                                htmlFor="hs-table-search-checkbox-1"
                                className="sr-only"
                              >
                                Checkbox
                              </label>
                            </div>
                          </td>
                          <td className="whitespace-nowrap px-6 py-4">
                            <div className="flex items-start gap-3">
                              <Image
                                src="/yum_r/assets/pizza-04FDaPt1.png"
                                width="48"
                                height="48"
                                className="size-12 rounded-full"
                                alt="Italian Pizza"
                              />
                              <h5 className="text-base font-medium text-default-700">
                                Italian Pizza
                              </h5>
                            </div>
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-base text-default-800">
                            {" "}
                            Pizza
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-base text-default-800">
                            $ 79
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-base text-default-800">
                            {" "}
                            1001
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-base text-default-800">
                            <div className="flex items-center justify-start gap-1">
                              <svg
                                stroke="currentColor"
                                fill="currentColor"
                                strokeWidth="0"
                                viewBox="0 0 576 512"
                                className="fill-yellow-400 text-yellow-400"
                                height="18"
                                width="18"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"></path>
                              </svg>
                              <svg
                                stroke="currentColor"
                                fill="currentColor"
                                strokeWidth="0"
                                viewBox="0 0 576 512"
                                className="fill-yellow-400 text-yellow-400"
                                height="18"
                                width="18"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"></path>
                              </svg>
                              <svg
                                stroke="currentColor"
                                fill="currentColor"
                                strokeWidth="0"
                                viewBox="0 0 576 512"
                                className="fill-yellow-400 text-yellow-400"
                                height="18"
                                width="18"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"></path>
                              </svg>
                              <svg
                                stroke="currentColor"
                                fill="currentColor"
                                strokeWidth="0"
                                viewBox="0 0 576 512"
                                className="fill-yellow-400 text-yellow-400"
                                height="18"
                                width="18"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"></path>
                              </svg>
                              <svg
                                stroke="currentColor"
                                fill="currentColor"
                                strokeWidth="0"
                                viewBox="0 0 640 512"
                                className="text-yellow-400"
                                height="18"
                                width="18"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path d="M320 376.4l.1-.1 26.4 14.1 85.2 45.5-16.5-97.6-4.8-28.7 20.7-20.5 70.1-69.3-96.1-14.2-29.3-4.3-12.9-26.6L320.1 86.9l-.1 .3V376.4zm175.1 98.3c2 12-3 24.2-12.9 31.3s-23 8-33.8 2.3L320.1 439.8 191.8 508.3C181 514 167.9 513.1 158 506s-14.9-19.3-12.9-31.3L169.8 329 65.6 225.9c-8.6-8.5-11.7-21.2-7.9-32.7s13.7-19.9 25.7-21.7L227 150.3 291.4 18c5.4-11 16.5-18 28.8-18s23.4 7 28.8 18l64.3 132.3 143.6 21.2c12 1.8 22 10.2 25.7 21.7s.7 24.2-7.9 32.7L470.5 329l24.6 145.7z"></path>
                              </svg>
                            </div>
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-center">
                            <div className="hs-dropdown relative inline-flex [--placement:bottom-right]">
                              <button
                                type="button"
                                className="hs-dropdown-toggle inline-flex font-medium text-default-600 transition-all"
                              >
                                <svg
                                  stroke="currentColor"
                                  fill="none"
                                  strokeWidth="2"
                                  viewBox="0 0 24 24"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  height="22"
                                  width="22"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <circle cx="12" cy="12" r="1"></circle>
                                  <circle cx="12" cy="5" r="1"></circle>
                                  <circle cx="12" cy="19" r="1"></circle>
                                </svg>
                              </button>
                              <div className="hs-dropdown-menu hs-dropdown-open:opacity-100 z-20 mt-4 hidden min-w-[150px] rounded-lg border border-default-100 bg-white p-1.5 opacity-0 shadow-[rgba(17,_17,_26,_0.1)_0px_0px_16px] transition-[opacity,margin] dark:bg-default-50">
                                <ul className="flex flex-col gap-1">
                                  <li>
                                    <button className="flex w-full items-center gap-3 rounded px-3 py-2 font-normal text-default-600 transition-all hover:bg-default-100 hover:text-default-700">
                                      Edit
                                    </button>
                                  </li>
                                  <li>
                                    <button className="flex w-full items-center gap-3 rounded px-3 py-2 font-normal text-default-600 transition-all hover:bg-default-100 hover:text-default-700">
                                      View
                                    </button>
                                  </li>
                                  <li>
                                    <button className="flex w-full items-center gap-3 rounded px-3 py-2 font-normal text-default-600 transition-all hover:bg-default-100 hover:text-default-700">
                                      Delete
                                    </button>
                                  </li>
                                </ul>
                              </div>
                            </div>
                          </td>
                        </tr>
                        <tr>
                          <td className="py-3 pl-4">
                            <div className="flex h-5 items-center">
                              <input
                                id="hs-table-search-checkbox-1"
                                type="checkbox"
                                className="form-checkbox h-5 w-5 rounded border border-default-300 bg-transparent text-primary focus:border-default-300 focus:ring focus:ring-default-200 focus:ring-opacity-50 focus:ring-offset-0"
                              />
                              <label
                                htmlFor="hs-table-search-checkbox-1"
                                className="sr-only"
                              >
                                Checkbox
                              </label>
                            </div>
                          </td>
                          <td className="whitespace-nowrap px-6 py-4">
                            <div className="flex items-start gap-3">
                              <Image
                                src="/yum_r/assets/burger-fLOaQ2L6.png"
                                width="48"
                                height="48"
                                className="size-12 rounded-full"
                                alt="Veg Burger"
                              />
                              <h5 className="text-base font-medium text-default-700">
                                Veg Burger
                              </h5>
                            </div>
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-base text-default-800">
                            {" "}
                            Burger
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-base text-default-800">
                            $ 488
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-base text-default-800">
                            {" "}
                            1002
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-base text-default-800">
                            <div className="flex items-center justify-start gap-1">
                              <svg
                                stroke="currentColor"
                                fill="currentColor"
                                strokeWidth="0"
                                viewBox="0 0 576 512"
                                className="fill-yellow-400 text-yellow-400"
                                height="18"
                                width="18"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"></path>
                              </svg>
                              <svg
                                stroke="currentColor"
                                fill="currentColor"
                                strokeWidth="0"
                                viewBox="0 0 576 512"
                                className="fill-yellow-400 text-yellow-400"
                                height="18"
                                width="18"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"></path>
                              </svg>
                              <svg
                                stroke="currentColor"
                                fill="currentColor"
                                strokeWidth="0"
                                viewBox="0 0 576 512"
                                className="fill-yellow-400 text-yellow-400"
                                height="18"
                                width="18"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"></path>
                              </svg>
                              <svg
                                stroke="currentColor"
                                fill="currentColor"
                                strokeWidth="0"
                                viewBox="0 0 576 512"
                                className="fill-yellow-400 text-yellow-400"
                                height="18"
                                width="18"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"></path>
                              </svg>
                              <svg
                                stroke="currentColor"
                                fill="currentColor"
                                strokeWidth="0"
                                viewBox="0 0 576 512"
                                className="fill-yellow-400 text-yellow-400"
                                height="18"
                                width="18"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"></path>
                              </svg>
                            </div>
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-center">
                            <div className="hs-dropdown relative inline-flex [--placement:bottom-right]">
                              <button
                                type="button"
                                className="hs-dropdown-toggle inline-flex font-medium text-default-600 transition-all"
                              >
                                <svg
                                  stroke="currentColor"
                                  fill="none"
                                  strokeWidth="2"
                                  viewBox="0 0 24 24"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  height="22"
                                  width="22"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <circle cx="12" cy="12" r="1"></circle>
                                  <circle cx="12" cy="5" r="1"></circle>
                                  <circle cx="12" cy="19" r="1"></circle>
                                </svg>
                              </button>
                              <div className="hs-dropdown-menu hs-dropdown-open:opacity-100 z-20 mt-4 hidden min-w-[150px] rounded-lg border border-default-100 bg-white p-1.5 opacity-0 shadow-[rgba(17,_17,_26,_0.1)_0px_0px_16px] transition-[opacity,margin] dark:bg-default-50">
                                <ul className="flex flex-col gap-1">
                                  <li>
                                    <button className="flex w-full items-center gap-3 rounded px-3 py-2 font-normal text-default-600 transition-all hover:bg-default-100 hover:text-default-700">
                                      Edit
                                    </button>
                                  </li>
                                  <li>
                                    <button className="flex w-full items-center gap-3 rounded px-3 py-2 font-normal text-default-600 transition-all hover:bg-default-100 hover:text-default-700">
                                      View
                                    </button>
                                  </li>
                                  <li>
                                    <button className="flex w-full items-center gap-3 rounded px-3 py-2 font-normal text-default-600 transition-all hover:bg-default-100 hover:text-default-700">
                                      Delete
                                    </button>
                                  </li>
                                </ul>
                              </div>
                            </div>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="xl:col-span-1">
          <div className="mb-6 rounded-lg border border-default-200">
            <div className="border-b border-b-default-300 p-6">
              <h4 className="text-xl font-medium text-default-900">
                Seller Personal Detail
              </h4>
            </div>
            <div className="px-6 py-5">
              <table cellPadding={10}>
                <tbody>
                  <tr>
                    <td className="text-start text-base font-medium">
                      Owner Name:
                    </td>
                    <td className="text-start">Hollie Bruggen</td>
                  </tr>
                  <tr>
                    <td className="text-start text-base font-medium">
                      Status:
                    </td>
                    <td className="text-start">active</td>
                  </tr>
                  <tr>
                    <td className="text-start text-base font-medium">Email:</td>
                    <td className="text-start"><EMAIL></td>
                  </tr>
                  <tr>
                    <td className="text-start text-base font-medium">
                      Contact No:
                    </td>
                    <td className="text-start">1078832848</td>
                  </tr>
                  <tr>
                    <td className="text-start text-base font-medium">
                      Orders:
                    </td>
                    <td className="text-start">17</td>
                  </tr>
                  <tr>
                    <td className="text-start text-base font-medium">
                      Location:
                    </td>
                    <td className="text-start">Mae Lan</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          <div className="mb-6 rounded-lg border border-default-200">
            <div className="border-b border-b-default-300 p-6">
              <h4 className="text-xl font-medium text-default-900">
                Customer Reviews
              </h4>
            </div>
            <div className="p-6">
              <div className="mb-6">
                <div className="flex items-center gap-2">
                  <h5 className="text-sm">5</h5>
                  <div className="flex h-2 w-full overflow-hidden rounded-lg bg-default-100">
                    <div className="w-full rounded-lg bg-yellow-400"></div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <h5 className="text-sm">4</h5>
                  <div className="flex h-2 w-full overflow-hidden rounded-lg bg-default-100">
                    <div className="w-4/5 rounded-lg bg-yellow-400"></div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <h5 className="text-sm">3</h5>
                  <div className="flex h-2 w-full overflow-hidden rounded-lg bg-default-100">
                    <div className="w-3/5 rounded-lg bg-yellow-400"></div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <h5 className="text-sm">2</h5>
                  <div className="flex h-2 w-full overflow-hidden rounded-lg bg-default-100">
                    <div className="w-2/5 rounded-lg bg-yellow-400"></div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <h5 className="text-sm">1</h5>
                  <div className="flex h-2 w-full overflow-hidden rounded-lg bg-default-100">
                    <div className="w-1/5 rounded-lg bg-yellow-400"></div>
                  </div>
                </div>
              </div>
              <div className="mb-6 flex justify-around">
                <div className="text-center">
                  <h2 className="mb-1 text-2xl font-medium text-default-900">
                    4.5{" "}
                    <svg
                      stroke="currentColor"
                      fill="currentColor"
                      strokeWidth="0"
                      viewBox="0 0 576 512"
                      className="inline-flex fill-yellow-400 align-middle text-yellow-400"
                      height="20"
                      width="20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"></path>
                    </svg>
                  </h2>
                  <p className="block text-xs text-default-600">452 Reviews</p>
                </div>
                <div className="text-center">
                  <h2 className="mb-1 text-2xl font-medium text-default-900">
                    91%
                  </h2>
                  <p className="block text-xs text-default-600">Recommended</p>
                </div>
              </div>
              <div className="mb-4">
                <div className="mb-4 flex items-center gap-3">
                  <Image
                    src="/yum_r/assets/avatar1-lkSFncXM.png"
                    className="h-11 w-11 rounded-full"
                    alt="avatar"
                  />
                  <div className="flex-grow">
                    <h4 className="mb-1 text-xs text-default-700">
                      Kianna Stanton{" "}
                      <span className="text-default-600">🇺🇸US</span>
                    </h4>
                    <h4 className="text-xs text-green-400">Verified Buyer</h4>
                  </div>
                  <div>
                    <svg
                      stroke="currentColor"
                      fill="currentColor"
                      strokeWidth="0"
                      viewBox="0 0 576 512"
                      className="inline-flex fill-yellow-400 align-middle text-yellow-400"
                      height="20"
                      width="20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"></path>
                    </svg>
                    <svg
                      stroke="currentColor"
                      fill="currentColor"
                      strokeWidth="0"
                      viewBox="0 0 576 512"
                      className="inline-flex fill-yellow-400 align-middle text-yellow-400"
                      height="20"
                      width="20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"></path>
                    </svg>
                    <svg
                      stroke="currentColor"
                      fill="currentColor"
                      strokeWidth="0"
                      viewBox="0 0 576 512"
                      className="inline-flex fill-yellow-400 align-middle text-yellow-400"
                      height="20"
                      width="20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"></path>
                    </svg>
                    <svg
                      stroke="currentColor"
                      fill="currentColor"
                      strokeWidth="0"
                      viewBox="0 0 576 512"
                      className="inline-flex fill-yellow-400 align-middle text-yellow-400"
                      height="20"
                      width="20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"></path>
                    </svg>
                    <svg
                      stroke="currentColor"
                      fill="currentColor"
                      strokeWidth="0"
                      viewBox="0 0 576 512"
                      className="inline-flex fill-yellow-400 align-middle text-yellow-400"
                      height="20"
                      width="20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"></path>
                    </svg>
                  </div>
                </div>
                <h5 className="mb-2 text-sm text-default-600">
                  SO DELICIOUS 🍯💯
                </h5>
                <p className="text-sm text-default-600">
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed
                  do eiusmod tempor incididunt ut labore et dolore magna aliqua.
                  Ut enim ad minim veniam, quis nostrud exercitation.
                </p>
              </div>
              <div className="mb-4">
                <div className="mb-4 flex items-center gap-3">
                  <Image
                    src="/yum_r/assets/avatar2-e3ZdIYj6.png"
                    className="h-11 w-11 rounded-full"
                    alt="avatar"
                  />
                  <div className="flex-grow">
                    <h4 className="mb-1 text-xs text-default-700">
                      Ryan Rhiel Madsen{" "}
                      <span className="text-default-600">🇺🇸US</span>
                    </h4>
                    <h4 className="text-xs text-green-400">Verified Buyer</h4>
                  </div>
                  <div>
                    <svg
                      stroke="currentColor"
                      fill="currentColor"
                      strokeWidth="0"
                      viewBox="0 0 576 512"
                      className="inline-flex fill-yellow-400 align-middle text-yellow-400"
                      height="20"
                      width="20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"></path>
                    </svg>
                    <svg
                      stroke="currentColor"
                      fill="currentColor"
                      strokeWidth="0"
                      viewBox="0 0 576 512"
                      className="inline-flex fill-yellow-400 align-middle text-yellow-400"
                      height="20"
                      width="20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"></path>
                    </svg>
                    <svg
                      stroke="currentColor"
                      fill="currentColor"
                      strokeWidth="0"
                      viewBox="0 0 576 512"
                      className="inline-flex fill-yellow-400 align-middle text-yellow-400"
                      height="20"
                      width="20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"></path>
                    </svg>
                    <svg
                      stroke="currentColor"
                      fill="currentColor"
                      strokeWidth="0"
                      viewBox="0 0 576 512"
                      className="inline-flex fill-yellow-400 align-middle text-yellow-400"
                      height="20"
                      width="20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"></path>
                    </svg>
                    <svg
                      stroke="currentColor"
                      fill="currentColor"
                      strokeWidth="0"
                      viewBox="0 0 576 512"
                      className="inline-flex fill-yellow-400 align-middle text-yellow-400"
                      height="20"
                      width="20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"></path>
                    </svg>
                  </div>
                </div>
                <h5 className="mb-2 text-sm text-default-600">
                  SO DELICIOUS 🍯💯
                </h5>
                <p className="text-sm text-default-600">
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed
                  do eiusmod tempor incididunt ut labore et dolore magna aliqua.
                  Ut enim ad minim veniam, quis nostrud exercitation.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
