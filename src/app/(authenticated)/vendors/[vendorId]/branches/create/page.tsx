import { api } from "@/lib/api";
import { cache } from "react";
import BranchCreateForm from "./form";

const fetchVendor = cache((id: string) => api.get<Vendor>(`vendors/${id}`));

export const generateMetadata = async ({
  params,
}: {
  params: {
    vendorId: string;
  };
}): Promise<Record<string, any>> => {
  const vendor = await fetchVendor(params.vendorId);

  return {
    title: `Add Branche To ${vendor?.name}`,
    description: "Vendor",
    keywords: ["vendor"],
  };
};

export default async function page({
  params,
}: {
  params: {
    vendorId: string;
  };
}) {
  const vendor = await fetchVendor(params.vendorId);

  const saveBranch = async (data: FormData) => {
    "use server";

    return await api.post<Branch>(`vendors/${params.vendorId}/branches`, data);
  };

  return (
    <div className="p-4">
      <BranchCreateForm storeBranch={saveBranch} />
    </div>
  );
}
