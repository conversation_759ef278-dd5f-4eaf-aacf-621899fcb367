import PaginatedTable from "@/components/table";
import { api } from "@/lib/api";
import Link from "next/link";
import { cache } from "react";

const fetchVendor = cache((id: string) => api.get<Vendor>(`vendors/${id}`));

export const generateMetadata = async ({
  params,
}: {
  params: {
    vendorId: string;
  };
}): Promise<Record<string, any>> => {
  const vendor = await fetchVendor(params.vendorId);

  return {
    title: `Branches In ${vendor?.name}`,
    description: "Vendor",
    keywords: ["vendor"],
  };
};

export default async function page({
  params,
}: {
  params: {
    vendorId: string;
  };
}) {
  const branches = await api.get<PaginatedData<Branch>>(
    `vendors/${params.vendorId}/branches`,
  );

  return (
    <div className="py-4">
      {branches && (
        <PaginatedTable<Branch>
          records={branches}
          columns={[
            {
              id: "name",
              title: "Name",
              render: (record) => (
                <Link href={`/branches/${record.id}`}>{record.name}</Link>
              ),
            },
            {
              id: "address",
              title: "Address",
              render: (record) => <span>{record.location?.address}</span>,
            },
            { id: "phone", title: "Phone" },
            { id: "email", title: "Email" },
            {
              id: "staff",
              title: "Staff",
              render: (record) => (
                <Link href={`/branches/${record.id}/staff`} title="Staff">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={1.5}
                    stroke="currentColor"
                    className="h-6 w-6"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"
                    />
                  </svg>
                </Link>
              ),
            },
            // customers
            {
              id: "customers",
              title: "Customers",
              render: (record) => (
                <Link
                  href={`/branches/${record.id}/customers`}
                  title="Customers"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={1.5}
                    stroke="currentColor"
                    className="h-6 w-6"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m.94 3.198.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0 1 12 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 0 1 6 18.719m12 0a5.971 5.971 0 0 0-.941-3.197m0 0A5.995 5.995 0 0 0 12 12.75a5.995 5.995 0 0 0-5.058 2.772m0 0a3 3 0 0 0-4.681 2.72 8.986 8.986 0 0 0 3.74.477m.94-3.197a5.971 5.971 0 0 0-.94 3.197M15 6.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm6 3a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Zm-13.5 0a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Z"
                    />
                  </svg>
                </Link>
              ),
            },
            // products
            {
              id: "products",
              title: "Products",
              render: (record) => (
                <Link href={`/branches/${record.id}/products`} title="Products">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={1.5}
                    stroke="currentColor"
                    className="h-6 w-6"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M15.75 10.5V6a3.75 3.75 0 1 0-7.5 0v4.5m11.356-1.993 1.263 12c.07.665-.45 1.243-1.119 1.243H4.25a1.125 1.125 0 0 1-1.12-1.243l1.264-12A1.125 1.125 0 0 1 5.513 7.5h12.974c.576 0 1.059.435 1.119 1.007ZM8.625 10.5a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm7.5 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"
                    />
                  </svg>
                </Link>
              ),
            },

            {
              id: "sections",
              title: "Sections",
              render: (record) => (
                <Link href={`/branches/${record.id}/sections`} title="Sections">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={1.5}
                    stroke="currentColor"
                    className="h-6 w-6"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M2.25 7.125C2.25 6.504 2.754 6 3.375 6h6c.621 0 1.125.504 1.125 1.125v3.75c0 .621-.504 1.125-1.125 1.125h-6a1.125 1.125 0 0 1-1.125-1.125v-3.75ZM14.25 8.625c0-.621.504-1.125 1.125-1.125h5.25c.621 0 1.125.504 1.125 1.125v8.25c0 .621-.504 1.125-1.125 1.125h-5.25a1.125 1.125 0 0 1-1.125-1.125v-8.25ZM3.75 16.125c0-.621.504-1.125 1.125-1.125h5.25c.621 0 1.125.504 1.125 1.125v2.25c0 .621-.504 1.125-1.125 1.125h-5.25a1.125 1.125 0 0 1-1.125-1.125v-2.25Z"
                    />
                  </svg>
                </Link>
              ),
            },
            {
              id: "actions",
              title: "Actions",
              render: (record) => (
                <div className="flex space-x-2">
                  <Link href={`/branches/${record.id}/edit`}>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      strokeWidth={1.5}
                      stroke="currentColor"
                      className="h-6 w-6"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125"
                      />
                    </svg>
                  </Link>
                </div>
              ),
            },
          ]}
          tools={
            <div className="mb-4 flex items-center justify-between px-4">
              <p></p>
              <Link
                href={`/vendors/${params.vendorId}/branches/create`}
                className="rounded-lg bg-primary px-4 py-2 text-white"
              >
                Add Branch
              </Link>
            </div>
          }
          title={"Branches"}
          path={`/vendors/${params.vendorId}/branches`}
        />
      )}
    </div>
  );
}
