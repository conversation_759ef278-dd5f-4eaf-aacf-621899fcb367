import PaginatedTable from "@/components/table";
import { api } from "@/lib/api";
import { cache } from "react";

const fetchVendor = cache((id: string) => api.get<Vendor>(`vendors/${id}`));

export const generateMetadata = async ({
  params,
}: {
  params: {
    vendorId: string;
  };
}): Promise<Record<string, any>> => {
  const vendor = await fetchVendor(params.vendorId);

  return {
    title: `Customers In ${vendor?.name}`,
    description: "Vendor customers",
    keywords: ["vendor"],
  };
};

export default async function page({
  params,
}: {
  params: {
    vendorId: string;
  };
}) {
  const vendor = await fetchVendor(params.vendorId);

  const customers = await api.get<PaginatedData<User>>(
    `vendors/${params.vendorId}/customers`,
  );

  return (
    <div>
      {customers && (
        <PaginatedTable<User>
          records={customers}
          columns={[
            {
              id: "name",
              title: "Name",
            },
            {
              id: "email",
              title: "Email",
            },
            {
              id: "phone",
              title: "Phone",
            },
            {
              id: "created_at",
              title: "Created At",
            },
            {
              id: "status",
              title: "Status",
            },
            {
              id: "actions",
              title: "Actions",
            },
          ]}
          title={`Customers In ${vendor?.name}`}
          path={`/portal/vendors/${params.vendorId}/customers`}
          tools={<></>}
        />
      )}
    </div>
  );
}
