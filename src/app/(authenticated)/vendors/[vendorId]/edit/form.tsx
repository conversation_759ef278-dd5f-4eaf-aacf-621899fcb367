"use client";

import AsyncSelect from "react-select/async";
import { useState, ChangeEvent } from "react";
import { <PERSON>mit<PERSON><PERSON><PERSON>, Controller, useForm } from "react-hook-form";
import { toast } from "react-hot-toast";
import Image from "next/image";
import PhoneInput from "react-phone-number-input";
import makeAnimated from "react-select/animated";

export default function VendorEditForm({
  updateVendor,
  updateSpecialization,
  defaultValues,
  services,
}: {
  updateVendor: (data: FormData) => Promise<void>;
  updateSpecialization: (data: { specialization: string[] }) => Promise<void>;
  defaultValues: Vendor;
  services: Service[];
}) {
  const [preview, setPreview] = useState<string>();
  const [cpreview, setCPreview] = useState<string>();

  const animatedComponents = makeAnimated();

  const {
    watch,
    handleSubmit,
    register,
    reset,
    control,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<
    Vendor & {
      upload: File;
      uploadc: File;
      taskId: string;
      serviceId?: string;
      vendorId?: string;
      specialization: string[];
    }
  >({
    defaultValues: { ...defaultValues, phone: `+${defaultValues.phone}` },
  });

  const vendor = watch();

  const handleUploadedFile = (event: ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;

    if (files) {
      const file = files[0];
      setValue("upload", file);
      const urlImage = URL.createObjectURL(file);

      setPreview(urlImage);
    }
  };

  const handleUploadedCover = (event: ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;

    if (files) {
      const file = files[0];
      setValue("uploadc", file);
      const urlImage = URL.createObjectURL(file);

      setCPreview(urlImage);
    }
  };

  const fetchSpecialities = async (s: string) => {
    if (s.length > 3) {
      const specialities: Speciality[] = await fetch(
        `/api/specialities?s=${s}`,
      ).then((r) => r.json());

      return specialities;
    }
  };

  const onSubmit: SubmitHandler<
    Vendor & { upload: File; uploadc: File; taskId: string }
  > = (v: Vendor & { upload: File; uploadc: File; taskId: string }) => {
    const data = new FormData();

    data.append("id", vendor.id);
    data.append("name", vendor.name);
    data.append("slug", vendor.slug);
    data.append("details", vendor.details!);
    data.append("email", vendor.email);
    data.append("phone", vendor.phone.replace("+", ""));

    if (vendor.upload) {
      data.append("logo", vendor.upload);
    }

    if (vendor.uploadc) {
      data.append("cover", vendor.uploadc);
    }

    toast
      .promise(
        updateVendor(data),
        {
          loading: "Updating vendor...",
          success: "Vendor has been updated 👌",
          error: "Could not update vendor 🤯",
        },
        {
          position: "bottom-center",
        },
      )
      .then(() => {
        reset();
      })
      .catch((e) => {
        console.error(e);
      });
  };

  return (
    <form action="#" className="space-y-4" onSubmit={handleSubmit(onSubmit)}>
      <div className="grid grid-cols-12">
        <label
          htmlFor="name"
          className="col-span-12 mb-2 text-sm font-medium text-default-900 dark:text-white lg:col-span-4"
        >
          Name
        </label>
        <input
          type="text"
          id="name"
          {...register("name")}
          className="col-span-12 block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-primary-600 focus:ring-primary-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-primary-500 dark:focus:ring-primary-600 lg:col-span-8"
          placeholder="Type vendor name"
          required
        />
      </div>

      <div className="grid grid-cols-12">
        <label
          htmlFor="slug"
          className="col-span-12 mb-2 text-sm font-medium text-default-900 dark:text-white lg:col-span-4"
        >
          Slug
        </label>
        <input
          type="text"
          id="slug"
          {...register("slug")}
          className="col-span-12 block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-primary-600 focus:ring-primary-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-primary-500 dark:focus:ring-primary-600 lg:col-span-8"
          placeholder="Type vendor slug"
          required
        />
      </div>

      <div className="grid grid-cols-12">
        <label
          htmlFor="email"
          className="col-span-12 mb-2 text-sm font-medium text-default-900 dark:text-white lg:col-span-4"
        >
          Email address
        </label>
        <input
          type="email"
          id="email"
          {...register("email")}
          className="col-span-12 block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-primary-600 focus:ring-primary-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-primary-500 dark:focus:ring-primary-600 lg:col-span-8"
          placeholder="Email"
          required
        />
      </div>

      <div className="grid grid-cols-12">
        <label
          htmlFor="name"
          className="col-span-12 mb-2 text-sm font-medium text-default-900 dark:text-white lg:col-span-4"
        >
          Phone number
        </label>

        <div className="col-span-12 block w-full rounded-lg border border-default-300 bg-default-50 px-3 py-3 text-sm text-default-900 focus:border-primary-600 focus:ring-primary-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-primary-500 dark:focus:ring-primary-600 lg:col-span-8 [&>input]:bg-transparent">
          <Controller
            name="phone"
            control={control}
            render={({ field: { onChange, value } }) => (
              <PhoneInput
                placeholder="Enter phone number"
                value={value}
                onChange={onChange}
                defaultCountry="KE"
              />
            )}
          />
        </div>
      </div>

      <div className="grid grid-cols-12">
        <label
          htmlFor="logo"
          className="col-span-12 mb-2 text-sm font-medium text-default-900 dark:text-white lg:col-span-4"
        >
          Vendor images
        </label>

        <div className="col-span-12 flex gap-3 lg:col-span-8">
          <label className="flex h-72 w-1/3 items-center justify-center rounded-lg border border-dashed border-default-300 bg-default-50 text-sm text-default-900 focus:border-primary-600 focus:ring-primary-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-primary-500 dark:focus:ring-primary-600">
            <input
              type="file"
              name="logo"
              id="logo"
              className="hidden"
              onChange={handleUploadedFile}
            />
            {preview ? (
              <Image
                src={preview}
                alt="preview"
                width={100}
                height={50}
                className="h-full w-full"
              />
            ) : (
              <p>Upload logo</p>
            )}
          </label>

          <label className="flex h-72 w-full flex-1 items-center justify-center rounded-lg border border-dashed border-default-300 bg-default-50 text-sm text-default-900 focus:border-primary-600 focus:ring-primary-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-primary-500 dark:focus:ring-primary-600 lg:col-span-8">
            <input
              type="file"
              name="logo"
              id="logo"
              className="hidden"
              onChange={handleUploadedCover}
            />
            {cpreview ? (
              <Image
                src={cpreview}
                alt="preview"
                width={100}
                height={50}
                className="h-full w-full"
              />
            ) : (
              <p>Upload cover image</p>
            )}
          </label>
        </div>
      </div>

      <div className="grid grid-cols-12">
        <label
          htmlFor="description"
          className="col-span-12 mb-2 text-sm font-medium text-default-900 dark:text-white lg:col-span-4"
        >
          Description
        </label>

        <textarea
          id="description"
          rows={4}
          {...register("details")}
          className=".5 col-span-12 block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-primary-600 focus:ring-primary-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-primary-500 dark:focus:ring-primary-600 lg:col-span-8"
          placeholder="Enter vendor description here"
        />
      </div>

      <div className="grid grid-cols-12">
        <label
          htmlFor="serviceId"
          className="col-span-12 mb-2 text-sm font-medium text-default-900 dark:text-white lg:col-span-4"
        >
          Default service
        </label>
        <Controller
          name="serviceId"
          control={control}
          render={({ field }) => (
            <div className="col-span-12 flex items-center space-x-4 lg:col-span-8">
              {services?.map((service) => {
                return (
                  <div key={service.id}>
                    <label className="inline-flex cursor-pointer items-center">
                      <input
                        type="radio"
                        placeholder="Enter Your Bank Name"
                        {...register("serviceId")}
                        id={service.id}
                        value={service.id}
                        className="peer sr-only"
                      />
                      <div className="peer relative h-6 w-11 rounded-full bg-default-200 after:absolute after:start-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-primary after:bg-white after:transition-all after:content-[''] peer-checked:bg-primary peer-checked:after:translate-x-full peer-checked:after:border-white peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:border-default-600 dark:bg-default-700 dark:peer-focus:ring-primary rtl:peer-checked:after:-translate-x-full"></div>
                      <span className="ms-3 text-sm font-medium text-default-900 dark:text-primary">
                        {service.name}
                      </span>
                    </label>
                  </div>
                );
              })}
            </div>
          )}
        />
      </div>

      <div className="grid grid-cols-12">
        <label
          htmlFor="specialization"
          className="col-span-12 mb-2 text-sm font-medium text-default-900 dark:text-white lg:col-span-4"
        >
          Specialization
        </label>
        <div className="col-span-12 lg:col-span-8">
          <AsyncSelect
            isClearable
            isMulti
            cacheOptions
            isSearchable
            // @ts-ignore
            loadOptions={fetchSpecialities}
            getOptionLabel={(option: Speciality) => option.name}
            getOptionValue={(option: Speciality) => option.id}
            placeholder="Enter Specialization"
            defaultOptions
            components={{
              IndicatorSeparator: () => null,
            }}
            classNames={{
              container: () => "w-full",
              control: () => "w-full !rounded-lg !py-[4px] w-full",
            }}
            onChange={(v) =>
              updateSpecialization({
                specialization: v.map((v) => v.id),
              })
            }
          />
        </div>
      </div>

      <div className="grid grid-cols-12">
        <label
          htmlFor="name"
          className="col-span-12 mb-2 text-sm font-medium text-default-900 dark:text-white lg:col-span-4"
        ></label>
        <div className="col-span-12 flex justify-between lg:col-span-8">
          <label className="relative inline-flex cursor-pointer items-center">
            <input
              {...register("active")}
              type="checkbox"
              className="peer sr-only"
            />
            <div className="peer h-6 w-11 rounded-full bg-default-200 after:absolute after:start-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-default-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-primary peer-checked:after:translate-x-full peer-checked:after:border-white peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:border-default-600 dark:bg-default-700 dark:peer-focus:ring-blue-800 rtl:peer-checked:after:-translate-x-full"></div>
            <span className="ms-3 text-sm font-medium text-default-900 dark:text-default-300">
              {vendor.active ? "Active" : "Inactive"}
            </span>
          </label>

          <label className="relative inline-flex cursor-pointer items-center">
            <input
              {...register("featured")}
              type="checkbox"
              className="peer sr-only"
            />
            <div className="peer h-6 w-11 rounded-full bg-default-200 after:absolute after:start-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-default-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-primary peer-checked:after:translate-x-full peer-checked:after:border-white peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:border-default-600 dark:bg-default-700 dark:peer-focus:ring-blue-800 rtl:peer-checked:after:-translate-x-full"></div>
            <span className="ms-3 text-sm font-medium text-default-900 dark:text-default-300">
              {vendor.featured ? "Featured" : "Not featured"}
            </span>
          </label>
        </div>
      </div>

      <div className="flex w-full justify-between">
        <button
          type="button"
          onClick={() => reset()}
          className="w-32 justify-center rounded-lg bg-default-200 px-5 py-3 text-center text-sm font-medium text-default-900 hover:bg-default-300 focus:outline-none focus:ring-4 focus:ring-default-300 dark:bg-default-700 dark:hover:bg-default-800 dark:focus:ring-default-800"
        >
          Reset
        </button>
        <button
          type="submit"
          className="w-60 justify-center rounded-lg bg-primary px-5 py-3 text-center text-sm font-medium text-white hover:bg-primary-800 focus:outline-none focus:ring-4 focus:ring-primary-300 dark:bg-primary dark:hover:bg-primary dark:focus:ring-primary-800"
        >
          Save vendor details
        </button>
      </div>
    </form>
  );
}
