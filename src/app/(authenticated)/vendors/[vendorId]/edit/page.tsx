import { cache } from "react";
import VendorEditForm from "./form";
import { api } from "@/lib/api";
import Link from "next/link";
import { revalidatePath } from "next/cache";

const getVendor = cache((vendorId: string) =>
  api.get<Vendor>(`vendors/${vendorId}`),
);

export const generateMetadata = async ({
  params: { vendorId },
}: {
  params: { vendorId: string };
}) => {
  const vendor = await getVendor(vendorId);

  return {
    title: `Edit ${vendor?.name}`,
  };
};

export default async function page({
  params: { vendorId },
}: {
  params: { vendorId: string };
}) {
  const vendor = await getVendor(vendorId);

  const services = await api.get<PaginatedData<Service>>(
    `vendors/${vendorId}/services`,
  );

  const updateVendor = async (data: FormData) => {
    "use server";

    await api.put(`vendors/${vendorId}`, data);

    revalidatePath(`/vendors/${vendorId}/edit`);
  };

  const updateSpecialization = async (data: { specialization: string[] }) => {
    "use server";

    await api.post(`vendors/${vendorId}/specialities`, data);
  };

  return (
    <div className="p-4">
      <div className="mb-4 flex justify-between">
        <p></p>

        <Link
          href="/vendors"
          className="rounded-lg bg-primary px-5 py-2 text-white"
        >
          <span>Back to vendors</span>
        </Link>
      </div>

      {vendor && services && (
        <VendorEditForm
          defaultValues={vendor}
          services={services?.data}
          updateVendor={updateVendor}
          updateSpecialization={updateSpecialization}
        />
      )}
    </div>
  );
}
