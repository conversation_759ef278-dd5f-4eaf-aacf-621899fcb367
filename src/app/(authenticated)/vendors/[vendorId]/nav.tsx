'use client';

import Link from "next/link";
import { useParams, usePathname } from "next/navigation";

export default function VendorNav() {
  const pathName = usePathname();
  const { vendorId } = useParams();

  return (
    <ul className="flex">
      <li className={`font-semibold px-2 hover:text-primary hover:border-b ${pathName === `/vendors/${vendorId}` ? 'border-b text-primary' : ''}`}>
        <Link href={`/vendors/${vendorId}`}>
          Overview
        </Link>
      </li>

      <li className={`font-semibold px-2 hover:text-primary hover:border-b ${pathName === `/vendors/${vendorId}/products` ? 'border-b text-primary' : ''}`}>
        <Link href={`/vendors/${vendorId}/products`}>
          Products
        </Link>
      </li>

      <li className={`font-semibold px-2 hover:text-primary hover:border-b ${pathName === `/vendors/${vendorId}/branches` ? 'border-b text-primary' : ''}`}>
        <Link href={`/vendors/${vendorId}/branches`}>
          Branches
        </Link>
      </li>

      <li className={`font-semibold px-2 hover:text-primary hover:border-b ${pathName === `/vendors/${vendorId}/tasks` ? 'border-b text-primary' : ''}`}>
        <Link href={`/vendors/${vendorId}/tasks`}>  
          Tasks
        </Link>
      </li>

      <li className={`font-semibold px-2 hover:text-primary hover:border-b ${pathName === `/vendors/${vendorId}/services` ? 'border-b text-primary' : ''}`}>
        <Link href={`/vendors/${vendorId}/services`}>
          Services
        </Link>
      </li>

      {/* <li className={`font-semibold px-2 hover:text-primary hover:border-b ${pathName === `/vendors/${vendorId}/specialization` ? 'border-b text-primary' : ''}`}>  
        <Link href={`/vendors/${vendorId}/specialization`}>
          Specialization    
        </Link>
      </li> */}
    </ul>   
  );
}