import notFound from "@/app/not-found";
import { api, imagePath } from "@/lib/api";
import Image from "next/image";
import Link from "next/link";
import { cache } from "react";
import { format as formatDate } from "date-fns";
import QRCode from "qrcode";
import BackBtn from "@/components/back";
import VendorNav from "./nav";

const fetchVendor = cache((id: string) => api.get<Vendor>(`vendors/${id}`));

export const generateMetadata = async ({
  params,
}: {
  params: {
    vendorId: string;
  };
}): Promise<Record<string, any>> => {
  const vendor = await fetchVendor(params.vendorId);

  return {
    title: vendor?.name,
    description: "Vendor",
    keywords: ["vendor"],
  };
};

export default async function page({
  params,
}: {
  params: {
    vendorId: string;
  };
}) {
  const vendor = await api.get<Vendor>(`vendors/${params.vendorId}`);
  const orders = await api.get<PaginatedData<Order>>(
    `vendors/${params.vendorId}/orders`,
  );
  

  const qrCode = await QRCode.toDataURL(params.vendorId, {
    width: 250,
    color: {
      dark: "#000000",
      light: "#ffffff",
    },
    errorCorrectionLevel: "L",
  });

  return (
    <div className="grid grid-cols-1 gap-6 lg:grid-cols-3 p-5">
      <div className="lg:col-span-1">
        <div className="rounded-lg border border-default-200">
          <div
            className={`h-52 rounded-t-lg bg-default-100 pt-20 bg-[url('${imagePath(vendor?.cover?.url, null)}')] bg-cover bg-center bg-no-repeat`}
          >
            {/* <Image
              src={imagePath(vendor?.logo?.url, null)}
              width="120"
              height="120"
              className="ml-4 w-32"
              alt="avatar"
            /> */}
          </div>

          <div className="mt-6 p-6 text-start">
            {vendor?.name && (
              <p className="mb-6 text-default-400">{vendor?.name}</p>
            )}

            <p className="mb-3 text-zinc-400">
              <b>Mobile :</b>
              <a href={`tel:${vendor?.phone}`} className="ms-2">
                {vendor?.phone}
              </a>
            </p>

            <p className="mb-3 text-zinc-400">
              <b>Email :</b>{" "}
              <a href={`mailto:${vendor?.email}`} className="ms-2 ">
                {vendor?.email}
              </a>
            </p>
          </div>
        </div>

        <div className="mt-6 flex items-center justify-center">
          <Image
            src={qrCode}
            className="w-80"
            title={vendor?.name}
            alt={vendor?.name!}
            width={"200"}
            height={"200"}
          />
        </div>
      </div>

      <div className="lg:col-span-2">
        <div className="rounded-lg border border-default-200">
          <div className="overflow-hidden p-6 ">
            <div className="flex flex-wrap items-center gap-4 sm:justify-between lg:flex-nowrap">
              <h2 className="text-xl font-semibold text-default-800">
                Order history
              </h2>
              <div className="flex flex-wrap items-center justify-end gap-2">
                <div className="hs-dropdown relative inline-flex">
                  <button
                    type="button"
                    className="hs-dropdown-toggle flex items-center gap-2 rounded-md bg-default-100 px-4 py-3 text-sm font-medium text-default-700 transition-all xl:px-5"
                  >
                    Sort : Ascending{" "}
                    <svg
                      stroke="currentColor"
                      fill="none"
                      strokeWidth="2"
                      viewBox="0 0 24 24"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      height="16"
                      width="16"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="m6 9 6 6 6-6"></path>
                    </svg>
                  </button>
                  <div className="hs-dropdown-menu hs-dropdown-open:opacity-100 z-20 mt-4 hidden min-w-[200px] rounded-lg border border-default-100 bg-white p-1.5 opacity-0 shadow-[rgba(17,_17,_26,_0.1)_0px_0px_16px] transition-[opacity,margin] dark:bg-default-50">
                    <ul className="flex flex-col gap-1">
                      <li>
                        <span className="flex items-center gap-3 rounded bg-default-100 px-3 py-2 font-normal text-default-700 transition-all hover:bg-default-100 hover:text-default-700">
                          Ascending
                        </span>
                      </li>
                      <li>
                        <span className="flex items-center gap-3 rounded px-3 py-2 font-normal text-default-600 transition-all hover:bg-default-100 hover:text-default-700">
                          Descending
                        </span>
                      </li>
                    </ul>
                  </div>
                </div>
                <div className="hs-dropdown relative inline-flex">
                  <button
                    type="button"
                    className="hs-dropdown-toggle flex items-center gap-2 rounded-md bg-default-100 px-4 py-3 text-sm font-medium text-default-700 transition-all xl:px-5"
                  >
                    Status : All{" "}
                    <svg
                      stroke="currentColor"
                      fill="none"
                      strokeWidth="2"
                      viewBox="0 0 24 24"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      height="16"
                      width="16"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="m6 9 6 6 6-6"></path>
                    </svg>
                  </button>
                  <div className="hs-dropdown-menu hs-dropdown-open:opacity-100 z-20 mt-4 hidden min-w-[200px] rounded-lg border border-default-100 bg-white p-1.5 opacity-0 shadow-[rgba(17,_17,_26,_0.1)_0px_0px_16px] transition-[opacity,margin] dark:bg-default-50">
                    <ul className="flex flex-col gap-1">
                      <li>
                        <span className="flex items-center gap-3 rounded bg-default-100 px-3 py-2 font-normal text-default-700 transition-all hover:bg-default-100 hover:text-default-700">
                          All
                        </span>
                      </li>
                      <li>
                        <span className="flex items-center gap-3 rounded px-3 py-2 font-normal text-default-600 transition-all hover:bg-default-100 hover:text-default-700">
                          Paid
                        </span>
                      </li>
                      <li>
                        <span className="flex items-center gap-3 rounded px-3 py-2 font-normal text-default-600 transition-all hover:bg-default-100 hover:text-default-700">
                          Cancelled
                        </span>
                      </li>
                      <li>
                        <span className="flex items-center gap-3 rounded px-3 py-2 font-normal text-default-600 transition-all hover:bg-default-100 hover:text-default-700">
                          Refunded
                        </span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="relative overflow-x-auto">
            <div className="inline-block min-w-full align-middle">
              <div className="overflow-hidden">
                <table className="w-full divide-y divide-default-200">
                  <thead className="bg-default-100">
                    <tr className="text-start">
                      <th className="whitespace-nowrap px-6 py-3 text-start text-sm font-medium text-default-800">
                        Customer
                      </th>
                      <th className="whitespace-nowrap px-6 py-3 text-start text-sm font-medium text-default-800">
                        Branch
                      </th>
                      <th className="whitespace-nowrap px-6 py-3 text-start text-sm font-medium text-default-800">
                        Date
                      </th>
                      <th className="whitespace-nowrap px-6 py-3 text-start text-sm font-medium text-default-800">
                        Amount
                      </th>
                      <th className="whitespace-nowrap px-6 py-3 text-start text-sm font-medium text-default-800">
                        Status
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-default-200">
                    {orders?.data.map((order) => (
                      <tr key={order.id}>
                        <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-default-800">
                          <div className="flex items-center gap-4">
                            <div className="shrink">
                              <div className="h-18 w-18">
                                {/* <Image
                                  src={imagePath(
                                    order.customer?.avatar?.url,
                                    null,
                                  )}
                                  className="h-full max-w-full"
                                  width="72"
                                  height="72"
                                  alt={order.customer?.name}
                                /> */}
                              </div>
                            </div>
                            <div className="grow">
                              <p className="mb-1 text-sm text-default-500">
                                {order.customer?.name}
                              </p>
                              <div className="flex items-center gap-2">
                                {order.customer?.email}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-default-800">
                          <div className="flex items-center gap-4">
                            <div className="grow">
                              <p className="mb-1 text-sm text-default-500">
                                {order.branch?.name}
                              </p>
                              <div className="flex items-center gap-2">
                                {order.branch?.email}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-default-500">
                          {formatDate(new Date(order.createdAt), "dd/MM/yyyy")}
                        </td>
                        <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-default-500">
                          KES {order.total}
                        </td>
                        <td className="px-6 py-4">
                          <span className="rounded-md bg-pink-500/10 px-3 py-1 text-xs font-medium text-pink-500">
                            {order.status}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
