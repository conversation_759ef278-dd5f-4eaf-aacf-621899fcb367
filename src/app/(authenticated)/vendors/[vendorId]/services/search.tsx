"use client";

import AsyncSelect from "react-select/async";

export default function ServiceSearch() {
  const loadOptions = async (inputValue: string) =>
    await fetch(`/api/services?s=${inputValue}`).then((res) => res.json());

  return (
    <AsyncSelect
      className="text-black"
      loadOptions={loadOptions}
      getOptionLabel={(service: Service) => service.name}
      getOptionValue={(service: Service) => service.id}
      placeholder="Search in services..."
      defaultOptions
      components={{
        IndicatorSeparator: () => null,
      }}
      classNames={{
        control: () => "w-80 !rounded-lg !py-[0.8px]",
      }}
    />
  );
}
