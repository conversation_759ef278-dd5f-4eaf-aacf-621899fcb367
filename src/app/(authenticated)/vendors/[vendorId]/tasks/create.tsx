"use client";

import { useState, ChangeEvent } from "react";
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import { toast } from "react-hot-toast";
import Image from "next/image";
import { useClickOutside } from "@/hooks/useClickOutside";
import { useParams } from "next/navigation";
import AsyncSelect from "react-select/async";

export default function AdminVendorTaskCreateForm({
  assignTask,
}: {
  assignTask: (data: FormData) => Promise<void>;
}) {
  const [creating, setCreating] = useState(false);
  const [preview, setPreview] = useState<string>();

  const ref = useClickOutside(() => setCreating(false));

  const { vendorId } = useParams();

  const {
    setValue,
    watch,
    handleSubmit,
    register,
    reset,
    control,
    formState: { errors, isSubmitting },
  } = useForm<{ tasks: string[] }>();

  const tasks = watch();

  const loadTasks = async (inputValue: string) => {
    return fetch(`/api/tasks?s=${inputValue}`).then((res) => res.json());
  };

  const createTask: SubmitHandler<{ tasks: string[] }> = ({ tasks }) => {
    const data = new FormData();

    data.append("vendorId", vendorId as string);

    tasks.forEach((task) => {
      data.append("tasks[]", task);
    });

    toast.promise(
      assignTask(data),
      {
        loading: "Assigning task...",
        success: "Task has been saved 👌",
        error: "Could not save tasks 🤯",
      },
      {
        position: "bottom-center",
      },
    );

    reset();

    setCreating(false);
  };

  return (
    <>
      <button
        onClick={() => setCreating(!creating)}
        id="createTaskButton"
        className="flex items-center justify-center rounded-lg bg-primary px-5 py-2 text-white hover:bg-default-800 dark:bg-primary"
        type="button"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="currentColor"
          className="bi bi-plus-lg h-5 w-5"
          viewBox="0 0 16 16"
        >
          <path
            fillRule="evenodd"
            d="M8 2a.5.5 0 0 1 .5.5v5h5a.5.5 0 0 1 0 1h-5v5a.5.5 0 0 1-1 0v-5h-5a.5.5 0 0 1 0-1h5v-5A.5.5 0 0 1 8 2Z"
          />
        </svg>

        <span>Assign tasks</span>
      </button>

      {creating && (
        <div
          ref={ref}
          id="drawer-create-task-default"
          className="fixed right-0 top-0 z-40 h-screen w-full max-w-xs overflow-y-auto bg-white p-4 transition-transform dark:bg-default-800"
          tabIndex={-1}
          aria-labelledby="drawer-label"
          aria-hidden="true"
        >
          <h5
            id="drawer-label"
            className="mb-6 inline-flex items-center text-sm font-semibold uppercase text-default-500 dark:text-default-400"
          >
            New Task
          </h5>
          <button
            onClick={() => setCreating(!creating)}
            type="button"
            data-drawer-dismiss="drawer-create-task-default"
            aria-controls="drawer-create-task-default"
            className="absolute right-2.5 top-2.5 inline-flex items-center rounded-lg bg-transparent p-1.5 text-sm text-default-400 hover:bg-default-200 hover:text-default-900 dark:hover:bg-default-600 dark:hover:text-white"
          >
            <svg
              aria-hidden="true"
              className="h-5 w-5"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fillRule="evenodd"
                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                clipRule="evenodd"
              ></path>
            </svg>
            <span className="sr-only">Close menu</span>
          </button>
          <form onSubmit={handleSubmit(createTask)}>
            <div className="space-y-4">
              <div>
                <label
                  htmlFor="name"
                  className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
                >
                  Tasks
                </label>
                <Controller
                  name="tasks"
                  control={control}
                  render={({ field }) => (
                    <AsyncSelect
                      isMulti
                      cacheOptions
                      defaultOptions
                      loadOptions={loadTasks}
                      getOptionLabel={(task: Task) => task.name}
                      getOptionValue={(task: Task) => task.id}
                      placeholder="Search in tasks..."
                      onChange={(v) =>
                        setValue(
                          "tasks",
                          v.map((task) => task.id),
                        )
                      }
                      components={{
                        IndicatorSeparator: () => null,
                      }}
                      classNames={{
                        control: () => "w-full !rounded-lg !py-[0.8px]",
                      }}
                    />
                  )}
                />
              </div>

              <div className="bottom-0 left-0 flex w-full justify-center space-x-4 pb-4 md:absolute md:px-4">
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full justify-center rounded-lg bg-primary px-5 py-3 text-center text-sm font-medium text-white hover:bg-default-800 focus:outline-none focus:ring-4 focus:ring-default-300 dark:bg-primary dark:hover:bg-primary dark:focus:ring-default-800"
                >
                  Save task details
                </button>
              </div>
            </div>
          </form>
        </div>
      )}
    </>
  );
}
