// /vendors/:vendorId/tasks/page.tsx

import PaginatedTable from "@/components/table";
import { api, imagePath } from "@/lib/api";
import { Metadata } from "next";
import Link from "next/link";
import { cache } from "react";
import TaskStatus from "./status";
import { revalidatePath } from "next/cache";
import Image from "next/image";
import TaskSearch from "./search";
import { auth } from "@/auth";
import AdminVendorTaskCreateForm from "./create";
import BackBtn from "@/components/back";
import VendorTaskTable from "@/components/tables/vendor-task-table";

export const dynamic = "force-dynamic";
export const fetchCache = "force-no-store";

const fetchTasks = cache(
  (vendorId?: string, searchParams?: Record<string, string>) =>
    api.get<PaginatedData<Task>>(`vendors/${vendorId}/tasks`, searchParams),
);

export const generateMetadata = async ({
  params,
}: {
  params: { vendorId: string };
}): Promise<Metadata> => {
  const vendor = await api.get<Vendor>(`vendors/${params.vendorId}`);

  return {
    title: vendor ? `${vendor.name} Tasks` : "Tasks",
    description: vendor ? `${vendor.name}'s Tasks` : "Tasks",
    keywords: ["task", vendor?.name || "vendor"],
  };
};

export default async function page({
  params,
  searchParams,
}: {
  params: { vendorId: string };
  searchParams: Record<string, string>;
}) {
  const session = await auth();


  const tasks = await fetchTasks(params.vendorId, searchParams);

  // Toggle task status (activate/deactivate)
  const toggleTaskStatus = async (task: Task) => {
    "use server";

    try {
      await api.put(`tasks/${task.id}`, { active: !task.active });
      revalidatePath(`/vendors/${params.vendorId}/tasks`);
    } catch (error) {
      console.error("Error toggling task status:", error);
    }
  };

  // Assign new tasks to vendor
  const assignTask = async (data: FormData) => {
    "use server";

    try {
      await api.post(`vendors/${params.vendorId}/tasks`, data);
      revalidatePath(`/vendors/${params.vendorId}/tasks`);
    } catch (error) {
      console.error("Error assigning task:", error);
    }
  };

  // Delete task
  const deleteTask = async (formData: FormData) => {
    "use server";

    const taskId = formData.get("id")?.toString();
    
    if (!taskId) {
      throw new Error("Task ID is required to delete a task.");
    }

    try {
      await api.destroy(taskId, `tasks`);
      revalidatePath(`/vendors/${params.vendorId}/tasks`);
    } catch (error) {
      console.error("Error deleting task:", error);
    }
  };

  return (
    <div className="p-5">
    {/* Button container aligned to the right */}
    <div className="flex justify-end mb-4">
      <AdminVendorTaskCreateForm assignTask={assignTask} />
    </div>
    
    {tasks && (
      <VendorTaskTable
        data={tasks.data}
        action={deleteTask}
        toggle={toggleTaskStatus}
        assign={assignTask}
        meta={tasks.meta}
        session={session}
      />
    )}
  </div>
    );
}
