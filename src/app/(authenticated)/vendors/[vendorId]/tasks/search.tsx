"use client";

import AsyncSelect from "react-select/async";

export default function TaskSearch() {
  const loadOptions = async (inputValue: string) =>
    await fetch(`/api/tasks?s=${inputValue}`).then((res) => res.json());

  return (
    <AsyncSelect
      className="text-black"
      loadOptions={loadOptions}
      getOptionLabel={(task: Task) => task.name}
      getOptionValue={(task: Task) => task.id}
      placeholder="Search in tasks..."
      defaultOptions
      components={{
        IndicatorSeparator: () => null,
      }}
      classNames={{
        control: () => "w-80 !rounded-lg !py-[0.8px]",
      }}
    />
  );
}
