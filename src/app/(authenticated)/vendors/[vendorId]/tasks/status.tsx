"use client";

import { useState } from "react";
import { toast } from "react-hot-toast";

export default function TaskStatus({
  task,
  toggleTaskStatus,
}: {
  task: Task;
  toggleTaskStatus: (task: Task) => Promise<void>;
}) {
  const [isActive, setIsActive] = useState(task.active);

  const toggleStatus = async () => {
    setIsActive(!isActive);

    toast.promise(toggleTaskStatus(task), {
      loading: "Updating task status...",
      success: "Task status updated!",
      error: "Failed to update task status",
    });
  };

  return (
    <label
      className="inline-flex cursor-pointer items-center"
      htmlFor={task.id}
    >
      <input
        type="checkbox"
        checked={isActive}
        className="peer sr-only"
        onChange={toggleStatus}
        id={task.id}
      />

      <div className="peer relative h-6 w-11 rounded-full bg-default-200 after:absolute after:start-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-default-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-primary peer-checked:after:translate-x-full peer-checked:after:border-white peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:border-default-600 dark:bg-default-700 dark:peer-focus:ring-primary rtl:peer-checked:after:-translate-x-full"></div>
    </label>
  );
}
