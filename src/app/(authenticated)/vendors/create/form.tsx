"use client";

import { ChangeEvent, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import toast from "react-hot-toast";
import PersonalInfoForm from "./forms/personal";
import AsyncSelect from "react-select/async";
import { useRouter, useSearchParams } from "next/navigation";
import VendorInfoFrom from "./forms/vendor";
import { Tabs, Tab } from "@nextui-org/react";
import { storeUser } from "@/actions/users";
import { storeVendor, storeVendorGrouping, storeVendorSpecialization } from "@/actions/vendors";
import { api } from "@/lib/api";

export default function VendorCreateForms({ tasks }: { tasks?: Task[] }) {
  const router = useRouter();

  const steps: Record<string, string> = {
    personal: "Admin user",
    business: "Vendor details",
    grouping: "Tasks & services",
    specialization: "Specialization",
  };

  const [step, setStep] = useState<any>("personal");
  const [vendorValue, setVendorValue] = useState<any>();
  const [services, setServices] = useState<Service[]>([]);

  const fetchVendorServices = async (vendorId: string) => {
    const res: Service[] = await fetch(
      `/api/vendors/${vendorId}/services`,
    ).then((r) => r.json());

    setServices(res);
  };

  const fetchSpecialities = async (s: string) => {
    if (s.length > 3) {
      const specialities: Speciality[] = await fetch(
        `/api/specialities?s=${s}`,
      ).then((r) => r.json());

      return specialities;
    }
  };

  const {
    register: registerGrouping,
    handleSubmit: handleGroupingSubmit,
    watch: watchGrouping,
    control: controlGrouping,
    formState: { errors: groupingErrors },
    setValue: setGroupingValue,
  } = useForm<{ tasks: string[]; services: string[]; vendorId: string }>({
    defaultValues: {
      tasks: [],
      services: [],
      vendorId: "",
    },
  });

  const grouping = watchGrouping();

  const {
    register: registerSpecialization,
    handleSubmit: handleSpecializationSubmit,
    watch: watchSpecialization,
    control: controlSpecialization,
    formState: { errors: specializationErrors },
    setValue: setSpecializationValue,
  } = useForm<{
    specialization: string[];
    vendorId: string;
    serviceId: string;
  }>({
    defaultValues: {
      specialization: [],
      vendorId: "",
      serviceId: "",
    },
  });

  const submitUser = (data: User) => {
    const updatedData = {
      ...data,
      role: "customer",
    };
    toast
      .promise(storeUser(updatedData), {
        loading: "Saving...",
        success: "User saved successfully",
        error: "Failed to save user",
      })
      .then((res) => {
        if (res && res.id) {
          setVendorValue({
            userId: res.id,
            branch: { identifier: res.phone },
          });

          setStep("business");
        }
      });
  };

  const addUserRole = async (userId: string, role: string) => {
    await api.post("/auth/addroletouser", {
      user_id: userId,
      role,
    });
  };


  const submitVendor = async (
    payload: Vendor & {
      vendorTypeId: string;
      logoUpload: File;
      coverUpload: File;
      branch: Branch & { upload: File };
    },
  ) => {
    const data = new FormData();

    data.append("name", payload.name);
    data.append("slug", payload.slug);
    data.append("email", payload.email);
    data.append("phone", payload.phone);
    data.append("kra", payload.kra);
    data.append("reg", payload.reg);
    data.append("vendorTypeId", payload.vendorTypeId);
    data.append("userId", vendorValue.userId);

    if (payload.logoUpload) {
      data.append("logo", payload.logoUpload);
    }

    if (payload.coverUpload) {
      data.append("cover", payload.coverUpload);
    }

    data.append("branch[name]", payload.branch.name);
    data.append("branch[details]", payload.branch.details);
    data.append("branch[email]", payload.branch.email);
    data.append("branch[phone]", payload.branch.phone);
    data.append("branch[identifier]", vendorValue.branch.identifier);

    if (payload.branch.upload) {
      data.append("branch[image]", payload.branch.upload);
    }
    data.append("branch[location][name]", payload.branch.location?.name as string);
    data.append("branch[location][address]", payload.branch.location?.address as string);
    data.append("branch[location][place_id]", payload.branch.location?.place_id as string);
    data.append("branch[location][coordinates][lat]", payload.branch.location?.coordinates?.lat as string);
    data.append("branch[location][coordinates][lng]", payload.branch.location?.coordinates?.lng as string);

    const regions = payload.branch.location?.regions || {};
    Object.keys(regions).forEach((key: string) => {
      data.append(`branch[location][regions][${key}]`, regions[key] as string);
    });

    toast
      .promise(storeVendor(data), {
        loading: "Saving vendor...",
        success: "Vendor saved successfully",
        error: "Failed to save vendor",
      })
      .then(async (res) => {
        if (res && res.id) {
          setGroupingValue("vendorId", res.id);
          await addUserRole(vendorValue.userId, "vendor"); // Assign "vendor" role to the user
          setStep("grouping");
        }
      });
  };


  const submitGrouping = (data: {
    tasks: string[];
    services: string[];
    vendorId: string;
  }) => {
    toast
      .promise(storeVendorGrouping(data), {
        loading: "Saving...",
        success: "Grouping saved successfully",
        error: "Failed to save grouping",
      })
      .then(async () => {
        await fetchVendorServices(data.vendorId);

        setSpecializationValue("vendorId", data.vendorId);
        setStep("specialization");
      });
  };

  const submitSpecialization = (data: {
    specialization: string[];
    vendorId: string;
    serviceId: string;
  }) => {
    toast
      .promise(storeVendorSpecialization(data), {
        loading: "Saving Specialization...",
        success: "Specialization Saved",
        error: "Error Saving Specialization",
      })
      .then(() => {
        router.push(`/vendors/${data.vendorId}`);
      });
  };

  return (
    <Tabs
      aria-label="Options"
      size="lg"
      selectedKey={step}
      onSelectionChange={setStep}
    >
      <Tab key="personal" title="Admin account">
        <PersonalInfoForm onSubmit={submitUser} />
      </Tab>
      <Tab key="business" title="Vendor details">
        <VendorInfoFrom
          defaultValues={vendorValue}
          onSubmit={submitVendor}
        />
      </Tab>
      <Tab key="grouping" title="Tasks & services">
        <form
          className=""
          role="tabpanel"
          onSubmit={handleGroupingSubmit(submitGrouping)}
        >
          <div className="mb-6 grid grid-cols-3 gap-5">
            {tasks?.map((task) => {
              return (
                <div key={task.id}>
                  <label className="inline-flex cursor-pointer items-center">
                    <input
                      type="checkbox"
                      placeholder="Enter Your Bank Name"
                      {...registerGrouping("tasks")}
                      id={task.id}
                      value={task.id}
                      className="peer sr-only"
                    />
                    <div className="peer relative h-6 w-11 rounded-full bg-default-200 after:absolute after:start-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-primary after:bg-white after:transition-all after:content-[''] peer-checked:bg-primary peer-checked:after:translate-x-full peer-checked:after:border-white peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:border-default-600 dark:bg-default-700 dark:peer-focus:ring-primary rtl:peer-checked:after:-translate-x-full"></div>
                    <span className="ms-3 text-sm font-medium text-default-900 dark:text-primary">
                      {task.name}
                    </span>
                  </label>

                  {grouping.tasks.includes(task.id) && (
                    <table className="my-4 table w-full">
                      <tbody>
                        {task.services.map((service) => {
                          return (
                            <tr key={service.id} className="border-b">
                              <td className="py-2 text-left text-sm">
                                <label htmlFor={service.id}>
                                  {service.name}
                                </label>
                              </td>
                              <td>
                                <label
                                  role="button"
                                  className={"flex items-center rounded-lg "}
                                >
                                  <input
                                    type="checkbox"
                                    placeholder="Enter Your Bank Name"
                                    {...registerGrouping("services")}
                                    id={service.id}
                                    value={service.id}
                                    className="h-5 w-full rounded-lg border border-default-200 px-4 py-4 after:bg-primary dark:bg-default-50"
                                  />
                                </label>
                              </td>
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  )}
                </div>
              );
            })}
          </div>

          <div className="flex flex-wrap justify-end gap-4">
            <button
              type="reset"
              className="flex items-center justify-center gap-2 rounded-lg bg-red-500/10 px-6 py-3 text-center text-sm font-semibold text-red-500 shadow-sm transition-colors duration-200 hover:bg-red-500 hover:text-white"
            >
              <svg
                stroke="currentColor"
                fill="none"
                strokeWidth="2"
                viewBox="0 0 24 24"
                strokeLinecap="round"
                strokeLinejoin="round"
                height="20"
                width="20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="m7 21-4.3-4.3c-1-1-1-2.5 0-3.4l9.6-9.6c1-1 2.5-1 3.4 0l5.6 5.6c1 1 1 2.5 0 3.4L13 21"></path>
                <path d="M22 21H7"></path>
                <path d="m5 11 9 9"></path>
              </svg>
              Clear
            </button>
            <button
              type="submit"
              className="flex items-center justify-center gap-2 rounded-lg bg-primary px-6 py-3 text-center text-sm font-semibold text-white shadow-sm transition-all duration-200 hover:bg-default-500"
            >
              <svg
                stroke="currentColor"
                fill="none"
                strokeWidth="2"
                viewBox="0 0 24 24"
                strokeLinecap="round"
                strokeLinejoin="round"
                height="20"
                width="20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                <polyline points="17 21 17 13 7 13 7 21"></polyline>
                <polyline points="7 3 7 8 15 8"></polyline>
              </svg>
              Save
            </button>
          </div>
        </form>
      </Tab>
      <Tab key="specialization" title="Specialization">
        <form
          id="tabSpecialDetail"
          className="space-y-4"
          role="tabpanel"
          onSubmit={handleSpecializationSubmit(submitSpecialization)}
        >
          <div className="space-y-2">
            <label className="block">Select flagship service</label>
            <div className="mb-6 grid grid-cols-3 gap-5">
              {services?.map((service) => {
                return (
                  <div key={service.id}>
                    <label className="inline-flex cursor-pointer items-center">
                      <input
                        type="radio"
                        placeholder="Enter Your Bank Name"
                        {...registerSpecialization("serviceId")}
                        id={service.id}
                        value={service.id}
                        className="peer sr-only"
                      />
                      <div className="peer relative h-6 w-11 rounded-full bg-default-200 after:absolute after:start-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-primary after:bg-white after:transition-all after:content-[''] peer-checked:bg-primary peer-checked:after:translate-x-full peer-checked:after:border-white peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:border-default-600 dark:bg-default-700 dark:peer-focus:ring-primary rtl:peer-checked:after:-translate-x-full"></div>
                      <span className="ms-3 text-sm font-medium text-default-900 dark:text-primary">
                        {service.name}
                      </span>
                    </label>
                  </div>
                );
              })}
            </div>
          </div>

          <div className="space-y-2">
            <label className="block">Select specializations</label>
            <div className="space-y-2">
              <AsyncSelect
                isClearable
                isMulti
                cacheOptions
                isSearchable
                // @ts-ignore
                loadOptions={fetchSpecialities}
                getOptionLabel={(option: Speciality) => option.name}
                getOptionValue={(option: Speciality) => option.id}
                placeholder="Enter Specialization"
                defaultOptions
                components={{
                  IndicatorSeparator: () => null,
                }}
                classNames={{
                  container: () => "w-full",
                  control: () =>
                    "!py-[6px] !border !border-primary !rounded-lg",
                }}
                onChange={(v) =>
                  setSpecializationValue(
                    "specialization",
                    v.map((v) => v.id),
                  )
                }
              />
            </div>
          </div>

          <div className="flex flex-wrap justify-end gap-4">
            <button
              type="reset"
              className="flex items-center justify-center gap-2 rounded-lg bg-red-500/10 px-6 py-3 text-center text-sm font-semibold text-red-500 shadow-sm transition-colors duration-200 hover:bg-red-500 hover:text-white"
            >
              <svg
                stroke="currentColor"
                fill="none"
                strokeWidth="2"
                viewBox="0 0 24 24"
                strokeLinecap="round"
                strokeLinejoin="round"
                height="20"
                width="20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="m7 21-4.3-4.3c-1-1-1-2.5 0-3.4l9.6-9.6c1-1 2.5-1 3.4 0l5.6 5.6c1 1 1 2.5 0 3.4L13 21"></path>
                <path d="M22 21H7"></path>
                <path d="m5 11 9 9"></path>
              </svg>
              Clear
            </button>
            <button
              type="submit"
              className="flex items-center justify-center gap-2 rounded-lg bg-primary px-6 py-3 text-center text-sm font-semibold text-white shadow-sm transition-all duration-200 hover:bg-default-500"
            >
              <svg
                stroke="currentColor"
                fill="none"
                strokeWidth="2"
                viewBox="0 0 24 24"
                strokeLinecap="round"
                strokeLinejoin="round"
                height="20"
                width="20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                <polyline points="17 21 17 13 7 13 7 21"></polyline>
                <polyline points="7 3 7 8 15 8"></polyline>
              </svg>
              Save
            </button>
          </div>
        </form>
      </Tab>
    </Tabs>
  );
}
