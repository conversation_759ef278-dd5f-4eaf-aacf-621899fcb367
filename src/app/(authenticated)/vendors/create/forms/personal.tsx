"use client";

import { searchUsers } from "@/actions/vendors";
import { Submit<PERSON><PERSON><PERSON>, use<PERSON><PERSON>, Controller } from "react-hook-form";
import PhoneInput from "react-phone-number-input";
import AsyncSelect from "react-select/async";

export default function PersonalInfoForm({
  onSubmit,
}: {
  onSubmit: SubmitHandler<User>;
}) {
  const {
    handleSubmit: handleUserSubmit,
    register,
    setValue,
    reset,
    watch: watch,
    control,
    formState: { errors: userErrors },
  } = useForm<User>({
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
    },
  });

  const user = watch();

  const loadUsers = async (s: string) => {
    if (s.length > 3) {
      const res = await searchUsers(s);

      return res?.data ?? [];
    }

    return [];
  };

  return (
    <form
      id="tabPersonalDetail"
      role="tabpanel"
      onSubmit={handleUserSubmit(onSubmit)}
    >
      <div className="mb-6 grid gap-6 lg:grid-cols-2">
        <div className="relative max-w-full">
          <label
            className="mb-2 block text-sm font-medium text-default-900"
            htmlFor="fName"
          >
            Search for existing user
          </label>
          <div className="relative max-w-full">
            <AsyncSelect
              isClearable
              isSearchable
              // cacheOptions
              //@ts-ignore
              loadOptions={loadUsers}
              placeholder="Type to search for existing user"
              getOptionLabel={(user: User) => `${user.name} (${user.email})`}
              getOptionValue={(user: User) => user.id}
              onChange={(user: any) => {
                setValue('firstName', user?.firstName);
                setValue('lastName', user?.lastName);
                setValue('email', user?.email);
                setValue('phone', user?.phone);
                setValue('idpass', user?.idpass);
                setValue('dob', user?.dob);
              }}
              components={{
                IndicatorSeparator: () => null,
              }}
              classNames={{
                control: () =>
                  "!py-[6px] !border !border-default-200 !rounded-lg",
                menu: () => "py-1",
                placeholder: () => "!text-gray-400",
              }}
            />
          </div>
        </div>

        <div className="relative max-w-full">
          <label
            className="mb-2 block text-sm font-medium text-default-900"
            htmlFor="fName"
          >
            First Name
          </label>
          <div className="relative max-w-full">
            <input
              type="text"
              placeholder="Enter First Name"
              {...register("firstName", {
                required: true,
              })}
              className="form-input w-full rounded-lg border border-default-200 px-4 py-3 dark:bg-default-50"
            />
          </div>
        </div>
        <div className="relative max-w-full">
          <label
            className="mb-2 block text-sm font-medium text-default-900"
            htmlFor="lName"
          >
            Last Name
          </label>
          <div className="relative max-w-full">
            <input
              type="text"
              placeholder="Enter Last Name"
              {...register("lastName", {
                required: true,
              })}
              className="form-input w-full rounded-lg border border-default-200 px-4 py-3 dark:bg-default-50"
            />
          </div>
        </div>
        <div className="relative max-w-full">
          <label
            className="mb-2 block text-sm font-medium text-default-900"
            htmlFor="contactNO"
          >
            Phone Number
          </label>
          <div className="relative max-w-full form-input w-full rounded-lg border border-default-200 px-4 py-3 bg-white dark:bg-default-50">
            <Controller
              name="phone"
              control={control}
              render={({ field: { onChange, value } }) => (
                <PhoneInput
                  placeholder="Enter phone number"
                  value={value}
                  onChange={onChange}
                  defaultCountry="KE"
                />
              )}
            />
          </div>
        </div>

        <div className="relative max-w-full">
          <label
            className="mb-2 block text-sm font-medium text-default-900"
            htmlFor="email"
          >
            Email address
          </label>
          <div className="relative max-w-full">
            <input
              type="email"
              placeholder="Enter Email"
              {...register("email", {
                required: true,
              })}
              name="email"
              id="email"
              className="form-input w-full rounded-lg border border-default-200 px-4 py-3 dark:bg-default-50"
            />
          </div>
        </div>

        <div className="relative max-w-full">
          <label
            className="mb-2 block text-sm font-medium text-default-900"
            htmlFor="panNo"
          >
            ID/Passport Number
          </label>
          <div className="relative max-w-full">
            <input
              type="text"
              {...register("idpass", {
                required: false,
              })}
              placeholder="Enter ID/Passport Number"
              className="form-input w-full rounded-lg border border-default-200 px-4 py-3 dark:bg-default-50"
            />
          </div>
        </div>

<div className="relative max-w-full">
  <label
    className="mb-2 block text-sm font-medium text-default-900"
    htmlFor="title"
  >
    Title
  </label>
  <div className="relative max-w-full">
    <input
      type="text"
      {...register("title", {
        required: false,
      })}
      placeholder="Eg Mr/Mrs/Miss"
      className="form-input w-full rounded-lg border border-default-200 px-4 py-3 dark:bg-default-50"
    />
  </div>
</div>

<div className="relative max-w-full">
  <label
    className="mb-2 block text-sm font-medium text-default-900"
    htmlFor="title"
  >
    Date of Birth
  </label>
  <div className="relative max-w-full">
    <input
      type="date"
      {...register("dob")}
      placeholder="Eg Mr/Mrs/Miss"
      className="form-input w-full rounded-lg border border-default-200 px-4 py-3 dark:bg-default-50"
    />
  </div>
</div>
      </div>

      <div className="flex flex-wrap justify-end gap-4">
        <button
          type="reset"
          className="flex items-center justify-center gap-2 rounded-lg bg-red-500/10 px-6 py-3 text-center text-sm font-semibold text-red-500 shadow-sm transition-colors duration-200 hover:bg-red-500 hover:text-white"
        >
          <svg
            stroke="currentColor"
            fill="none"
            strokeWidth="2"
            viewBox="0 0 24 24"
            strokeLinecap="round"
            strokeLinejoin="round"
            height="20"
            width="20"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="m7 21-4.3-4.3c-1-1-1-2.5 0-3.4l9.6-9.6c1-1 2.5-1 3.4 0l5.6 5.6c1 1 1 2.5 0 3.4L13 21"></path>
            <path d="M22 21H7"></path>
            <path d="m5 11 9 9"></path>
          </svg>
          Clear
        </button>
        <button
          type="submit"
          className="flex items-center justify-center gap-2 rounded-lg bg-primary px-6 py-3 text-center text-sm font-semibold text-white shadow-sm transition-colors duration-200 hover:bg-default-500"
        >
          <svg
            stroke="currentColor"
            fill="none"
            strokeWidth="2"
            viewBox="0 0 24 24"
            strokeLinecap="round"
            strokeLinejoin="round"
            height="20"
            width="20"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
            <polyline points="17 21 17 13 7 13 7 21"></polyline>
            <polyline points="7 3 7 8 15 8"></polyline>
          </svg>
          Create vendor admin
        </button>
      </div>
    </form>
  );
}
