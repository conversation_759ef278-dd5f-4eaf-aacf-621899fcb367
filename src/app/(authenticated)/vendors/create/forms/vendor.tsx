"use client";

import { ChangeEvent, useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import AsyncSelect from "react-select/async";
import Image from "next/image";
import GooglePlacesAutocomplete, {
  geocodeByPlaceId,
} from "react-google-places-autocomplete";
import makeAnimated from "react-select/animated";
import { useSearchParams } from "next/navigation";
import PhoneInput from "react-phone-number-input";
import { Tabs, Tab } from "@nextui-org/tabs";
import { fetchVendorTypes } from "@/actions/vendors";

export default function VendorInfoFrom({
  defaultValues,
  onSubmit,
}: {
  defaultValues: any;
  onSubmit: (data: any) => Promise<void>;
}) {
  const searchParams = useSearchParams();

  const animatedComponents = makeAnimated();

  const [preview, setPreview] = useState<string>();
  const [vendorTypes, setVendorTypes] = useState<VendorType[]>([]);
  const [vendorLogoPreview, setVendorLogoPreview] = useState<string>();
  const [vendorCoverPreview, setVendorCoverPreview] = useState<string>();
  const [results, setResults] = useState<any>();
  const [selectedTab, setSelectedTab] = useState<any>("vendor");

  const {
    handleSubmit: handleSubmit,
    register: register,
    reset: resetVendor,
    setValue: setVendorValue,
    formState: { errors: vendorErrors },
    control,
    watch,
  } = useForm<
    Vendor & {
      vendorTypeId: string;
      logoUpload: File;
      coverUpload: File;
      branch: Branch & { upload: File; identifier?: string };
    }
  >({
    defaultValues: {
      ...defaultValues,
      ...{
        name: "",
        vendorCategoryId: searchParams.get("vendorCategoryId") || "",
        email: "",
        phone: "",
        userId: "",
        kra: "",
        reg: "",
      },
    },
  });

  const onboard = watch();

  const fetchVendorCategories = async (s: string) => {
    if (s.length > 3) {
      const categories: VendorCategory[] = await fetch(
        `/api/vendor-types/${onboard.vendorTypeId}/categories?s=${s}`,
      ).then((r) => r.json());
      return categories;
    }

    return [];
  };

  const handleLogo = (event: ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;

    if (files) {
      const file = files[0];
      setVendorValue("logoUpload", file);
      const urlImage = URL.createObjectURL(file);

      setVendorLogoPreview(urlImage);
    }
  };

  const handleCover = (event: ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;

    if (files) {
      const file = files[0];
      setVendorValue("coverUpload", file);
      const urlImage = URL.createObjectURL(file);

      setVendorCoverPreview(urlImage);
    }
  };

  const handleBranchImage = (event: ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;

    if (files) {
      const file = files[0];
      setVendorValue("branch.upload", file);
      const urlImage = URL.createObjectURL(file);

      setPreview(urlImage);
    }
  };

  const setPlace = ({ value }: any) => {
    geocodeByPlaceId(value.place_id)
      .then((places) => {
        const place = places[0];
        setVendorValue("branch.location", {
          // name: place.label,
          address: place.formatted_address,
          coordinates: {
            lat: place.geometry?.location?.lat(),
            lng: place.geometry?.location?.lng(),
          },
          regions: place.address_components?.reduce((acc: any, curr: any) => {
            acc[curr.types[0]] = curr.long_name;
            return acc;
          }, {}),
          place_id: value.place_id,
        });
      })
      .catch((error) => console.error(error));
  };

  useEffect(() => {
    fetchVendorTypes().then((res) => {
      if (res) {
        setVendorTypes(res.data);
      }
    });
  }, []);

  return (
    <form
      id="tabBusinessDetail"
      className=""
      role="tabpanel"
      onSubmit={handleSubmit(onSubmit)}
    >
      <Tabs
        aria-label="Options"
        size="lg"
        selectedKey={selectedTab}
        onSelectionChange={setSelectedTab}
      >
        <Tab key="vendor" title="Vendor details">
          <div className="mb-6 grid gap-6 lg:grid-cols-3">
            <div className="relative max-w-full">
              <label
                className="mb-2 block text-sm font-medium text-default-900"
                htmlFor="businessName"
              >
                Business Name
              </label>
              <div className="relative max-w-full">
                <input
                  type="text"
                  placeholder="Enter Your Business Name"
                  {...register("name", {
                    required: true,
                  })}
                  id="businessName"
                  className="form-input w-full rounded-lg border border-default-200 px-4 py-3 dark:bg-default-50"
                />
              </div>
            </div>

            {/* slug */}
            <div className="relative max-w-full">
              <label
                className="mb-2 block text-sm font-medium text-default-900"
                htmlFor="businessSlug"
              >
                Business Slug
              </label>
              <div className="relative max-w-full">
                <input
                  type="text"
                  placeholder="Enter Your Business Slug"
                  {...register("slug", {
                    required: true,
                  })}
                  id="businessSlug"
                  className="form-input w-full rounded-lg border border-default-200 px-4 py-3 dark:bg-default-50"
                />
              </div>
            </div>

            <div className="relative max-w-full">
              <label
                className="mb-2 block text-sm font-medium text-default-900"
                htmlFor="businessType"
              >
                Business Registration number
              </label>
              <div className="relative max-w-full">
                <input
                  type="text"
                  placeholder="Enter Your Business reg no"
                  {...register("reg", {
                    required: true,
                  })}
                  className="form-input w-full rounded-lg border border-default-200 px-4 py-3 dark:bg-default-50"
                />
              </div>
            </div>

            <div className="relative max-w-full">
              <label
                className="mb-2 block text-sm font-medium text-default-900"
                htmlFor="businessType"
              >
                Business KRA
              </label>
              <div className="relative max-w-full">
                <input
                  type="text"
                  placeholder="Enter Your Business reg no"
                  {...register("kra", {
                    required: true,
                  })}
                  className="form-input w-full rounded-lg border border-default-200 px-4 py-3 dark:bg-default-50"
                />
              </div>
            </div>
            <div className="relative max-w-full">
              <label
                className="mb-2 block text-sm font-medium text-default-900"
                htmlFor="contactNO"
              >
                Contact Number
              </label>
              <div className="form-input relative w-full max-w-full rounded-lg border border-default-200 bg-white px-4 py-3 dark:bg-default-50">
                <Controller
                  name="phone"
                  control={control}
                  render={({ field: { onChange, value } }) => (
                    <PhoneInput
                      placeholder="Enter phone number"
                      value={value}
                      onChange={onChange}
                      defaultCountry="KE"
                    />
                  )}
                />
              </div>
            </div>
            <div className="relative max-w-full">
              <label
                className="mb-2 block text-sm font-medium text-default-900"
                htmlFor="gstNo"
              >
                Email
              </label>
              <div className="relative max-w-full">
                <input
                  type="text"
                  placeholder="Enter Your email"
                  {...register("email", {
                    required: true,
                  })}
                  className="form-input w-full rounded-lg border border-default-200 px-4 py-3 dark:bg-default-50"
                />
              </div>
            </div>

            {!searchParams.get("vendorCategoryId") && (
              <div className="col-span-2 lg:col-span-3">
                <div className="relative max-w-full">
                  <label
                    className="mb-2 block text-sm font-medium text-default-900"
                    htmlFor="gstNo"
                  >
                    Select vendor grouping
                  </label>

                  <div className="grid grid-cols-4">
                    {vendorTypes.map((vendorType) => (
                      <div key={vendorType.id}>
                        <label className="inline-flex cursor-pointer items-center">
                          <input
                            type="radio"
                            placeholder="Enter Your Bank Name"
                            id={vendorType.id}
                            value={vendorType.id}
                            className="peer sr-only"
                            {...register("vendorTypeId")}
                            defaultChecked={
                              onboard.vendorTypeId === vendorType.id
                            }
                          />
                          <div className="peer relative h-6 w-11 rounded-full bg-default-200 after:absolute after:start-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-primary after:bg-white after:transition-all after:content-[''] peer-checked:bg-primary peer-checked:after:translate-x-full peer-checked:after:border-white peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:border-default-600 dark:bg-default-700 dark:peer-focus:ring-primary rtl:peer-checked:after:-translate-x-full"></div>
                          <span className="ms-3 text-sm font-medium text-default-900 dark:text-primary">
                            {vendorType.name}
                          </span>
                        </label>

                        {onboard.vendorTypeId === vendorType.id && (
                          <table className="my-4 table w-full">
                            <tbody>
                              {vendorType.vendorCategories.map((category) => {
                                return (
                                  <tr
                                    key={category.id}
                                    className="border border-t-0"
                                  >
                                    <td className="p-2 text-left text-sm">
                                      <label className="inline-flex cursor-pointer items-center">
                                        <input
                                          type="radio"
                                          placeholder="Enter Your Bank Name"
                                          className="peer sr-only"
                                          id={category.id}
                                          value={category.id}
                                          defaultChecked={
                                            onboard.vendorCategoryId ===
                                            category.id
                                          }
                                          {...register("vendorCategoryId", {
                                            required: true,
                                          })}
                                        />
                                        <div className="peer relative h-6 w-11 rounded-full bg-default-200 after:absolute after:start-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-primary after:bg-white after:transition-all after:content-[''] peer-checked:bg-primary peer-checked:after:translate-x-full peer-checked:after:border-white peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:border-default-600 dark:bg-default-700 dark:peer-focus:ring-primary rtl:peer-checked:after:-translate-x-full"></div>
                                      </label>
                                    </td>
                                    <td>
                                      <label className="inline-flex cursor-pointer items-center">
                                        <span className="ms-3 text-sm font-medium text-default-900 dark:text-primary">
                                          {category.name}
                                        </span>
                                      </label>
                                    </td>
                                  </tr>
                                );
                              })}
                            </tbody>
                          </table>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            <div className="col-span-3 flex gap-5">
              <div className="flex-1">
                <label
                  htmlFor="image"
                  className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
                >
                  Vendor logo
                </label>

                <label className="flex h-80 w-full items-center justify-center rounded-lg border border-dashed border-primary bg-default-50 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600">
                  <input
                    type="file"
                    name="image"
                    id="image"
                    className="hidden"
                    onChange={handleLogo}
                  />
                  {vendorLogoPreview ? (
                    <Image
                      src={vendorLogoPreview}
                      alt="preview"
                      width={200}
                      height={200}
                      className="h-full"
                    />
                  ) : (
                    <p>Click to select file</p>
                  )}
                </label>
              </div>

              <div className="flex-1">
                <label
                  htmlFor="image"
                  className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
                >
                  Vendor cover image
                </label>

                <label className="flex h-80 w-full items-center justify-center rounded-lg border border-dashed border-primary bg-default-50 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600">
                  <input
                    type="file"
                    name="image"
                    id="image"
                    className="hidden"
                    onChange={handleCover}
                  />
                  {vendorCoverPreview ? (
                    <Image
                      src={vendorCoverPreview}
                      alt="preview"
                      width={200}
                      height={200}
                      className="h-full w-auto"
                    />
                  ) : (
                    <p>Click to select file</p>
                  )}
                </label>
              </div>
            </div>
            <div className="col-span-3 flex justify-end">
              <button
                onClickCapture={() => setSelectedTab("branch")}
                className="rounded-lg bg-primary px-6 py-2 text-white"
                onClick={() => setSelectedTab("branch")}
              >
                Add branch
              </button>
            </div>
          </div>
        </Tab>

        <Tab key="branch" title="Add a branch">
          <div className="space-y-4">
            <div className="flex space-x-3">
              <div className="flex-1">
                <label
                  htmlFor="name"
                  className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
                >
                  Branch name
                </label>
                <input
                  type="text"
                  {...register("branch.name")}
                  id="name"
                  className="block w-full rounded-lg border border-primary bg-default-50 px-4 py-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
                  placeholder="Type branch name"
                  required
                />
              </div>

              <div className="w-1/4">
                <label
                  htmlFor="name"
                  className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
                >
                  Location
                </label>

                <GooglePlacesAutocomplete
                  apiKey="AIzaSyB5XjUloDmNIRd88muXWdfztbX0sBdI3qQ"
                  selectProps={{
                    value: results,
                    onChange: setPlace,
                    classNames: {
                      control: () =>
                        "!rounded-lg !border !border-primary focus:ring-default-600 focus:border-default-600 dark:bg-default-700 dark:border-default-600 dark:placeholder-default-400 dark:text-white dark:focus:ring-default-600 dark:focus:border-default-500 py-[4px]",
                    },
                    components: {
                      IndicatorSeparator: () => null,
                    },
                    placeholder: "Start typing location",
                  }}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="col-span-2 lg:col-span-1">
                <label
                  htmlFor="eamil"
                  className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
                >
                  Email
                </label>

                <input
                  type="email"
                  {...register("branch.email")}
                  id="email"
                  className="block w-full rounded-lg border border-primary bg-default-50 px-4 py-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
                  placeholder="Type branch email"
                />
              </div>

              <div className="col-span-2 lg:col-span-1">
                <label
                  htmlFor="phone"
                  className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
                >
                  Phone
                </label>
                <div className="form-input relative w-full max-w-full rounded-lg border border-default-200 bg-white px-4 py-3 dark:bg-default-50">
                  <Controller
                    name="branch.phone"
                    control={control}
                    render={({ field: { onChange, value } }) => (
                      <PhoneInput
                        placeholder="Enter phone number"
                        value={value}
                        onChange={onChange}
                        defaultCountry="KE"
                      />
                    )}
                  />
                </div>
              </div>
            </div>

            <div className="flex gap-4">
              <div className="w-2/3">
                <label
                  htmlFor="description"
                  className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
                >
                  Description
                </label>
                <textarea
                  id="description"
                  rows={4}
                  {...register("branch.details")}
                  className="block h-60 w-full rounded-lg border border-primary bg-default-50 px-4 py-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
                  placeholder="Enter branch description here"
                />
              </div>

              <div className="w-1/3">
                <label
                  htmlFor="image"
                  className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
                >
                  Image or icon
                </label>

                <label className="flex h-60 w-full items-center justify-center rounded-lg border border-dashed border-primary bg-default-50 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600">
                  <input
                    type="file"
                    name="image"
                    id="image"
                    className="hidden"
                    onChange={handleBranchImage}
                  />
                  {preview ? (
                    <Image
                      src={preview}
                      alt="preview"
                      width={100}
                      height={100}
                      className="w-1/2"
                    />
                  ) : (
                    <p>Click to select file</p>
                  )}
                </label>
              </div>
            </div>
          </div>

          <div className="mt-4 flex flex-wrap items-center justify-between gap-4">
            <div>
              {/* Identifier */}

              <label className="mb-2 block text-sm font-medium text-default-900 dark:text-white">
                Staff ID
              </label>

              <div className="relative max-w-full">
                <input
                  type="text"
                  placeholder="Enter staff ID"
                  {...register("branch.identifier")}
                  className="form-input w-full rounded-lg border border-default-200 px-4 py-3 dark:bg-default-50"
                />
              </div>
            </div>
            <div className="flex gap-4">
              <button
                type="reset"
                className="flex items-center justify-center gap-2 rounded-lg bg-red-500/10 px-6 py-3 text-center text-sm font-semibold text-red-500 shadow-sm transition-colors duration-200 hover:bg-red-500 hover:text-white"
              >
                <svg
                  stroke="currentColor"
                  fill="none"
                  strokeWidth="2"
                  viewBox="0 0 24 24"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  height="20"
                  width="20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path d="m7 21-4.3-4.3c-1-1-1-2.5 0-3.4l9.6-9.6c1-1 2.5-1 3.4 0l5.6 5.6c1 1 1 2.5 0 3.4L13 21"></path>
                  <path d="M22 21H7"></path>
                  <path d="m5 11 9 9"></path>
                </svg>
                Clear
              </button>
              <button
                type="submit"
                className="flex items-center justify-center gap-2 rounded-lg bg-primary px-6 py-3 text-center text-sm font-semibold text-white shadow-sm transition-all duration-200 hover:bg-default-500"
              >
                <svg
                  stroke="currentColor"
                  fill="none"
                  strokeWidth="2"
                  viewBox="0 0 24 24"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  height="20"
                  width="20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                  <polyline points="17 21 17 13 7 13 7 21"></polyline>
                  <polyline points="7 3 7 8 15 8"></polyline>
                </svg>
                Save vendor details
              </button>
            </div>
          </div>
        </Tab>
      </Tabs>
    </form>
  );
}
