import { api } from "@/lib/api";
import VendorCreateForms from "./form";

export const dynamic = "force-dynamic";
export const fetchCache = "force-no-store";

export const metadata = {
  title: "Onboard a new vendor",
  description: "Create Vendor",
  keywords: ["vendor"],
};

export default async function page() {
  const tasks = await api.get<PaginatedData<Task>>("tasks");

  return (
    <div className="page-content space-y-6 p-6">
      <VendorCreateForms tasks={tasks?.data} />
    </div>
  );
}
