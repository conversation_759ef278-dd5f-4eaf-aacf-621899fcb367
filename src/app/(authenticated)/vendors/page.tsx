import VendorsTable from "@/components/tables/vendors-table";
import { api } from "@/lib/api";
import { Metadata } from "next";

export const dynamic = "force-dynamic";
export const fetchCache = "force-no-store";

export const metadata: Metadata = {
  title: "Vendors",
  description: "Vendor listing",
};

export default async function page({
  searchParams,
}: {
  searchParams?: Record<string, string>;
}) {
  let vendors
  try {
    vendors = await api.get<any>("vendors", searchParams);
  } catch (error) {
    console.log("VENDORS FETCH FAILED(error):", error)
  }

  return (
    <div className="page-content p-5">

      {vendors ? (
        <VendorsTable
          data={vendors.data}
          meta={vendors.meta}
        />
      ):<div><h1>No vendors to show</h1></div>}
    </div>
  );
}