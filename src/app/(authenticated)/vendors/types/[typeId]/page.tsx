import { api } from "@/lib/api";
import Link from "next/link";
import AdminVendorCategoryCreateForm from "../../categories/create";
import { revalidatePath } from "next/cache";
import { Metadata } from "next";
import { cache } from "react";
import PaginatedTable from "@/components/table";

const fetchType = cache((typeId: string) =>
  api.get<Task>(`vendor-types/${typeId}`),
);

export const generateMetadata = async ({
  params,
}: {
  params: { typeId: string };
}): Promise<Metadata> => {
  const type = await fetchType(params.typeId);

  return {
    title: type?.name,
    description: type?.details,
  };
};

export default async function AdminVendorTypeIndex({
  params,
}: {
  params: { typeId: string };
}) {
  const type = await fetchType(params.typeId);

  const categories = await api.get<PaginatedData<VendorCategory>>(
    `vendor-types/${params.typeId}/categories`,
  );

  const storeVendorCategory = async (vendorcategory: FormData) => {
    "use server";

    await api.post("vendor-categories", vendorcategory);

    revalidatePath(`vendors/types/${params.typeId}`);
  };

  return (
    <div className="py-4">
      {categories && (
        <PaginatedTable<VendorCategory>
          records={categories}
          title={"Categories"}
          columns={[
            {
              id: "name",
              title: "Category",
              render: (category) => (
                <Link href={`/vendors/categories/${category.id}`}>
                  {category.name}
                </Link>
              ),
            },
            { id: "details", title: "Details" },
          ]}
          path={`/vendors/types/${params.typeId}`}
          tools={
            <div className="mb-4 flex justify-between px-4">
              <p className="text-lg font-bold">{type?.details}</p>

              <AdminVendorCategoryCreateForm
                storeVendorCategory={storeVendorCategory}
                typeId={params.typeId}
              />
            </div>
          }
        />
      )}
    </div>
  );
}
