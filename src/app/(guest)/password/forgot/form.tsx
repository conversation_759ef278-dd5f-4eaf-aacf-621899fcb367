"use client";

import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import toast from "react-hot-toast";

export default function ForgotPasswordForm({
  getPasswordResetLink,
}: {
  getPasswordResetLink: (
    data: any,
  ) => Promise<{ success: boolean; message: string } | undefined>;
}) {
  const router = useRouter();
  const { register, handleSubmit } = useForm();

  const onSubmit = async (data: any) => {
    toast
      .promise(getPasswordResetLink(data), {
        loading: "Sending password reset link...",
        success: "Password reset link sent",
        error: "Failed to send password reset link",
      })
      .then((res) => {
        if (res?.success) {
          router.push("/password/reset?username=" + data.username);
        }
      });
  };
  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <div>
        <label
          className="mb-2 block text-sm font-medium text-default-900"
          htmlFor="email"
        >
          Email or phone
        </label>
        <div className="relative max-w-full">
          <input
            type="text"
            placeholder="Enter your email or phone"
            id="username"
            className="form-input w-full rounded-lg border border-default-200 bg-white px-4 py-3 dark:bg-default-50"
            {...register("username", { required: true })}
          />
        </div>
      </div>

      <button
        type="submit"
        className="mt-4 w-full rounded-lg bg-primary px-5 py-3 text-white"
      >
        Get password reset link
      </button>
    </form>
  );
}
