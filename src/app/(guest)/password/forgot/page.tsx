import { api } from "@/lib/api";
import ForgotPasswordForm from "./form";

export const dynamic = "force-dynamic";
export const fetchCache = "force-no-store";

export default async function page() {
  const getPasswordResetLink = async (data: any) => {
    "use server";
    return await api.post<{ success: boolean; message: string }>(
      "/auth/password/forgot",
      data,
    );
  };
  return (
    <div>
      <ForgotPasswordForm getPasswordResetLink={getPasswordResetLink} />
    </div>
  );
}
