"use client";

import { useRouter, useSearchParams } from "next/navigation";
import { useForm } from "react-hook-form";
import toast from "react-hot-toast";

export default function ForgotPasswordForm({
  getPasswordResetLink,
}: {
  getPasswordResetLink: (
    data: any,
  ) => Promise<{ success: boolean; message: string } | undefined>;
}) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const {
    register,
    handleSubmit,
    watch,
    formState: { errors, isSubmitting },
  } = useForm({
    defaultValues: {
      username: searchParams.get("username"),
      password: "",
      otp: "",
    },
  });

  const reset = watch();

  const onSubmit = async (data: any) => {
    toast
      .promise(getPasswordResetLink(data), {
        loading: "Resetting password reset link...",
        success: "Password reset successful",
        error: "Failed to reset password",
      })
      .then((res) => {
        if (res?.success) {
          router.push("/login");
        }
      });
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="w-full space-y-5">
      <div>
        <label
          className="mb-2 block text-sm font-medium text-default-900"
          htmlFor="email"
        >
          New password for {searchParams.get("username")}
        </label>
        <div className="relative max-w-full">
          <input
            type="password"
            placeholder="Enter new password"
            id="password"
            className="form-input w-full rounded-lg border border-default-200 bg-white px-4 py-3 dark:bg-default-50"
            {...register("password", { required: true })}
          />
        </div>
      </div>

      {/* OTP */}

      <div>
        <label
          className="mb-2 block text-sm font-medium text-default-900"
          htmlFor="otp"
        >
          One Time Pin
        </label>

        <div className="relative max-w-full">
          <input
            type="text"
            placeholder={`Enter OTP sent to ${reset.username?.includes("@") ? "email" : "phone"}`}
            id="otp"
            className="form-input w-full rounded-lg border border-default-200 bg-white px-4 py-3 dark:bg-default-50"
            {...register("otp", { required: true })}
          />
        </div>
      </div>

      <button
        type="submit"
        className="mt-4 w-full rounded-lg bg-primary px-5 py-3 text-white"
      >
        Get password reset link
      </button>
    </form>
  );
}
