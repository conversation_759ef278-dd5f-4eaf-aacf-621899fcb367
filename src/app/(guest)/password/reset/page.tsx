import { api } from "@/lib/api";
import ForgotPasswordForm from "./form";
import Link from "next/link";

export const dynamic = "force-dynamic";
export const fetchCache = "force-no-store";

export default async function page() {
  const getPasswordResetLink = async (data: any) => {
    "use server";
    return await api.post<{ success: boolean; message: string }>(
      "/auth/password/reset",
      data,
    );
  };
  return (
    <div>
      <ForgotPasswordForm getPasswordResetLink={getPasswordResetLink} />
      <div className="mt-16 flex grow items-end justify-center">
        <p className="mt-auto text-center text-default-950">
          You can now{" "}
          <Link className="ms-1 text-primary" href="/login">
            <span className="font-medium">login</span>
          </Link>
        </p>
      </div>
    </div>
  );
}
