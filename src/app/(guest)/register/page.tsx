import Image from "next/image";
import Link from "next/link";

export const dynamic = "force-dynamic";
export const fetchCache = "force-no-store";

export default async function page() {
  return (
    <>
      <div className="py-10">
        <h1 className="mb-2 text-3xl font-semibold text-default-800">
          Register
        </h1>
        <p className="max-w-md text-sm text-default-500">
          Create your account to access AppInApp.
        </p>
      </div>
      <form>
        <div className="relative mb-6 max-w-full">
          <label
            className="mb-2 block text-sm font-medium text-default-900"
            htmlFor="email"
          >
            Email
          </label>
          <div className="relative max-w-full">
            <input
              type="email"
              placeholder="Enter your email"
              name="email"
              id="email"
              className="form-input w-full rounded-lg border border-default-200 bg-white px-4 py-3 dark:bg-default-50"
              value="<EMAIL>"
            />
          </div>
        </div>
        <div className="relative mb-1 w-full">
          <label
            className="mb-2 block text-sm font-medium text-default-900"
            htmlFor="password"
          >
            Password
          </label>
          <div className="flex w-full">
            <div className="relative w-full">
              <input
                name="password"
                type="password"
                className="w-full rounded-e-none rounded-s-lg border border-default-200 px-4 py-3 focus:border-primary dark:bg-default-50"
                value="password"
              />
            </div>
            <button
              className="password-toggle ms-[1px] inline-flex items-center justify-center rounded-e-lg border border-s-0 border-default-200 bg-white px-4 py-3 dark:bg-default-50"
              type="button"
            >
              <svg
                stroke="currentColor"
                fill="none"
                strokeWidth="2"
                viewBox="0 0 24 24"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-default-600"
                height="20"
                width="20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path>
                <circle cx="12" cy="12" r="3"></circle>
              </svg>
            </button>
          </div>
        </div>
        <a
          className="float-right text-end text-sm text-default-600 underline"
          href="/yum_r/auth/forgot-password"
        >
          Forgot Password?
        </a>
        <button
          type="submit"
          className="mt-5 w-full rounded-lg bg-primary px-6 py-3 text-base capitalize text-white transition-all hover:bg-default-500"
        >
          Log In
        </button>
      </form>

      <div className="my-6 flex items-center justify-center gap-4">
        <Image
          height="32"
          width="32"
          alt="social-login-google"
          src="data:image/svg+xml,%3csvg%20width='32'%20height='32'%20viewBox='0%200%2032%2032'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20clip-path='url(%23clip0_254_2392)'%3e%3cpath%20d='M11.1477%201.0525C7.95043%202.16167%205.19309%204.26692%203.28071%207.059C1.36833%209.85109%200.401709%2013.1829%200.522836%2016.5649C0.643964%2019.947%201.84645%2023.201%203.95367%2025.8492C6.06089%2028.4973%208.96178%2030.3999%2012.2302%2031.2775C14.88%2031.9612%2017.6563%2031.9913%2020.3202%2031.365C22.7335%2030.8229%2024.9647%2029.6634%2026.7952%2028C28.7004%2026.2159%2030.0833%2023.9462%2030.7952%2021.435C31.5688%2018.7041%2031.7065%2015.8323%2031.1977%2013.04H16.3177V19.2125H24.9352C24.763%2020.197%2024.3939%2021.1366%2023.8501%2021.9751C23.3063%2022.8136%2022.5989%2023.5338%2021.7702%2024.0925C20.7181%2024.7888%2019.5318%2025.2572%2018.2877%2025.4675C17.0401%2025.6995%2015.7604%2025.6995%2014.5127%2025.4675C13.2481%2025.2063%2012.0518%2024.6844%2011.0002%2023.935C9.3106%2022.739%208.04192%2021.0398%207.37523%2019.08C6.69745%2017.0835%206.69745%2014.919%207.37523%2012.9225C7.8498%2011.523%208.63431%2010.2488%209.67023%209.195C10.8557%207.96687%2012.3566%207.08899%2014.0081%206.65769C15.6597%206.22639%2017.3981%206.25833%2019.0327%206.75C20.3097%207.14181%2021.4775%207.82669%2022.4427%208.75C23.4144%207.78333%2024.3844%206.81417%2025.3527%205.8425C25.8527%205.32%2026.3977%204.8225%2026.8902%204.2875C25.4166%202.9163%2023.6869%201.84926%2021.8002%201.1475C18.3645%20-0.100025%2014.6052%20-0.133551%2011.1477%201.0525Z'%20fill='white'/%3e%3cpath%20d='M11.1479%201.0525C14.6051%20-0.134358%2018.3644%20-0.101715%2021.8004%201.145C23.6874%201.85153%2025.4163%202.9237%2026.8879%204.3C26.3879%204.835%2025.8604%205.335%2025.3504%205.855C24.3804%206.82333%2023.4113%207.78833%2022.4429%208.75C21.4777%207.82669%2020.3099%207.14181%2019.0329%206.75C17.3989%206.2566%2015.6605%206.22282%2014.0085%206.65235C12.3565%207.08189%2010.8547%207.95815%209.66793%209.185C8.63201%2010.2388%207.8475%2011.513%207.37293%2012.9125L2.19043%208.9C4.04545%205.2214%207.2573%202.40756%2011.1479%201.0525Z'%20fill='%23E33629'/%3e%3cpath%20d='M0.814876%2012.875C1.09322%2011.4944%201.55569%2010.1575%202.18988%208.9L7.37238%2012.9225C6.6946%2014.919%206.6946%2017.0835%207.37238%2019.08C5.64571%2020.4133%203.91821%2021.7533%202.18988%2023.1C0.602752%2019.9408%200.118707%2016.3413%200.814876%2012.875Z'%20fill='%23F8BD00'/%3e%3cpath%20d='M16.3175%2013.0375H31.1975C31.7062%2015.8299%2031.5686%2018.7016%2030.795%2021.4325C30.0831%2023.9437%2028.7002%2026.2134%2026.795%2027.9975C25.1225%2026.6925%2023.4425%2025.3975%2021.77%2024.0925C22.5992%2023.5332%2023.3069%2022.8122%2023.8508%2021.9728C24.3946%2021.1334%2024.7634%2020.1929%2024.935%2019.2075H16.3175C16.315%2017.1525%2016.3175%2015.095%2016.3175%2013.0375Z'%20fill='%23587DBD'/%3e%3cpath%20d='M2.1875%2023.1C3.91583%2021.7667%205.64333%2020.4267%207.37%2019.08C8.03802%2021.0405%209.30851%2022.7397%2011%2023.935C12.0549%2024.6809%2013.2537%2025.1986%2014.52%2025.455C15.7676%2025.687%2017.0474%2025.687%2018.295%2025.455C19.5391%2025.2447%2020.7253%2024.7763%2021.7775%2024.08C23.45%2025.385%2025.13%2026.68%2026.8025%2027.985C24.9722%2029.6493%2022.741%2030.8097%2020.3275%2031.3525C17.6635%2031.9788%2014.8873%2031.9487%2012.2375%2031.265C10.1418%2030.7054%208.18419%2029.719%206.4875%2028.3675C4.69182%2026.9415%203.22509%2025.1448%202.1875%2023.1Z'%20fill='%23319F43'/%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='clip0_254_2392'%3e%3crect%20width='32'%20height='32'%20fill='white'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e"
          className="h-8 w-8"
        />
        <Image
          height="32"
          width="32"
          alt="social-login-facebook"
          src="data:image/svg+xml,%3csvg%20width='32'%20height='32'%20viewBox='0%200%2032%2032'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20clip-path='url(%23clip0_254_2398)'%3e%3cpath%20d='M32%2016C32%207.1635%2024.8365%200%2016%200C7.1635%200%200%207.16337%200%2016C0%2023.986%205.851%2030.6054%2013.5%2031.8056V20.625H9.4375V16H13.5V12.475C13.5%208.465%2015.8888%206.25%2019.5434%206.25C21.294%206.25%2023.125%206.5625%2023.125%206.5625V10.5H21.1075C19.1198%2010.5%2018.5%2011.7334%2018.5%2012.9987V16H22.9375L22.2281%2020.625H18.5V31.8056C26.149%2030.6054%2032%2023.9861%2032%2016Z'%20fill='%231877F2'/%3e%3cpath%20d='M22.2281%2020.625L22.9375%2016H18.5V12.9987C18.5%2011.7332%2019.1199%2010.5%2021.1075%2010.5H23.125V6.5625C23.125%206.5625%2021.294%206.25%2019.5434%206.25C15.8888%206.25%2013.5%208.465%2013.5%2012.475V16H9.4375V20.625H13.5V31.8056C14.327%2031.9352%2015.1629%2032.0002%2016%2032C16.8371%2032.0002%2017.673%2031.9353%2018.5%2031.8056V20.625H22.2281Z'%20fill='white'/%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='clip0_254_2398'%3e%3crect%20width='32'%20height='32'%20fill='white'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e"
          className="h-8 w-8"
        />
      </div>

      <div className="mt-16 flex grow items-end justify-center">
        <p className="mt-auto text-center text-default-950">
          Already have an account ?{" "}
          <Link className="ms-1 text-primary" href="/login">
            <span className="font-medium">Login</span>
          </Link>
        </p>
      </div>
    </>
  );
}
