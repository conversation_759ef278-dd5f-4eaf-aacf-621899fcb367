import { api } from "@/lib/api";
import { NextRequest, NextResponse } from "next/server";

export async function GET(
  request: NextRequest,
  { params }: { params: { typeId: string } },
) {
  const searchParams = request.nextUrl.searchParams;
  const s = searchParams.get("s") || "";
  const res = await api.get<PaginatedData<ProductCategory>>(
    `product-types/${params.typeId}/categories`,
    { s },
  );

  return NextResponse.json(res?.data);
}
