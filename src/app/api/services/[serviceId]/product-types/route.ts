import { api } from "@/lib/api";
import { NextRequest, NextResponse } from "next/server";

export async function GET(
  request: NextRequest,
  { params }: { params: { serviceId: string } },
) {
  const searchParams = request.nextUrl.searchParams;
  const s = searchParams.get("s") || "";
  const res = await api.get<PaginatedData<ProductType>>(
    `services/${params.serviceId}/product-types`,
    { s },
  );

  return NextResponse.json(res?.data);
}
