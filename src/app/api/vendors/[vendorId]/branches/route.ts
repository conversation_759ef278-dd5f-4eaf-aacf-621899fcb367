import { api } from "@/lib/api";
import { NextRequest, NextResponse } from "next/server";

export async function GET(
  request: NextRequest,
  { params }: { params: { vendorId: string } },
) {
  const searchParams = request.nextUrl.searchParams;
  const s = searchParams.get("s") || "";
  const res = await api.get<PaginatedData<Branch>>(
    `vendors/${params.vendorId}/branches`,
    { s },
  );

  return NextResponse.json(res?.data);
}
