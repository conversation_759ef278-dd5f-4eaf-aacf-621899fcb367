"use client";

import Link from "next/link";

export const dynamic = "force-dynamic";
export const fetchCache = "force-no-store";

import logoImg from "@/images/logo.png";
import Image from "next/image";
import { useEffect } from "react";

export default function serverEror({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: any;
}) {

  console.log("Error by component", error)
  return (
    <section className="py-10">
      <div className="flex items-center justify-center">
        <div>
          <div className="mb-10 flex h-full w-full justify-center">
            <Image
              src={logoImg}
              width="450"
              height="450"
              alt="not-found-image"
              className="h-full max-w-full"
            />
          </div>

          <div className="flex max-w-xl flex-col justify-center text-center">
            <h1 className="mb-4 text-5xl font-semibold text-default-800">
              {error.message.endsWith("401") ? 401 : 500}
            </h1>
            <h1 className="osen-error-head"></h1>
            <h3 className="mb-4 text-2xl font-medium text-default-800">
              {error.message.endsWith("401")
                ? "Your session has expired"
                : "Oops! Something went wrong!"}
            </h3>
            <p className="mx-auto mb-8 max-w-xl text-base text-default-600">
              {error.message}
            </p>
            <div className="flex flex-wrap items-center justify-center gap-4">
              {error.message.endsWith("401") ? (
                <Link
                  href="/logout"
                  className="w-1/2 rounded-lg bg-primary px-6 py-3 text-base font-medium capitalize text-white transition-all hover:bg-default-500 lg:w-2/6"
                >
                  Go to login
                </Link>
              ) : (
                <button
                  onClick={() => reset()}
                  className="w-1/2 rounded-lg bg-primary px-6 py-3 text-base font-medium capitalize text-white transition-all hover:bg-default-500 lg:w-2/6"
                >
                  Try again
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
