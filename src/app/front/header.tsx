"use client";

import Link from "next/link";
import Image from "next/image";

import logoImg from "@/images/logo.png";
import { usePathname, useRouter } from "next/navigation";

export default function FrontHeader() {
  const path = usePathname();
  const router = useRouter();

  return (
    <header className="flex h-20 items-center justify-between">
      {path === "/front" ? (
        <Link href="/front">
          <Image src={logoImg} alt="logo" width={100} height={100} />
        </Link>
      ) : (
        <button onClick={() => router.back()}>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
            className="h-6 w-6"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"
            />
          </svg>
        </button>
      )}

      {path !== "/front" && (
        <Link href="/front">
          <Image src={logoImg} alt="logo" width={70} height={70} />
        </Link>
      )}

      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        strokeWidth={1.5}
        stroke="currentColor"
        className="h-6 w-6"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"
        />
      </svg>
    </header>
  );
}
