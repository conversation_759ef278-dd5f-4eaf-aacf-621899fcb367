import Link from "next/link";
import Image from "next/image";

import logoImg from "@/images/logo.png";
import { cache } from "react";
import { api, imagePath } from "@/lib/api";

const fetchTasks = cache(() => api.get<PaginatedData<Task>>("tasks"));

export default async function page() {
  const tasks = await fetchTasks();

  return (
    <div className="space-y-4">
      {tasks?.data?.map((task) => (
        <Link
          key={task.id}
          href={`/front/tasks/${task.id}`}
          className="flex items-center space-x-2 rounded-lg border bg-white"
        >
          <div className="w-1/4">
            <Image
              src={imagePath(task.image?.url)}
              alt={task.name}
              width={100}
              height={100}
            />
          </div>
          <div className="flex-1">
            <h1>{task.name}</h1>
            <p>{task.details}</p>
          </div>
        </Link>
      ))}
    </div>
  );
}
