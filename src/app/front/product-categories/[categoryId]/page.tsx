import Link from "next/link";
import Image from "next/image";

import logoImg from "@/images/logo.png";
import { cache } from "react";
import { api, imagePath } from "@/lib/api";

const fetchProduct = cache((id: string) =>
  api.get<PaginatedData<Product>>(`product-categories/${id}/products`),
);

export default async function page({
  params,
  searchParams,
}: {
  params: { categoryId: string };
  searchParams: Record<string, string>;
}) {
  const product = await fetchProduct(params.categoryId);

  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-2 rounded-lg border-primary bg-white px-5 py-2">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          strokeWidth={1.5}
          stroke="currentColor"
          className="h-6 w-6 text-primary"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"
          />
        </svg>

        <input placeholder="Search for product" />
      </div>
      {product?.data?.map((product) => (
        <Link
          key={product.id}
          href={`/front/products/${product.id}`}
          className="flex items-center space-x-2 rounded-lg border bg-white py-2"
        >
          <div className="w-1/4">
            <Image
              src={imagePath(product.image?.url)}
              alt={product.name}
              width={100}
              height={100}
            />
          </div>
          <div className="flex-1">
            <h1 className="text-lg font-bold">{product.name}</h1>
            <article
              className="text-xs"
              dangerouslySetInnerHTML={{
                __html: product.details,
              }}
            />
          </div>
        </Link>
      ))}
    </div>
  );
}
