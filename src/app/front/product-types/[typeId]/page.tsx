import Link from "next/link";
import Image from "next/image";

import logoImg from "@/images/logo.png";
import { cache } from "react";
import { api, imagePath } from "@/lib/api";

const fetchType = cache((id: string) =>
  api.get<ProductType>(`product-types/${id}`),
);

const fetchCategories = cache((id: string) =>
  api.get<PaginatedData<ProductCategory>>(`product-types/${id}/categories`),
);

export const generateMetadata = async ({
  params,
  searchParams,
}: {
  params: { typeId: string };
  searchParams: Record<string, string>;
}) => {
  const type = await fetchType(params.typeId);

  return {
    title: type?.name,
    description: type?.details,
  };
};

export default async function page({
  params,
  searchParams,
}: {
  params: { typeId: string };
  searchParams: Record<string, string>;
}) {
  const type = await fetchType(params.typeId);
  const categories = await fetchCategories(params.typeId);

  return (
    <div>
      <h1 className="pb-5 font-bold">{type?.name}</h1>

      <div className="space-y-4">
        {categories?.data?.map((category) => (
          <Link
            key={category.id}
            href={`/front/product-categories/${category.id}`}
            className="flex items-center space-x-2 rounded-lg border bg-white"
          >
            <div className="w-1/4">
              <Image
                src={imagePath(category.image?.url)}
                alt={category.name}
                width={100}
                height={100}
              />
            </div>
            <div className="flex-1">
              <h1>{category.name}</h1>
              <p>{category.details}</p>
            </div>
          </Link>
        ))}
      </div>
    </div>
  );
}
