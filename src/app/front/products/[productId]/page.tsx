import Link from "next/link";
import Image from "next/image";

import logoImg from "@/images/logo.png";
import { cache } from "react";
import { api, imagePath } from "@/lib/api";
import FormPreview from "../../../../components/form-preview";
import QuantityInput from "./quantity";
import { auth } from "@/auth";
import FormSection from "@/components/form-section";
import React from "react";

const fetchProduct = cache((id: string) => api.get<Product>(`products/${id}`));

export default async function page({
  params,
  searchParams,
}: {
  params: { productId: string };
  searchParams: Record<string, string>;
}) {
  const product = await fetchProduct(params.productId);
  const session = await auth();

  return (
    <div className="space-y-4">
      {product && (
        <>
          <Image
            src={imagePath(product.image?.url)}
            alt="logo"
            width={100}
            height={100}
            className="w-full"
          />
          <h1 className="text-lg font-bold">{product.name}</h1>

          <p
            dangerouslySetInnerHTML={{
              __html: product.details,
            }}
          />

          <p>
            Starting at <strong>KES {product.price}</strong>
          </p>

          <form
            method="post"
            action={`/products/${params.productId}/process`}
            className="pb-5"
          >
            {product.forms.map((form) => (
              <div key={form.id}>
                {Object.values(form.sections).map((section) => {
                  return section.repeatable &&
                    section.repeats &&
                    section.repeats > 0 ? (
                    Array.from(Array(Number(section.repeats))).map((_, i) => (
                      <FormSection
                        key={`section-${section.id}-${i}`}
                        section={section}
                        index={i + 1}
                      />
                    ))
                  ) : (
                    <FormSection
                      key={`section-${section.id}`}
                      section={section}
                    />
                  );
                })}
              </div>
            ))}

            <input type="hidden" name="userId" value={session?.user.id} />
            <input type="hidden" name="vendorId" value={product?.vendorId} />
            <input type="hidden" name="branchId" value={product?.branchId} />
            <input type="hidden" name="action" value="Purchase" />
            <input type="hidden" name="type" value="Instant" />
            <input type="hidden" name="delivery" value="Dinein" />
            <input type="hidden" name="status" value="Pending" />
            <input type="hidden" name="tip" value="0" />

            <button
              name="quantity"
              value="1"
              type="submit"
              className="w-full rounded-lg bg-primary px-4 py-2 text-white"
            >
              Place Order
            </button>
          </form>

          {/* <QuantityInput product={product} placeOrder={placeOrder} /> */}
        </>
      )}
    </div>
  );
}
