import { api } from "@/lib/api";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  const { userId, product, quantity } = await request.json();

  const order = await api.post<Order>("orders", {
    userId,
    vendorId: product?.vendorId,
    branchId: product?.branchId,
    sectionId: null,
    action: "Purchase",
    type: "Instant",
    delivery: "Dinein",
    status: "Pending",
    meta: {
      responses: {},
    },
    items: [
      {
        [product.id]: quantity,
      },
    ],
    tip: 0,
  });

  if (order) {
    return NextResponse.redirect(`/orders/${order.id}`);
  }

  return NextResponse.redirect(`/products/${product.id}`);
}
