"use client";

import FormPreview from "../../../../components/form-preview";
import { useForm } from "react-hook-form";
import toast from "react-hot-toast";

export default function QuantityInput({
  product,
  placeOrder,
}: {
  product: Product;
  placeOrder: (data: FormData) => Promise<Order | undefined>;
}) {
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<{
    quantity: number;
    sections: FormSection[];
  }>({
    defaultValues: {
      quantity: 1,
      sections: [],
    },
  });

  const quantity = watch("quantity");

  const onSubmit = (payload: { quantity: number; sections: FormSection[] }) => {
    const data = new FormData();
    payload.sections.map((s, si) => {
      data.append(`sections[${si}][id]`, s.id.toString());
      data.append(`sections[${si}][name]`, s.name);
      data.append(`sections[${si}][details]`, s.details);

      if (s.repeatable) {
        data.append(`sections[${si}][repeatable]`, s.repeatable.toString());
      }

      if (s.repeats) {
        data.append(`sections[${si}][repeats]`, s.repeats.toString());
      }

      s.fields.map((f, fi) => {
        data.append(`sections[${si}][fields][${fi}][id]`, f.id.toString());
        data.append(`sections[${si}][fields][${fi}][type]`, f.type);
        data.append(`sections[${si}][fields][${fi}][name]`, f.name);
        data.append(`sections[${si}][fields][${fi}][label]`, f.label);
        data.append(
          `sections[${si}][fields][${fi}][defaultValue]`,
          f.defaultValue,
        );
        data.append(
          `sections[${si}][fields][${fi}][placeholder]`,
          f.placeholder,
        );
        data.append(
          `sections[${si}][fields][${fi}][required]`,
          f.required.toString(),
        );

        if (f.repeatable) {
          data.append(
            `sections[${si}][fields][${fi}][repeatable]`,
            f.repeatable.toString(),
          );
        }

        if (f.repeats) {
          data.append(
            `sections[${si}][fields][${fi}][repeats]`,
            f.repeats?.toString(),
          );
        }

        if (f.min) {
          data.append(`sections[${si}][fields][${fi}][min]`, f.min?.toString());
        }

        if (f.max) {
          data.append(`sections[${si}][fields][${fi}][max]`, f.max?.toString());
        }

        f.options?.map((o, oi) => {
          data.append(
            `sections[${si}][fields][${fi}][options][${oi}][value]`,
            o.value.toString(),
          );
          data.append(
            `sections[${si}][fields][${fi}][options][${oi}][label]`,
            o.label.toString(),
          );
        });
      });
    });
    toast.promise(placeOrder(data), {
      loading: "Placing order...",
      success: "Order placed successfully",
      error: "Failed to place order",
    });
  };

  return (
    <form
      className="my-5 flex flex-col justify-center space-y-4"
      onSubmit={handleSubmit(onSubmit)}
    >
      {product.forms?.map((f) => (
        <FormPreview
          key={f.id}
          sections={Object.values(f.sections)}
          onSubmit={({ sections }) => setValue("sections", sections)}
        />
      ))}

      <div className="flex space-x-12 px-12">
        <button onClick={() => setValue("quantity", quantity - 1)}>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
            className="h-6 w-6"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M15 12H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
            />
          </svg>
        </button>

        <span className="w-full rounded-lg border border-primary px-5 py-2 text-center">
          {quantity}
        </span>

        <button onClick={() => setValue("quantity", quantity + 1)}>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
            className="h-6 w-6"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
            />
          </svg>
        </button>
      </div>

      <button
        type="submit"
        className="w-full rounded-lg bg-default-500 px-5 py-3 text-white"
      >
        Buy this item
      </button>
    </form>
  );
}
