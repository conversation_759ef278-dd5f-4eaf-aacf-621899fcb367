import Link from "next/link";
import Image from "next/image";

import logoImg from "@/images/logo.png";
import { cache } from "react";
import { api, imagePath } from "@/lib/api";

const fetchService = cache((id: string) =>
  api.get<PaginatedData<Service>>(`tasks/${id}/services`),
);

export default async function page({
  params,
  searchParams,
}: {
  params: { taskId: string };
  searchParams: Record<string, string>;
}) {
  const service = await fetchService(params.taskId);

  return (
    <div className="space-y-4">
      {service?.data?.map((service) => (
        <Link
          key={service.id}
          href={`/front/services/${service.id}`}
          className="flex items-center space-x-2 rounded-lg border bg-white"
        >
          <div className="w-1/4">
            <Image
              src={imagePath(service.image?.url)}
              alt={service.name}
              width={100}
              height={100}
            />
          </div>
          <div className="flex-1">
            <h1>{service.name}</h1>
            <p>{service.details}</p>
          </div>
        </Link>
      ))}
    </div>
  );
}
