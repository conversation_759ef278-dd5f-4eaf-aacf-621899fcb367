import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import { PropsWithChildren } from "react";
import { Toaster } from "react-hot-toast";
import NextTopLoader from "nextjs-toploader";

import "./globals.css";
import 'react-phone-number-input/style.css'
import "react-datepicker/dist/react-datepicker.css";

import { Providers } from "./providers";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: {
    template: "%s :: App In App",
    default: "App In App",
  },
  description: "All in one app for all your needs",
};

export default function RootLayout({ children }: PropsWithChildren<{}>) {
  
  return (
    <html lang="en">
      <body className={`${inter.className} text-black dark:text-white`}>
        <NextTopLoader
          color="#83B4AA"
          initialPosition={0.08}
          crawlSpeed={200}
          height={3}
          crawl={true}
          showSpinner={false}
          easing="ease"
          speed={200}
          shadow="0 0 10px #83B4AA,0 0 5px #83B4AA"
        />
        <Providers>{children}</Providers>

        <Toaster position="bottom-center" />
      </body>
    </html>
  );
}
