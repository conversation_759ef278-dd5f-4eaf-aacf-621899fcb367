"use client";

import Link from "next/link";
import logoImg from "@/images/logo.png";
import Image from "next/image";
import { useRouter } from "next/navigation";

export default function NotFound() {
  const router = useRouter();

  return (
    <section className="py-10">
      <div className="flex items-center justify-center">
        <div>
          <div className="mb-10 flex h-full w-full justify-center">
            <Image
              src={logoImg}
              width="450"
              height="450"
              alt="not-found-image"
              className="h-full max-w-full"
            />
          </div>
          <div className="flex max-w-xl flex-col justify-center text-center">
            <h1 className="mb-4 text-5xl font-semibold text-default-800">
              Ooops...
            </h1>
            <h3 className="mb-4 text-2xl font-medium text-default-800">
              It looks like you’re lost...
            </h3>
            <p className="mx-auto mb-8 max-w-xl text-base text-default-600">
              The requested page could not be found. The link might be broken or
              the page was removed.
            </p>
            <div className="flex flex-wrap items-center justify-center gap-4">
              <button
                onClick={() => router.back()}
                className="w-1/2 rounded-lg bg-primary px-6 py-3 text-base font-medium capitalize text-white transition-all hover:bg-default-500 lg:w-2/6"
              >
                Go Back
              </button>
              <Link
                className="relative inline-flex w-1/2 items-center justify-center rounded-lg border border-primary px-6 py-3 text-base font-medium capitalize text-primary transition-all hover:bg-primary hover:text-white lg:w-2/6"
                href="/"
              >
                Go To home
              </Link>
            </div>

            <div className="mt-5">
              <Link
                href="/login"
                className="w-full rounded-lg bg-primary px-6 py-3 text-base font-medium capitalize text-white transition-all hover:bg-default-500 lg:w-2/6"
              >
                Go to login
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
