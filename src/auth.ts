import NextAuth from "next-auth";

import Credentials from "next-auth/providers/credentials";

import { type NextAuthConfig } from "next-auth";
import { api } from "./lib/api";

export const config = {
  basePath: "/api/auth",
  trustHost: true,
  session: {
    strategy: "jwt",
  },
  pages: {
    signIn: "/login",
    signOut: "/logout",
    error: "/auth-error",
    verifyRequest: "/verify-request",
  },
  providers: [
    Credentials({
      name: "Credentials",
      id: "credentials",
      credentials: {
        username: { label: "Username", type: "text" },
        password: { label: "Password", type: "password" },
        identifier: { label: "Identifier", type: "text" },
      },
      // @ts-ignore
      async authorize(credentials) {
        try {
          const auth = credentials.identifier
            ? await api.post<{
                user: User;
                branch?: Branch;
                vendor?: Vendor;
                token: string;
                expiresAt: string;
                error?: string;
              }>("auth/login/staff", credentials)
            : await api.post<{
                user: User;
                branch?: Branch;
                vendor?: Vendor;
                token: string;
                expiresAt: string;
                error?: string;
              }>("auth/login", {
                username: credentials.username,
                password: credentials.password,
              });

          if (auth) {
            if (auth.error) {
              throw new Error(auth.error);
            } else {
              const { user, branch, vendor, token, expiresAt } = auth;

              return {
                ...user,
                roles: user.roles,
                accessToken: token,
                expiresAt,
                branch,
                vendor,
              };
            }
          }

          return null;
        } catch (error: any) {
          console.log("AUTH RESPONSE ERROR MESSAGE:, ", auth)
          throw new Error(error.message);
        }
      },
    }),
  ],
  callbacks: {
    async session({ session, token }) {
      if (token) {
        session.accessToken = token.accessToken;
        session.user.id = token.id;
        session.user.name = token.name;
        session.user.firstName = token.firstName;
        // @ts-ignore
        session.user.roles = token.roles;
        // @ts-ignore
        session.user.email = token.email;
        session.user.image = token.picture;
        // @ts-ignore
        session.user.avatarUrl = token.avatarUrl;
        // @ts-ignore
        session.user.avatar = token.avatar;
        // @ts-ignore
        session.branch = token.branch;
        // @ts-ignore
        session.vendor = token.vendor;
        // @ts-ignore
        session.expiresAt = token.expiresAt;
      }

      return session;
    },

    async jwt({ token, user }) {
      if (user) {
        token.accessToken = user.accessToken;
        token.name = user.name;
        token.firstName = user.firstName;
        token.roles = user.roles?.map((role) => role.name);
        token.email = user.email;
        token.picture = user.image;
        token.avatarUrl = user.avatarUrl;
        token.avatar = user.avatar;
        // @ts-ignore
        token.roles = user.roles;
        token.id = user.id!;
        token.expiresAt = user.expiresAt;
        // @ts-ignore
        token.branch = user.branch;
        // @ts-ignore
        token.vendor = user.vendor;
      }

      return token;
    },
  },
} satisfies NextAuthConfig;

export const { handlers, auth, signIn, signOut } = NextAuth(config);
