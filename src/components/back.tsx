"use client";

import { useRouter } from "next/navigation";

import { Icon } from "./icon";

const BackBtn = () => {
  const router = useRouter();
  return (
    <div
      className="mb-3 flex w-fit cursor-pointer items-center gap-x-1 rounded-lg  py-1 pl-1 pr-2 transition-all duration-300 hover:bg-default-200 dark:hover:bg-gray-800"
      onClick={() => router.back()}
    >
      <Icon
        name="icon-[heroicons--arrow-small-left-20-solid]"
        classNames="text-primary"
      />
      <span className="font-psans text-sm font-semibold text-primary dark:text-gray-400">
        Back
      </span>
    </div>
  );
};

export default BackBtn;
