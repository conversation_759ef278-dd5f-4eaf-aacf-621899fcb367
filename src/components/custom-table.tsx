"use client";

import { Key, SetStateAction, use<PERSON><PERSON>back, useMemo, useState } from "react";

import {
  Button,
  Chip,
  Dropdown,
  DropdownItem,
  DropdownMenu,
  DropdownTrigger,
  Image,
  Input,
  Pagination,
  PaginationItemType,
  Select,
  SelectItem,
  Spinner,
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from "@nextui-org/react";
import Link from "next/link";
import { usePathname, useRouter, useSearchParams } from "next/navigation";

import { cn, createUrl } from "@/lib/utils";
import { Icon } from "./icon";
import { useSession } from "next-auth/react";
import { generateRandomString } from "./tables/staff-table";

interface Columns {
  name: string;
  uid: string | "actions";
  sortable?: boolean;
  renderCell?: (row: any) => React.ReactNode;
}

interface FilterValue {
  name: string;
  value: string | number;
}

interface Filter {
  column: string;
  displayName?: string;
  values: FilterValue[];
}

interface Props {
  title: string;
  columns: Columns[];
  data?: any[];
  /**
   * Use this to either redirect a use to a new page or
   * pass a react node that may open a dialog/modal for the
   * user to interact with
   */
  action?: string | React.ReactNode;
  isLoading?: boolean;
  // use this to hide the top and bottom content when true
  isDashboard?: boolean;
  meta?: PaginationMeta;
  filter?: Filter | Filter[];
}

type Order = "ascending" | "descending";

function descendingComparator<T>(a: T, b: T, orderBy: keyof T) {
  if (b[orderBy] < a[orderBy]) {
    return -1;
  }

  if (b[orderBy] > a[orderBy]) {
    return 1;
  }

  return 0;
}

function getComparator<Key extends keyof any>(
  order: Order,
  orderBy: Key,
): (
  a: { [key in Key]: number | string },
  b: { [key in Key]: number | string },
) => number {
  return order === "descending"
    ? (a, b) => descendingComparator(a, b, orderBy)
    : (a, b) => -descendingComparator(a, b, orderBy);
}

export const CustomTable = ({
  data = [],
  columns,
  title,
  action,
  isLoading = false,
  isDashboard = false,
  meta,
  filter,
}: Props) => {
  const searchParams = useSearchParams();
  const { data: session } = useSession();
  const [pending, setPending] = useState(isLoading);
  const [filterValue, setFilterValue] = useState("");
  const rowsPerPage = meta?.perPage as number;
  const page = meta?.currentPage as number;
  // const [rowsPerPage, setRowsPerPage] = useState(searchParams.has('per') ? Number(searchParams.get('per')) : meta?.perPage ?? 10);
  // const [page, setPage] = useState(meta?.currentPage as number);
  const [sortDescriptor, setSortDescriptor] = useState<any>({});
  const [selectedKeys, setSelectedKeys] = useState<Set<Key>>(new Set([]));
  const [selectedFilter, setSelectedFilter] = useState<any>({});

  const router = useRouter();
  const pathname = usePathname();

  function stableSort<T>(
    array: readonly T[],
    comparator: (a: T, b: T) => number,
  ) {
    const stabilizedThis = array.map((el, index) => [el, index] as [T, number]);

    stabilizedThis.sort((a, b) => {
      const order = comparator(a[0], b[0]);
      if (order !== 0) return order;
      return a[1] - b[1];
    });

    return stabilizedThis.map((el) => el[0]);
  }

  const searchedItems = useMemo(() => {
    const filteredRows: any[] = [];

    if (filterValue === "" || filterValue === null) return data;

    data.forEach((row: any) => {
      columns.some((column) => {
        if (row[column.uid] !== undefined && row[column.uid] !== null) {
          const rowValue = String(row[column.uid]).toLowerCase();

          if (
            rowValue.length >= filterValue.length &&
            rowValue.indexOf(filterValue.toLowerCase()) >= 0
          ) {
            filteredRows.push(row);
            return true;
          }
        }

        return false;
      });
    });

    return filteredRows;
  }, [columns, filterValue, data]);

  const recordsAfterSorting = useMemo(() => {
    return stableSort(
      searchedItems,
      getComparator(sortDescriptor.direction, sortDescriptor.column),
    );
  }, [searchedItems, sortDescriptor.column, sortDescriptor.direction]);

  const pages = Math.ceil((meta?.total ?? searchedItems.length) / rowsPerPage);

  const onSearchChange = useCallback((value: SetStateAction<string>) => {
    if (value) {
      // setPage(1);
      setFilterValue(value);
    } else {
      setFilterValue("");
    }
  }, []);

  const onClear = useCallback(() => {
    // setPage(1);
    setFilterValue("");
  }, []);

  const renderCell = useCallback(
    (item: { [x: string]: any }, key: string) => {
      const cellValue = item[key];
      const column = columns.find((column) => column.uid === key);

      if (column?.renderCell !== undefined) {
        return column.renderCell(item);
      } else if (typeof cellValue === "boolean") {
        return (
          <Chip
            size="sm"
            color={Boolean(cellValue) ? "success" : "danger"}
            className={cn(
              "capitalize",
              key === "isActive" && "gap-1 border-none text-default-600",
            )}
            variant={key === "isActive" ? "dot" : "flat"}
          >
            {key === "isActive"
              ? Boolean(cellValue)
                ? "Active"
                : "Inactive"
              : key === "isArchived"
                ? Boolean(cellValue)
                  ? "Archived"
                  : "Un archived"
                : key}
          </Chip>
        );
      } else {
        return cellValue;
      }
    },
    [columns],
  );

  const topContent = useMemo(() => {
    const newParams = new URLSearchParams(searchParams.toString());
    newParams.set("page", "1");

    const perPage = ["5", "10", "20", "50", "75", "100"];

    const filterParams = new URLSearchParams(searchParams.toString());
    filterParams.set("page", "1");

    const renderFilters = () => {
      if (filter && Array.isArray(filter)) {
        return filter.map((filterValue, index) => {
          const filterValues = filterValue?.values
            ? [{ name: "All", value: "" }, ...filterValue.values]
            : [];

          return (
            <Select
              key={index}
              isOpen={Boolean(selectedFilter[filterValue?.column ?? ""])}
              onOpenChange={(open) =>
                open !== Boolean(selectedFilter[filterValue?.column ?? ""]) &&
                setSelectedFilter({ [filterValue?.column as any]: open })
              }
              classNames={{
                base: "w-fit xl:min-w-[200px] text-primary",
                trigger: "",
              }}
              selectedKeys={[searchParams.get(filterValue?.column) ?? ""]}
              startContent={
                <p className="text-sm">
                  {filterValue?.displayName ?? "Status"}:
                </p>
              }
              selectorIcon={
                <Icon
                  name="icon-[eva--arrow-down-fill]"
                  classNames="text-primary h-4 w-4"
                />
              }
              aria-label={filterValue?.displayName ?? "Status"}
            >
              {filterValues.map((status) => {
                const params = new URLSearchParams(searchParams.toString());
                params.set("page", "1");

                if (!status.value) {
                  params.delete(filterValue.column);
                }
                const url = !status.value
                  ? createUrl(pathname, params)
                  : createUrl(
                      pathname,
                      params,
                      filterValue.column,
                      String(status.value),
                    );

                return (
                  <SelectItem
                    key={status.value}
                    value={status.value}
                    as={Link}
                    href={url}
                    className="font-bold"
                    onPress={() =>
                      setSelectedFilter({
                        [filterValue?.column as any]: !Boolean(
                          selectedFilter[filterValue?.column ?? ""],
                        ),
                      })
                    }
                    aria-label={status.name}
                  >
                    {status.name}
                  </SelectItem>
                );
              })}
            </Select>
          );
        });
      } else {
        const filterValues = filter?.values
          ? [{ name: "All", value: "" }, ...filter.values]
          : [];

        return filter === undefined ? null : (
          <Select
            isOpen={Boolean(selectedFilter[filter?.column ?? ""])}
            onOpenChange={(open) =>
              open !== Boolean(selectedFilter[filter?.column ?? ""]) &&
              setSelectedFilter({ [filter?.column as any]: open })
            }
            classNames={{ base: "w-fit min-w-[150px]" }}
            className="text-primary"
            selectedKeys={[searchParams.get(filter?.column ?? "") ?? ""]}
            startContent={
              <p className="text-sm">
                {filter?.displayName ?? "Status"}
                {": "}
              </p>
            }
            selectorIcon={
              <Icon
                name="icon-[eva--arrow-down-fill]"
                classNames="text-primary h-4 w-4"
              />
            }
            aria-label={filter?.displayName ?? "Status"}
          >
            {filterValues.map((status) => {
              if (!status.value) {
                filter?.column && filterParams.delete(filter.column);
              }

              const url = !status.value
                ? createUrl(pathname, filterParams)
                : createUrl(
                    pathname,
                    filterParams,
                    filter?.column,
                    String(status.value),
                  );

              return (
                <SelectItem
                  key={String(status.value)}
                  value={String(status.value)}
                  as={Link}
                  href={url}
                  onPress={() =>
                    setSelectedFilter({
                      [filter?.column as any]: !Boolean(
                        selectedFilter[filter?.column ?? ""],
                      ),
                    })
                  }
                  aria-label={status.name}
                >
                  {status.name}
                </SelectItem>
              );
            })}
          </Select>
        );
      }
    };

    return (
      <div className="flex flex-col gap-4">
        <div className="flex items-end justify-between gap-3">
          <Input
            isClearable
            variant="bordered"
            size={"lg"}
            className="w-full sm:max-w-[34%]"
            placeholder={`Search ${title.toLowerCase()}`}
            startContent={
              <Icon name="icon-[eva--search-outline]" classNames="h-5 w-5" />
            }
            classNames={{
              inputWrapper: [
                "border border-default-300 dark:border-default-700 rounded-lg",
              ],
              input: [
                "text-default-700 dark:text-default-300 font-semibold font-psans text-sm placeholder:text-default placeholder:font-normal",
              ],
            }}
            value={filterValue}
            onClear={() => onClear()}
            onValueChange={onSearchChange}
          />

          <div className="flex items-center gap-3">
            {action !== undefined &&
              (typeof action === "string" ? (
                <>
                  <div className="hidden md:flex">
                    <Button
                      as={Link}
                      href={action}
                      className="bg-slate-800 text-white"
                      endContent={
                        <Icon
                          name="icon-[ic--round-add]"
                          classNames="text-white"
                        />
                      }
                    >
                      Add New
                    </Button>
                  </div>

                  <div className="sm:hidden">
                    <Button
                      isIconOnly
                      as={Link}
                      size="lg"
                      href={action}
                      className="bg-slate-800 text-white"
                    >
                      <Icon
                        name="icon-[ic--round-add]"
                        classNames="text-white"
                      />
                    </Button>
                  </div>
                </>
              ) : (
                action
              ))}
          </div>
        </div>

        <div className="flex items-center justify-between">
          <span className="text-small text-default-400">
            Total {meta?.total} {title.toLowerCase()}
          </span>
          <div className="flex items-center gap-5">
            <div className="flex gap-3">{renderFilters()}</div>
            <Dropdown size="sm" showArrow placement="bottom-end">
              <DropdownTrigger>
                <div className="flex flex-row hover:cursor-pointer">
                  <div className="flex items-center">
                    <p className="text-default flex items-center text-xs">
                      Rows per page:{" "}
                      <span className="ml-1 font-bold">{rowsPerPage}</span>
                    </p>
                    <Icon
                      name="icon-[eva--chevron-down-outline]"
                      classNames="cursor-pointer"
                    />
                  </div>
                </div>
              </DropdownTrigger>
              <DropdownMenu
                selectedKeys={new Set([searchParams.get("per") || ""])}
                selectionMode="multiple"
              >
                {perPage.map((page) => (
                  <DropdownItem
                    key={page}
                    as={Link}
                    href={createUrl(pathname, newParams, "per", page)}
                  >
                    {page}
                  </DropdownItem>
                ))}
              </DropdownMenu>
            </Dropdown>
          </div>
        </div>
      </div>
    );
  }, [
    action,
    filter,
    filterValue,
    meta?.total,
    onClear,
    onSearchChange,
    pathname,
    rowsPerPage,
    searchParams,
    selectedFilter,
    title,
  ]);

  const bottomContent = useMemo(() => {
    const newParams = new URLSearchParams(searchParams.toString());
    newParams.set("page", String(page - 1));

    const previousUrl = createUrl(pathname, newParams);

    const nextParams = new URLSearchParams(searchParams.toString());
    nextParams.set("page", String(page + 1));

    const nextUrl = createUrl(pathname, nextParams);

    return (
      <div className="flex items-center justify-between px-2 py-2">
        {/* <span className="w-[30%] text-small text-default-400"></span> */}
        <div className="flex items-center gap-2">
          {selectedKeys.size >= 1 && (
            <Button
              className="text-sm font-bold text-red-500"
              variant="light"
              startContent={<Icon name="icon-[mingcute--delete-2-line]" />}
            >
              delete
            </Button>
          )}
          {session?.vendor ? null : (
            <span className="text-small text-default-400">
              {(selectedKeys as any) === "all" ? (
                <Button
                  className="text-sm font-bold text-red-500"
                  variant="light"
                  startContent={<Icon name="icon-[mingcute--delete-2-line]" />}
                >
                  delete all
                </Button>
              ) : (
                `${selectedKeys.size} of ${data.length} selected`
              )}
            </span>
          )}
        </div>

        <Pagination
          showShadow
          showControls
          color="primary"
          initialPage={1}
          page={page}
          total={pages || 1}
          renderItem={({ ref, key, value, index, isActive, className }) => {
            const newParams = new URLSearchParams(searchParams.toString());
            newParams.set("page", value.toString());

            if (value === PaginationItemType.NEXT) {
              return (
                <Button
                  key={key?key+generateRandomString(10):undefined}
                  className={cn(className)}
                  isDisabled={pages === 1 || page == pages}
                  as={page < pages ? Link : "button"}
                  href={nextUrl}
                  isIconOnly
                >
                  <Icon
                    name="icon-[tabler--chevron-right]"
                    classNames="size-4"
                  />
                </Button>
              );
            }

            if (value === PaginationItemType.PREV) {
              return (
                <Button
                  key={key}
                  className={cn(className)}
                  isDisabled={page === 1}
                  as={page > 1 ? Link : "button"}
                  href={previousUrl}
                  isIconOnly
                >
                  <Icon
                    name="icon-[tabler--chevron-left]"
                    classNames="size-4"
                  />
                </Button>
              );
            }

            if (value === PaginationItemType.DOTS) {
              const dotsParams = new URLSearchParams(searchParams.toString());
              dotsParams.set("page", index.toString());
              return (
                <Button
                  key={key}
                  className={cn(className, "group")}
                  isIconOnly
                  as={Link}
                  href={createUrl(pathname, dotsParams)}
                >
                  <Icon
                    name="icon-[ant-design--ellipsis-outlined]"
                    classNames="group-hover:hidden"
                  />
                  <Icon
                    name={
                      index < page
                        ? "icon-[ep--d-arrow-left]"
                        : "icon-[material-symbols-light--double-arrow]"
                    }
                    classNames="hidden group-hover:flex"
                  />
                </Button>
              );
            }

            return isActive ? (
              <Button
                key={key}
                ref={ref}
                className={cn(className)}
                color={"primary"}
              >
                {value}
              </Button>
            ) : (
              <Button
                key={key}
                ref={ref}
                className={cn(className, "dark:text-gray-800")}
                as={Link}
                href={createUrl(pathname, newParams)}
              >
                {value}
              </Button>
            );
          }}
        />

        <div className="hidden w-[30%] justify-end gap-2 sm:flex">
          <Button
            as={page > 1 ? Link : "button"}
            href={previousUrl}
            isDisabled={page === 1}
            size="sm"
            color="primary"
            variant={page === 1 ? "ghost" : "solid"}
          >
            Previous
          </Button>
          <Button
            as={page < pages ? Link : "button"}
            href={nextUrl}
            isDisabled={pages === 1 || page == pages}
            size="sm"
            color="primary"
            variant={page == pages ? "ghost" : "solid"}
          >
            Next
          </Button>
        </div>
      </div>
    );
  }, [data.length, page, pages, pathname, searchParams, selectedKeys]);

  return (
    <Table
      aria-label={title}
      sortDescriptor={sortDescriptor}
      onSortChange={setSortDescriptor}
      topContent={isDashboard ? null : topContent}
      selectionMode={session?.vendor ? "none" : "multiple"}
      selectedKeys={selectedKeys as any}
      onSelectionChange={(keys) => setSelectedKeys(keys as Set<string>)}
      onRowAction={() => {}}
      classNames={{
        th: [
          "bg-default-100",
          "shadow-none",
          "text-default-600",
          "radius:none",
          "py-4",
          "dark:bg-default-800",
          "dark:text-default-400",
        ],

        thead: [
          "rounded-none",
          "shadow-none",
          "p-0",
          "dark:bg-transparent dark:m-0 ",
        ],
        wrapper:
          "p-0 shadow-lg shadow-none border dark:border-default-700 dark:shadow-lg",
        td: "rounded-none bg-white border-b border-dashed border-default-300 dark:bg-default-900 dark:border-default-700 group-data-[last=true]:border-none",
        table: "bg-default-100 dark:bg-default-800",
      }}
      topContentPlacement="outside"
      bottomContent={isDashboard ? null : bottomContent}
      bottomContentPlacement="outside"
    >
      <TableHeader columns={columns}>
        {(column) => (
          <TableColumn
            key={column.uid}
            align={column.uid === "actions" ? "center" : "start"}
            allowsSorting={column.sortable}
          >
            {column.name}
          </TableColumn>
        )}
      </TableHeader>
      <TableBody
        isLoading={pending}
        loadingContent={<Spinner />}
        emptyContent={
          pending ? (
            <Spinner />
          ) : (
            <div className="my-8 flex flex-col items-center justify-center p-8 dark:bg-default-800">
              <Image src="/assets/images/empty.svg" alt="No content" />
              <p className="mt-3 text-xs">No Data found</p>
            </div>
          )
        }
      >
        {recordsAfterSorting.map((item: any, index: any) => {
          return (
            <TableRow key={index + item.id+generateRandomString(10)}>
              {columns.map((column, index) => (
                <TableCell key={index + column.uid}>
                  {renderCell(item, column.uid)}
                </TableCell>
              ))}
            </TableRow>
          );
        })}
      </TableBody>
    </Table>
  );
};
