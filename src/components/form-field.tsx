export default function FormField({
  field,
  index,
}: {
  field: FormField;
  index?: number;
}) {
  return (
    <div className="mt-3" data-type={field.type}>
      <label
        className="flex items-center space-x-2"
        htmlFor={`field-${field.id}`}
      >
        {field.label}{" "}
        {index && <span className="text-gray-500">({index})</span>}
        {field.required && <span className="text-red-500">*</span>}
      </label>

      {['text', 'tel', 'uFirstName', 'uLastName'].includes(field.type) && (
        <input
          placeholder={field.placeholder}
          name={field.name}
          type={field.type}
          id={
            field.id?.toString() ||
            Math.random().toString(16).slice(2, 8).toUpperCase()
          }
          className="w-full rounded-lg border border-default-600 px-4 py-2"
          defaultValue={field.defaultValue}
        />
      )}

      {field.type === "password" && (
        <input
          placeholder={field.placeholder}
          name={field.name}
          type={field.type}
          id={
            field.id?.toString() ||
            Math.random().toString(16).slice(2, 8).toUpperCase()
          }
          className="w-full rounded-lg border border-default-600 px-4 py-2"
          defaultValue={field.defaultValue}
        />
      )}

      {field.type === "range" && (
        <div className="relative mb-6">
          <label htmlFor="labels-range-input" className="sr-only">
            {field.label}
          </label>

          <input
            type="range"
            placeholder={field.placeholder}
            name={field.name}
            id={
              field.id?.toString() ||
              Math.random().toString(16).slice(2, 8).toUpperCase()
            }
            className="w-full rounded-lg border border-default-600 px-4 py-2"
            min={field.min}
            max={field.max}
            defaultValue={field.defaultValue}
          />
          <span className="absolute -bottom-6 start-0 text-sm text-gray-500 dark:text-gray-400">
            Min ({field.min})
          </span>

          {field.max && (
            <span className="absolute -bottom-6 start-1/2 -translate-x-1/2 text-sm text-gray-500 dark:text-gray-400 rtl:translate-x-1/2">
              {field.max / 2}
            </span>
          )}
          <span className="absolute -bottom-6 end-0 text-sm text-gray-500 dark:text-gray-400">
            Max ({field.max}))
          </span>
        </div>
      )}

      {field.type === "color" && (
        <input
          placeholder={field.placeholder}
          name={field.name}
          type={field.type}
          id={
            field.id?.toString() ||
            Math.random().toString(16).slice(2, 8).toUpperCase()
          }
          className="w-full rounded-lg border border-default-600 px-4 py-2"
          defaultValue={field.defaultValue}
        />
      )}

      {field.type === "email" && (
        <input
          placeholder={field.placeholder}
          name={field.name}
          type={field.type}
          id={
            field.id?.toString() ||
            Math.random().toString(16).slice(2, 8).toUpperCase()
          }
          className="w-full rounded-lg border border-default-600 px-4 py-2"
          defaultValue={field.defaultValue}
        />
      )}

      {field.type === "tel" && (
        <input
          placeholder={field.placeholder}
          name={field.name}
          type={field.type}
          id={
            field.id?.toString() ||
            Math.random().toString(16).slice(2, 8).toUpperCase()
          }
          className="w-full rounded-lg border border-default-600 px-4 py-2"
          defaultValue={field.defaultValue}
        />
      )}

      {field.type === "textarea" && (
        <textarea
          placeholder={field.placeholder}
          name={field.name}
          id={
            field.id?.toString() ||
            Math.random().toString(16).slice(2, 8).toUpperCase()
          }
          className="w-full rounded-lg border border-default-600 px-4 py-2"
          defaultValue={field.defaultValue}
        />
      )}

      {field.type === "number" && (
        <input
          placeholder={field.placeholder}
          name={field.name}
          type={field.type}
          id={
            field.id?.toString() ||
            Math.random().toString(16).slice(2, 8).toUpperCase()
          }
          className="w-full rounded-lg border border-default-600 px-4 py-2"
          defaultValue={field.defaultValue}
        />
      )}

      {field.type === "date" && (
        <input
          placeholder={field.placeholder}
          name={field.name}
          type={field.type}
          id={
            field.id?.toString() ||
            Math.random().toString(16).slice(2, 8).toUpperCase()
          }
          className="w-full rounded-lg border border-default-600 px-4 py-2"
          defaultValue={field.defaultValue}
        />
      )}

      {field.type === "time" && (
        <input
          placeholder={field.placeholder}
          name={field.name}
          type={field.type}
          id={
            field.id?.toString() ||
            Math.random().toString(16).slice(2, 8).toUpperCase()
          }
          className="w-full rounded-lg border border-default-600 px-4 py-2"
          defaultValue={field.defaultValue}
        />
      )}

      {field.type === "select" && (
        <select
          name={field.name}
          id={
            field.id?.toString() ||
            Math.random().toString(16).slice(2, 8).toUpperCase()
          }
          className="w-full rounded-lg border border-default-600 px-4 py-2"
          defaultValue={field.defaultValue}
        >
          {field.options?.map((option, oi) => (
            <option key={`select-opt-${option.value}`} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      )}

      {field.type === "radio" &&
        field.options?.map((option, oi) => (
          <div key={`radio-opt-${option.value}`} className="my-2">
            <label className="flex items-center space-x-2">
              <input
                type="radio"
                name={field.name}
                value={option.value}
                id={`radio-${field.id}-${option.value}`}
              />

              <span>{option.label}</span>
            </label>
          </div>
        ))}

      {field.type === "multicheck" &&
        field.options?.map((option, oi) => (
          <div key={`checkbox-opt-${option.value}`} className="my-2">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                name={field.name}
                value={option.value}
                id={`checkbox-${field.id}-${option.value}`}
              />

              <span>{option.label}</span>
            </label>
          </div>
        ))}

      {field.type === "checkbox" &&
        field.options?.map((option, oi) => (
          <div key={`checkbox-opt-${option.value}`} className="my-2">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                name={field.name}
                value={option.value}
                id={`checkbox-${field.id}-${option.value}`}
              />

              <span>{option.label}</span>
            </label>
          </div>
        ))}

      {field.type === "file" && (
        <div className="flex h-32 items-center justify-center rounded-lg border border-dashed border-default-600">
          <input
            type="file"
            name={field.name}
            id={
              field.id?.toString() ||
              Math.random().toString(16).slice(2, 8).toUpperCase()
            }
            className="hidden"
          />
          <p>{field.placeholder || field.label}</p>
        </div>
      )}

      {field.type === "slot" && (
        <div className="flex space-x-2">
          <div className="flex-1">
            <input
              placeholder={field.placeholder}
              name={field.name}
              type="date"
              id={`${field.id}-date`}
              className="w-full rounded-lg border border-default-600 px-4 py-2"
            />
          </div>

          <div className="flex-1">
            <input
              placeholder={field.placeholder}
              name={field.name}
              type="time"
              id={`${field.id}-time`}
              className="w-full rounded-lg border border-default-600 px-4 py-2"
            />
          </div>
        </div>
      )}
    </div>
  );
}
