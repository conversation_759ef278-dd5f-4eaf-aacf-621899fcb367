"use client";

import { useForm } from "react-hook-form";
import AsyncSelect from "react-select/async";

export default function FormPreview({
  sections,
  onSubmit,
}: {
  sections: FormSection[];
  onSubmit?: ({ sections }: { sections: FormSection[] }) => void;
}) {
  const fetchVendors = async (s: string) => {
    if (s.length > 3) {
      const vendors: Vendor[] = await fetch(
        `/api/vendors?s=${s}`
      ).then((r) => r.json());

      return vendors?.map((vendor) => ({
        value: vendor.id,
        label: vendor.name,
      }));
    }

    return [];
  };
  
  const FormField = ({
    field,
    index,
  }: {
    field: FormField;
    index?: number;
  }) => {
    return (
      <div className="mt-3">
        <label
          className="flex items-center gap-2"
          htmlFor={`field-${field.id}`}
        >
          {field.label}{" "}
          {index && <span className="text-gray-500">({index})</span>}
          {field.required && <span className="text-red-500">*</span>}
        </label>

        {field.type === "vendorSelect" && (
          <AsyncSelect
            isClearable
            isSearchable
            // cacheOptions
            //@ts-ignore
            loadOptions={fetchVendors}
            getOptionLabel={(vendor: Vendor) => vendor.name}
            getOptionValue={(vendor: Vendor) => vendor.id}
            placeholder="Search in vendors"
            classNames={{
              control: () =>
                "!border !border-default-600 py-[1px] !rounded-lg w-full",
            }}
          />
        )}

        {['text', 'uFirstName', 'uLastName'].includes(field.type) && (
          <input
            placeholder={field.placeholder}
            name={field.name}
            type={field.type}
            id={
              field.id?.toString() ||
              Math.random().toString(16).slice(2, 8).toUpperCase()
            }
            className="w-full rounded-lg border border-default-600 px-4 py-2"
            defaultValue={field.defaultValue || ""}
          />
        )}

        {field.type === "password" && (
          <input
            placeholder={field.placeholder}
            name={field.name}
            type={field.type}
            id={
              field.id?.toString() ||
              Math.random().toString(16).slice(2, 8).toUpperCase()
            }
            className="w-full rounded-lg border border-default-600 px-4 py-2"
            defaultValue={field.defaultValue}
          />
        )}

        {field.type === "range" && (
          <div className="relative mb-6">
            <label htmlFor="labels-range-input" className="sr-only">
              {field.label}
            </label>

            <input
              type="range"
              placeholder={field.placeholder}
              name={field.name}
              id={
                field.id?.toString() ||
                Math.random().toString(16).slice(2, 8).toUpperCase()
              }
              className="w-full rounded-lg border border-default-600 px-4 py-2"
              min={field.min}
              max={field.max}
              defaultValue={field.defaultValue}
            />
            <span className="absolute -bottom-6 start-0 text-sm text-gray-500 dark:text-gray-400">
              Min ({field.min})
            </span>

            {field.max && (
              <span className="absolute -bottom-6 start-1/2 -translate-x-1/2 text-sm text-gray-500 dark:text-gray-400 rtl:translate-x-1/2">
                {field.max / 2}
              </span>
            )}
            <span className="absolute -bottom-6 end-0 text-sm text-gray-500 dark:text-gray-400">
              Max ({field.max}))
            </span>
          </div>
        )}

        {field.type === "color" && (
          <input
            placeholder={field.placeholder}
            name={field.name}
            type={field.type}
            id={
              field.id?.toString() ||
              Math.random().toString(16).slice(2, 8).toUpperCase()
            }
            className="w-full rounded-lg border border-default-600 px-4 py-2"
            defaultValue={field.defaultValue}
          />
        )}

        {(field.type === "email" || field.type === "uEmail") && (
          <input
            placeholder={field.placeholder}
            name={field.name}
            type={field.type}
            id={
              field.id?.toString() ||
              Math.random().toString(16).slice(2, 8).toUpperCase()
            }
            className="w-full rounded-lg border border-default-600 px-4 py-2"
            defaultValue={field.defaultValue}
          />
        )}

        {(field.type === "tel" || field.type === "uPhone") && (
          <input
            placeholder={field.placeholder}
            name={field.name}
            type={field.type}
            id={
              field.id?.toString() ||
              Math.random().toString(16).slice(2, 8).toUpperCase()
            }
            className="w-full rounded-lg border border-default-600 px-4 py-2"
            defaultValue={field.defaultValue}
          />
        )}

        {field.type === "textarea" && (
          <textarea
            placeholder={field.placeholder}
            name={field.name}
            id={
              field.id?.toString() ||
              Math.random().toString(16).slice(2, 8).toUpperCase()
            }
            className="w-full rounded-lg border border-default-600 px-4 py-2"
            defaultValue={field.defaultValue}
          />
        )}

        {field.type === "number" && (
          <input
            placeholder={field.placeholder}
            name={field.name}
            type={field.type}
            id={
              field.id?.toString() ||
              Math.random().toString(16).slice(2, 8).toUpperCase()
            }
            className="w-full rounded-lg border border-default-600 px-4 py-2"
            defaultValue={field.defaultValue}
          />
        )}

        {field.type === "date" && (
          <input
            placeholder={field.placeholder}
            name={field.name}
            type={field.type}
            id={
              field.id?.toString() ||
              Math.random().toString(16).slice(2, 8).toUpperCase()
            }
            className="w-full rounded-lg border border-default-600 px-4 py-2"
            defaultValue={field.defaultValue}
          />
        )}

        {field.type === "time" && (
          <input
            placeholder={field.placeholder}
            name={field.name}
            type={field.type}
            id={
              field.id?.toString() ||
              Math.random().toString(16).slice(2, 8).toUpperCase()
            }
            className="w-full rounded-lg border border-default-600 px-4 py-2"
            defaultValue={field.defaultValue}
          />
        )}

        {field.type === "select" && (
          <select
            name={field.name}
            id={
              field.id?.toString() ||
              Math.random().toString(16).slice(2, 8).toUpperCase()
            }
            className="w-full rounded-lg border border-default-600 px-4 py-2"
            defaultValue={field.defaultValue}
          >
            {field.options?.map((option, oi) => (
              <option key={`select-opt-${option.value}`} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        )}

        {field.type === "radio" &&
          field.options?.map((option, oi) => (
            <div key={`radio-opt-${option.value}`} className="my-2">
              <label className="flex items-center space-x-2">
                <input
                  type="radio"
                  name={field.name}
                  value={option.value}
                  id={`radio-${field.id}-${option.value}`}
                />

                <span>{option.label}</span>
              </label>
            </div>
          ))}

        {field.type === "multicheck" &&
          field.options?.map((option, oi) => (
            <div key={`checkbox-opt-${option.value}`} className="my-2">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  name={field.name}
                  value={option.value}
                  id={`checkbox-${field.id}-${option.value}`}
                />

                <span>{option.label}</span>
              </label>
            </div>
          ))}

        {field.type === "checkbox" &&
          field.options?.map((option, oi) => (
            <div key={`checkbox-opt-${option.value}`} className="my-2">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  name={field.name}
                  value={option.value}
                  id={`checkbox-${field.id}-${option.value}`}
                />

                <span>{option.label}</span>
              </label>
            </div>
          ))}

        {(field.type === "file" || field.type === 'image') && (
          <div className="flex h-32 items-center justify-center rounded-lg border border-dashed border-default-600">
            <input
              type="file"
              name={field.name}
              id={
                field.id?.toString() ||
                Math.random().toString(16).slice(2, 8).toUpperCase()
              }
              className="hidden"
            />
            <p>{field.placeholder || field.label}</p>
          </div>
        )}

        {field.type === "slot" && (
          <div className="flex space-x-2">
            <div className="flex-1">
              <input
                placeholder={field.placeholder}
                name={field.name}
                type="date"
                id={`${field.id}-date`}
                className="w-full rounded-lg border border-default-600 px-4 py-2"
              />
            </div>

            <div className="flex-1">
              <input
                placeholder={field.placeholder}
                name={field.name}
                type="time"
                id={`${field.id}-time`}
                className="w-full rounded-lg border border-default-600 px-4 py-2"
              />
            </div>
          </div>
        )}
      </div>
    );
  };

  const FormSection = ({
    section,
    index,
  }: {
    section: FormSection;
    index?: number;
  }): JSX.Element => {
    return (
      <div
        key={`section-${section.id}` + (index ? `-${index}` : "")}
        className="rounded-lg bg-default-100 p-4"
      >
        <h3 className="font-bold text-lg">
          {section.name} {index && <span>({index})</span>}
        </h3>

        <p className="text-sm">{section.details}</p>

        {section.fields?.map((field, f) => {
          return field.repeatable && field.repeats && field.repeats > 0 ? (
            Array.from(Array(Number(field.repeats))).map((_, i) => (
              <FormField
                key={`${section.id}-field-${field.id}-${i}`}
                field={field}
                index={i + 1}
              />
            ))
          ) : (
            <FormField key={`${section.id}-field-${field.id}`} field={field} />
          );
        })}
      </div>
    );
  };

  const { handleSubmit } = useForm({
    defaultValues: {
      sections,
    },
  });

  return (
    <div className="mt-4 flex flex-col gap-4">
      {sections.map((section) => {
        return section.repeatable && section.repeats && section.repeats > 0 ? (
          Array.from(Array(Number(section.repeats))).map((_, i) => (
            <FormSection
              key={`section-${section.id}-${i}`}
              section={section}
              index={i + 1}
            />
          ))
        ) : (
          <FormSection key={`section-${section.id}`} section={section} />
        );
      })}

      {onSubmit && (
        <button
          onClick={() => handleSubmit(onSubmit)}
          className="rounded-lg bg-primary px-4 py-2 text-white"
        >
          Update form
        </button>
      )}
    </div>
  );
}
