import React from "react";
import <PERSON>Field from "./form-field";

export default function FormSection({
  section,
  index,
}: {
  section: FormSection;
  index?: number;
}): JSX.Element {
  return (
    <div
      key={`section-${section.id}` + (index ? `-${index}` : "")}
      className="rounded-lg bg-default-100 p-4"
    >
      <h3 className="fonr-bold text-lg">
        {section.name} {index && <span>({index})</span>}
      </h3>
      <p>{section.details}</p>

      {section.fields?.map((field, f) => {
        return field.repeatable && field.repeats && field.repeats > 0 ? (
          Array.from(Array(Number(field.repeats))).map((_, i) => (
            <FormField
              key={`field-${field.id}-${i}`}
              field={field}
              index={i + 1}
            />
          ))
        ) : (
          <FormField key={`field-${field.id}`} field={field} />
        );
      })}
    </div>
  );
}
