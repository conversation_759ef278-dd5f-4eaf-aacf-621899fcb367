import {
  Button,
  Dropdown,
  DropdownItem,
  DropdownMenu,
  DropdownTrigger,
} from "@nextui-org/react";
import React from "react";
import { Icon } from "./icon";

const LanguageSwitcher = () => {
  const [selectedKeys, setSelectedKeys] = React.useState(new Set(["english"]));

  const selectedValue = React.useMemo(
    () => Array.from(selectedKeys).join(", ").replaceAll("_", " "),
    [selectedKeys],
  );

  return (
    <Dropdown showArrow placement="bottom-end">
      <DropdownTrigger>
        <Button isIconOnly className="border" size="sm">
          <Icon
            name="icon-[heroicons--language-20-solid]"
            classNames="h-5 w-5"
          />
        </Button>
      </DropdownTrigger>

      <DropdownMenu
        selectionMode="single"
        selectedKeys={selectedKeys}
        onSelectionChange={setSelectedKeys as any}
      >
        <DropdownItem
          key="english"
          startContent={<Icon name="icon-[twemoji--flag-england]" />}
        >
          English
        </DropdownItem>
        <DropdownItem
          key="german"
          startContent={<Icon name="icon-[twemoji--flag-germany]" />}
        >
          German
        </DropdownItem>
        <DropdownItem
          key="italian"
          startContent={<Icon name="icon-[twemoji--flag-italy]" />}
        >
          Italian
        </DropdownItem>
        <DropdownItem
          key="spanish"
          startContent={<Icon name="icon-[twemoji--flag-spain]" />}
        >
          Spanish
        </DropdownItem>
        <DropdownItem
          key="russian"
          startContent={<Icon name="icon-[twemoji--flag-russia]" />}
        >
          Russian
        </DropdownItem>
      </DropdownMenu>
    </Dropdown>
  );
};

export default LanguageSwitcher;
