"use client";

import Link from "next/link";
import { usePathname, useSearchParams, useRouter } from "next/navigation";

export default function Pagination({
  meta,
  path = "users",
}: {
  meta: PaginationMeta;
  path: string;
}) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const query: Record<string, string> = {};

  for (const [key, value] of searchParams.entries()) {
    query[key] = value;
  }

  const makeRoute = (page: number, query: any): string => {
    query["page"] = page.toString();
    const keys = Object.keys(query);

    return pathname + keys.length
      ? "?" + keys.map((k) => `${k}=${query[k]}`).join("&")
      : `?page=${page}`;
  };

  const pager = () => {
    let pagination = [],
      i = 1;

    while (i <= meta.lastPage) {
      if (
        i <= 3 || //the first three pages
        i >= meta.lastPage - 2 || //the last three pages
        (i >= meta.currentPage - 1 && i <= meta.currentPage + 1)
      ) {
        //the currentPage, the page before and after
        pagination.push(
          <li key={i} value={i}>
            <Link
              href={makeRoute(i, query)}
              className={
                "border border-default-300 px-3 py-2 leading-tight hover:bg-default-100 hover:text-default-700 dark:border-default-700 dark:bg-default-800 dark:text-default-400 dark:hover:bg-default-700 dark:hover:text-white " +
                (meta.currentPage === i
                  ? "bg-default-500 text-white"
                  : "bg-white text-default-500")
              }
            >
              {i}
            </Link>
          </li>,
        );
        i++;
      } else {
        //any other page should be represented by ...
        pagination.push(
          <li className="flex items-center justify-center border border-default-300 px-3 py-2 leading-tight hover:bg-default-100 hover:text-default-700 dark:border-default-700 dark:bg-default-800 dark:text-default-400 dark:hover:bg-default-700 dark:hover:text-white">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="h-5 w-5 text-default-500 dark:text-default-400"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M6.75 12a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM12.75 12a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM18.75 12a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0Z"
              />
            </svg>
          </li>,
        );
        //jump to the next page to be linked in the navigation
        i = i < meta.currentPage ? meta.currentPage - 1 : meta.lastPage - 2;
      }
    }
    return pagination;
  };

  return (
    <div className="flex w-full items-center justify-between px-4">
      <div>
        Showing {meta.currentPage * meta.perPage - meta.perPage + 1} to{" "}
        {(meta.currentPage + 1) * meta.perPage - meta.perPage} of {meta.total}
      </div>

      <nav aria-label="page navigation example">
        <ul className="inline-flex items-center -space-x-px">
          <li>
            <Link
              href={makeRoute(meta.currentPage - 1, query)}
              aria-disabled={meta.currentPage === 1}
              className="ml-0 block rounded-l-lg border border-default-300 bg-white px-3 py-2 leading-tight text-default-500 hover:bg-default-100 hover:text-default-700 dark:border-default-700 dark:bg-default-800 dark:text-default-400 dark:hover:bg-default-700 dark:hover:text-white"
            >
              <span className="sr-only">Previous</span>
              <svg
                aria-hidden="true"
                className="h-5 w-5"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fillRule="evenodd"
                  d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                  clipRule="evenodd"
                ></path>
              </svg>
            </Link>
          </li>

          {pager()}

          <li>
            <Link
              href={makeRoute(meta.currentPage + 1, query)}
              aria-disabled={meta.currentPage === meta.lastPage}
              className="block rounded-r-lg border border-default-300 bg-white px-3 py-2 leading-tight text-default-500 hover:bg-default-100 hover:text-default-700 dark:border-default-700 dark:bg-default-800 dark:text-default-400 dark:hover:bg-default-700 dark:hover:text-white"
            >
              <span className="sr-only">Next</span>
              <svg
                aria-hidden="true"
                className="h-5 w-5"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fillRule="evenodd"
                  d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                  clipRule="evenodd"
                ></path>
              </svg>
            </Link>
          </li>
        </ul>
      </nav>
      {/* <div className="g">
            <ul className="pagination flex justify-center md:justify--start">
                <li className={"page-item " + (meta.previousPageUrl ? '' : 'disabled')}>
                    <Link
                        className="page-link" href={meta.previousPageUrl ? makeRoute(meta.currentPage - 2, query) : "#"}>
                        <em className="icon ni ni-chevrons-left"/>
                    </Link>
                </li>

                {Array.from(Array(meta.lastPage).keys())
                    .map((page) => {
                        return <li className={"page-item" + (meta.currentPage === page + 1 ? ' active' : '')} key={page}>
                            {page === 5
                                ? <span className="page-link">
                                    <em className="icon ni ni-more-h"/>
                                </span>

                                : <Link className="page-link"
                                    href={makeRoute(page, query)}
                                >
                                    {page + 1}
                                </Link>}
                        </li>
                    })}

                <li className={"page-item " + (meta.nextPageUrl ? '' : 'disabled')}>
                    <Link className="page-link" href={meta.nextPageUrl ? makeRoute(meta.currentPage, query) : "#"}>
                        <em className="icon ni ni-chevrons-right"/>
                    </Link>
                </li>
            </ul>
        </div>

        <div className="g">
            <div className="pagination-goto d-flex justify-content-center justify-content-md-start gx-3">
                <div>Page</div>
                <div>
                    <select
                        className="form-select"
                        aria-hidden="true"
                        defaultValue={meta.currentPage}>
                        {Array.from(Array(meta.lastPage).keys())
                            .map((page) => (<option
                                key={page} value={page + 1}
                            >
                                {page + 1}
                            </option>)
                            )}
                    </select>
                </div>
                <div>OF {meta.lastPage}</div>
            </div>
        </div> */}
    </div>
  );
}
