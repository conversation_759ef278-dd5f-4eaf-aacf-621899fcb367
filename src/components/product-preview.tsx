import { imagePath } from "@/lib/api";
import Image from "next/image";

export default function ProductPreview({
  product,
  branch,
}: {
  product: ProductPayload;
  branch?: Branch;
}) {
  return (
    <div className="grid grid-cols-2 gap-5">
      <div>
        <Image
          src={imagePath(product.image?.url)}
          width={500}
          height={500}
          alt={product.name}
          className="w-full"
        />
      </div>
      <div className="space-y-4">
        <div className="flex justify-between">
          <h1 className="text-4xl">{product.name}</h1>

          <div className="text-3xl">
            <span className={product.discounted ? "line-through" : ""}>
              KES {product.price}
            </span>

            {product.discounted && (
              <>
                <span>KES {product.discounted}</span>
                <span className="text-sm"> / item</span>
              </>
            )}
          </div>
        </div>

        {!branch && (
          <div>
            <div className="mb-3 flex items-center gap-3 rounded-lg">
              <Image
                src={imagePath(product.vendor?.logo?.url)}
                width={600}
                height={400}
                className="h-14 w-28 rounded-lg"
                alt={product.vendor?.name}
              />

              <h1 className="text-lg font-bold">{product.vendor?.name}</h1>
            </div>

            <p className="prose text-justify text-sm">
              {product.vendor?.details}
            </p>
          </div>
        )}

        <article
          className="prose text-sm"
          dangerouslySetInnerHTML={{ __html: product.details }}
        />

        <div className="flex flex-wrap space-x-2">
          <p>Tags:</p>
          {product.tags.map((tag) => (
            <div
              key={tag.id}
              className="rounded-lg bg-primary px-4 py-1 text-sm text-white"
            >
              {tag.name}
            </div>
          ))}
        </div>

        <div className="grid grid-cols-3 gap-4 [&>p]:rounded-lg [&>p]:border [&>p]:p-1">
          <p>
            <span className="mr-2">Stock:</span>
            {product.stock}
          </p>
          <p>
            <span className="mr-2">Rating:</span>
            {product.active ? "Active" : "Inactive"}
          </p>
          <p>
            <span className="mr-2">Featured:</span>
            {product.featured ? "Yes" : "No"}
          </p>
          <p>
            <span className="mr-2">Type:</span>
            {product.type}
          </p>
          <p>
            <span className="mr-2">Condition:</span>
            {product.condition}
          </p>
          <p>
            <span className="mr-2">Status:</span>
            {product.status}
          </p>
          <p>
            <span className="mr-2">Availability:</span>
            {product.availability}
          </p>
          <p>
            <span className="mr-2">Shipping:</span>
            {product.shipping}
          </p>
          <p>
            <span className="mr-2">Unit:</span>
            {product.unit}
          </p>
          <p>
            <span className="mr-2">Mode:</span>
            {product.mode}
          </p>
          <p>
            <span className="mr-2">Payment:</span>
            {product.payment}
          </p>
          <p>
            <span className="mr-2">Visibility:</span>
            {product.visibility}
          </p>
          <p>
            <span className="mr-2">Created on:</span>
            {product.createdAt}
          </p>
          <p>
            <span className="mr-2">Updated on:</span>
            {product.updatedAt}
          </p>
          <p>
            <span className="mr-2">Expires on:</span>
            {product.expiresAt}
          </p>
          <p>
            <span className="mr-2">HasExpiry:</span>
            {product.hasExpiry ? "Yes" : "No"}
          </p>
        </div>
      </div>
    </div>
  );
}
