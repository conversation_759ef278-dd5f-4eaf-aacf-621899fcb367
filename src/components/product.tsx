"use client";

import { useContext } from "react";
import { CartContext } from "@/providers/CartContext";
import Image from "next/image";
import Link from "next/link";
import { MdShoppingCartCheckout } from "react-icons/md";
import { imagePath } from "@/lib/api";

export default function ProductCard({ product }: { product: Product }) {
  const { addToCart } = useContext(CartContext);

  return (
    <div className="lg:aspect-none group block rounded-xl border bg-gray-200 py-2 group-hover:opacity-75 lg:h-60">
      <Link
        href={`/products/${product.id}`}
        className="aspect-h-1 aspect-w-1 w-full"
      >
        <Image
          src={imagePath(product.image?.url)}
          alt="Front of men&#039;s Basic Tee in black."
          className="h-full w-full object-cover object-center lg:h-full lg:w-full"
          width={100}
          height={100}
        />
      </Link>

      <div className="mt-4 px-2">
        <div className="flex justify-between">
          <h1 className="text-lg font-bold text-gray-700">
            <Link href={`/products/${product.id}`}>{product.name}</Link>
          </h1>

          {product.meta?.delivery && (
            <p className="mt-1 text-sm text-gray-500">
              {product.meta?.delivery}
            </p>
          )}
        </div>

        <div>
          <p>{product?.vendor?.name}</p>
        </div>

        <div className="mb-2 flex items-center justify-between">
          <h3 className="text-xl text-gray-700">KES {product.price}</h3>

          <button className="" onClick={() => addToCart(product)}>
            <MdShoppingCartCheckout className="h-6 w-6 text-teal-700" />
          </button>
        </div>
      </div>
    </div>
  );
}
