"use client";

import { imagePath } from "@/lib/api";
import { Chip, User } from "@nextui-org/react";
import { formatDistance } from "date-fns";
import Link from "next/link";
import toast from "react-hot-toast";

import { CustomTable } from "../custom-table";
import { Icon } from "../icon";

export default function CustomersTable({
  data,
  meta,
  action,
}: {
  data: User[];
  meta: PaginationMeta;
  action: (formData: FormData) => Promise<void>;
}) {
  const onDeleteSubmit = async (id: string) => {
    const formData = new FormData();
    formData.append("id", id);

    toast.promise(action(formData), {
      loading: "Deleting customer...",
      success: "Customer deleted!",
      error: "Failed to delete customer",
    });
  };

  return (
    <CustomTable
      title="Customers"
      columns={[
        {
          name: "Customer",
          uid: "customer",
          renderCell: (customer: User) => (
            <Link
              href={`/customers/${customer.id}`}
              className="flex items-center space-x-2"
            >
              <User
                name={customer.name}
                description={customer.email}
                avatarProps={{
                  src: `${imagePath(customer?.avatar?.url, customer.avatarUrl)}`,
                }}
                classNames={{
                  description: "text-default-600",
                  name: "font-bold",
                }}
              />
            </Link>
          ),
        },
        { name: "Phone", uid: "phone" },
        {
          name: "Status",
          uid: "status",
          renderCell: (customer) => (
            <Chip
              color={customer.status === "Active" ? "success" : "warning"}
              variant="flat"
              className="text-xs"
            >
              {customer.status}
            </Chip>
          ),
        },
        {
          name: "Created At",
          uid: "created At",
          sortable: true,
          renderCell: (customer: User) => (
            <span className="text-default-700">
              {formatDistance(new Date(customer.createdAt), new Date(), {
                addSuffix: true,
              })}
            </span>
          ),
        },
        {
          name: "Actions",
          uid: "actions",
          renderCell: (customer: User) => (
            <div className="flex w-fit items-center gap-5">
              <Link href={`/customers/${customer.id}/edit`}>
                <Icon
                  name="icon-[mage--edit-pen-fill]"
                  classNames="text-primary"
                />
              </Link>
              <form
                onSubmit={(e) => {
                  e.preventDefault();
                  onDeleteSubmit(customer.id);
                }}
              >
                <button
                  name="id"
                  value={customer.id}
                  type="submit"
                  className="text-red-500"
                >
                  <Icon name="icon-[mingcute--delete-2-line]" />
                </button>
              </form>
            </div>
          ),
        },
      ]}
      data={data}
      meta={meta}
      filter={[
        {
          column: "status",
          displayName: "Status",
          values: [
            { name: "Active", value: "Active" },
            { name: "Inactive", value: "Inactive" },
            { name: "Suspended", value: "Suspended" },
          ],
        },
      ]}
    />
  );
}
