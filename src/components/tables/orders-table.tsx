"use client";

import { CustomTable } from "@/components/custom-table";
import { Icon } from "@/components/icon";
import { imagePath } from "@/lib/api";
import { Chip, User } from "@nextui-org/react";
import { format as formatDate } from "date-fns";
import Link from "next/link";

function OrdersTable({
  data,
  meta,
}: {
  data: TempOrder[];
  meta: PaginationMeta;
}) {
  return (
    <CustomTable
      title="Orders"
      columns={[
        {
          name: "Customer",
          uid: "customer",
          renderCell: (order: TempOrder) => (
            <Link
              href={`/customers/${order.customer.id}`}
              className="flex items-center space-x-2"
            >
              <User
                name={order.customer.name}
                description={order.customer.email}
                avatarProps={{
                  src: `${imagePath(order.customer?.avatar?.url, order.customer.avatarUrl)}`,
                }}
                classNames={{
                  description: "text-default-600",
                  name: "font-bold",
                }}
              />
            </Link>
          ),
        },
        {
          name: "Amount",
          uid: "amount",
          renderCell: (order: TempOrder) => <>KES {order.total}</>,
        },

        {
          name: "Status",
          uid: "status",
          renderCell: (order) => (
            <Chip
              color={
                order.status === "Completed"
                  ? "success"
                  : order.status === "Pending"
                    ? "warning"
                    : order.status === "Placed"
                      ? "secondary"
                      : "default"
              }
              variant="flat"
              className="text-xs"
            >
              {order.status}
            </Chip>
          ),
        },
        {
          name: "Date",
          uid: "createdAt",
          sortable: true,
          renderCell: (order: TempOrder) => (
            <>
              {formatDate(
                new Date(order.createdAt),
                "EEE, MMM dd, yyyy hh:mm a",
              )}
            </>
          ),
        },
        {
          name: "Actions",
          uid: "actions",
          renderCell: (order: TempOrder) => (
            <div className="flex w-fit items-center gap-5">
              <Link href={`/temp-orders/${order.id}`}>
                <Icon
                  name="icon-[mingcute--eye-line]"
                  classNames="text-primary"
                />
              </Link>

              <Link href={`/temp-orders/${order.id}/edit`}>
                <Icon
                  name="icon-[mage--edit-pen-fill]"
                  classNames="text-primary"
                />
              </Link>
            </div>
          ),
        },
      ]}
      data={data}
      meta={meta}
      filter={[]}
    />
  );
}

export default OrdersTable;
