"use client";

import Link from "next/link";
import Image from "next/image";
import { Chip } from "@nextui-org/react";
import { Session } from "next-auth";
import toast from "react-hot-toast";

import { imagePath } from "@/lib/api";
import EditProductBranch from "@/app/(authenticated)/products/edit-branch";
import EditProduct from "@/app/(authenticated)/products/edit";

import { CustomTable } from "../custom-table";
import { Icon } from "../icon";

function ProductsTable({
  data,
  meta,
  action,
  session,
  updateRecord,
  loadVendors,
}: {
  data: Product[];
  meta: PaginationMeta;
  action: (formData: FormData) => Promise<void>;
  session: Session | null;
  updateRecord: (product: Product) => Promise<void>;
  loadVendors: (s: string) => Promise<Vendor[]>;
}) {
  const onDeleteSubmit = async (id: string) => {
    const formData = new FormData();
    formData.append("id", id);

    toast.promise(action(formData), {
      loading: "Deleting product...",
      success: "Product deleted!",
      error: "Failed to delete product",
    });
  };

  const columns = [
    {
      uid: "name",
      name: "Name",
      renderCell: (product: Product) => (
        <Link href={`/products/${product.id}`} className="flex items-center">
          <Image
            src={imagePath(product.image?.url)}
            alt={product.name}
            width={40}
            height={40}
            className="size-16 rounded-lg"
          />
          <div className="ms-2">
            <p className="tb-lead">{product.name}</p>
            <span className="tb-lead">{product.ref}</span>
          </div>
        </Link>
      ),
    },

    {
      uid: "serviceId",
      name: "Service",
      renderCell: (product: Product) => (
        <Link
          href={`/services/${product.serviceId}`}
          className="flex items-center space-x-2"
        >
          <Image
            src={imagePath(product.service?.image?.url)}
            alt={product.service?.name}
            width={100}
            height={100}
            className="size-12 rounded-lg"
          />

          <div className="flex-1">
            <h1>{product.service?.name}</h1>
            <p>{product.service?.details}</p>
          </div>
        </Link>
      ),
    },

    {
      uid: "price",
      name: "Price",
      renderCell: (product: Product) => <>KES {product.price}</>,
      sortable: true,
    },

    {
      uid: "stock",
      name: "Stock",
      sortable: true,
    },

    {
      uid: "status",
      name: "Status",
      renderCell: (product: Product) => (
        <Chip
          color={
            product.status === "Published"
              ? "success"
              : product.status === "Archived"
                ? "warning"
                : product.status === "Draft"
                  ? "secondary"
                  : "default"
          }
          variant="flat"
          className="text-xs"
        >
          {product.status}
        </Chip>
      ),
    },

    {
      name: "Actions",
      uid: "actions",
      renderCell: (product: Product) => (
        <div className="flex w-fit items-center gap-5">
          <Link
            href={`/products/${product.id}/forms`}
            className="rounded-lg bg-primary px-5 py-2 text-white"
          >
            Forms
          </Link>

          <Link href={`/products/${product.id}/edit`}>
            <Icon name="icon-[mage--edit-pen-fill]" classNames="text-primary" />
          </Link>

          <form
            onSubmit={(e) => {
              e.preventDefault();
              onDeleteSubmit(product.id);
            }}
          >
            <button
              name="id"
              value={product.id}
              type="submit"
              className="text-red-500"
            >
              <Icon name="icon-[mingcute--delete-2-line]" />
            </button>
          </form>
        </div>
      ),
    },
  ];

  if (session?.branch) {
  } else {
    columns.splice(2, 0, {
      uid: "vendor.name",
      name: "Vendor",
      renderCell: (product: Product) =>
        product.vendorId ? (
          <Link href={`/vendors/${product.vendorId}`}>
            <span>{product.vendor?.name}</span>
          </Link>
        ) : (
          <>
              <EditProduct
                defaultValues={product}
                updateRecord={updateRecord}
                loadVendors={loadVendors}
              />
          
          </>
        ),
    });

    columns.splice(3, 0, {
      uid: "branch.name",
      name: "Branch",
      renderCell: (product: Product) =>
        product.branchId ? (
          <Link href={`/branches/${product.branchId}`}>
            <span>{product.branch?.name || "Branch"}</span>
          </Link>
        ) : (
          <EditProductBranch
            defaultValues={product}
            updateRecord={updateRecord}
          />
        ),
    });
  }

  return (
    <CustomTable
      title="Products"
      columns={columns}
      data={data}
      meta={meta}
      filter={[
        // {
        //   column: "vendorId",
        //   displayName: "Vendor",
        //   values:
        //     vendors?.data?.map((vendor) => ({
        //       name: vendor.name,
        //       value: vendor.id,
        //     })) ?? [],
        // },
        // {
        //   column: "serviceId",
        //   displayName: "Service",
        //   values:
        //     services?.data?.map((service) => ({
        //       name: service.name,
        //       value: service.id,
        //     })) ?? [],
        // },
        {
          column: "status",
          displayName: "Status",
          values: [
            { name: "Draft", value: "Draft" },
            { name: "Pending", value: "Pending" },
            { name: "Published", value: "Published" },
            { name: "Unpublished", value: "Unpublished" },
            { name: "Archived", value: "Archived" },
          ],
        },
      ]}
      action={
        <Link
          className="flex items-center space-x-3 rounded-lg bg-primary px-6 py-2 text-white"
          href="/product-templates/create"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
            className="h-4 w-4"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M12 4.5v15m7.5-7.5h-15"
            />
          </svg>
          <span className="s-only">Add product template</span>
        </Link>
      }
    />
  );
}

export default ProductsTable;
