"use client";

import Link from "next/link";

import { CustomTable } from "../custom-table";
import { Icon } from "../icon";
import { Button } from "@nextui-org/react";

function ServicesProductsTable({
  data,
  meta,
  title,
  serviceId
}: {
  data: Product[];
  meta: PaginationMeta;
  title: string;
  serviceId: string;
}) {
  return (
    <CustomTable
      columns={[
        {
          uid: "name",
          name: "Name",
          renderCell: (record) => (
            <Link href={`/products/${record.id}`}>{record.name}</Link>
          ),
        },
        { uid: "price", name: "Price" },
        { uid: "stock", name: "Stock" },
        {
          uid: "actions",
          name: "Actions",
          renderCell: (record) => (
            <Link href={`/products/${record.id}`}>
              <Icon
                name="icon-[mage--edit-pen-fill]"
                classNames="text-primary"
              />
            </Link>
          ),
        },
      ]}
      title={title}
      data={data}
      meta={meta}
      action={
        <Button
          as={Link}
          variant="solid"
          color="primary"
          href={`/products/create?serviceId=${serviceId}`}
          endContent={<Icon name="icon-[ic--round-add]" />}
        >
          Create new product
        </Button>
      }
    />
  );
}

export default ServicesProductsTable;
