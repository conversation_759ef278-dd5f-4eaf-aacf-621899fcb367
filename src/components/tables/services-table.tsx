"use client";

import ServiceStatus from "@/app/(authenticated)/tasks/[taskId]/status";
import { CustomTable } from "@/components/custom-table";
import { Icon } from "@/components/icon";
import { imagePath } from "@/lib/api";
import { Button } from "@nextui-org/react";
import Image from "next/image";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import React from "react";
import toast from "react-hot-toast";

export default function ServicesTable({
  role,
  data,
  meta,
  taskId,
  action,
  toggle,
}: {
  role: any;
  data: Service[];
  meta: PaginationMeta;
  taskId?: string;
  action: (formData: FormData) => Promise<void>;
  toggle: (service: Service) => Promise<void>;
}) {
  const query = useSearchParams();
  const onDeleteSubmit = async (id: string) => {
    const formData = new FormData();
    formData.append("id", id);

    toast.promise(action(formData), {
      loading: "Deleting service...",
      success: "Service deleted!",
      error: "Failed to delete service",
    });
  };

  // Checking whether the logged in user is a vendor or an admin


  return (
    <CustomTable
      title="tasks"
      columns={[
        {
          name: "Service",
          uid: "service",
          sortable: true,
          renderCell: (service: Service) => {
            return (
              <Link
                href={`/services/${service.id}`}
                className="flex items-center space-x-3"
              >
                <div className="rounded-md border p-1">
                  <Image
                    src={imagePath(service.image?.url, null)}
                    alt={service.name}
                    width={30}
                    height={30}
                  />
                </div>
                <div>
                  <p className="text-sm font-bold">{service.name}</p>
                  <p className="text-xs text-gray-600">{service.details}</p>
                </div>
              </Link>
            );
          },
        },
        { name: "Slug", uid: "slug", sortable: true },
        {
          name: "Active",
          uid: "active",
          renderCell: (service: Service) => (
            <ServiceStatus service={service} toggleServiceStatus={toggle} />
          ),
        },
        {
          name: "Actions",
          uid: "actions",
          renderCell: (service: Task) => (
            <div className="flex w-fit items-center gap-5">
              <>
                <Link href={`/services/${service.id}/edit`}>
                  <Icon
                    name="icon-[mage--edit-pen-fill]"
                    classNames="text-primary"
                  />
                </Link>
                <form
                  onSubmit={(e) => {
                    e.preventDefault();
                    onDeleteSubmit(service.id);
                  }}
                >
                  <button
                    name="id"
                    value={service.id}
                    type="submit"
                    className="text-red-500"
                  >
                    <Icon name="icon-[mingcute--delete-2-line]" />
                  </button>
                </form>
              </>
            </div>
          ),
        },
      ]}
      data={data}
      meta={meta}
      filter={{
        column: "active",
        values: [
          { name: "Active", value: "true" },
          { name: "Inactive", value: "false" },
        ],
      }}
      action={
        role.roles[0].name === "admin" ?<Button
          as={Link}
          variant="solid"
          color="primary"
          href={`/services/create?task=${taskId}`}
          endContent={<Icon name="icon-[ic--round-add]" />}
          // hide if role is vendor
        >
          Create new service
        </Button> 
        : null
      }
    />
  );
}
