"use client";

import Link from "next/link";
import AdminVendorTypesCreateForm from "../../app/(authenticated)/vendors/types/create";
import { CustomTable } from "../custom-table";
import { Icon } from "../icon";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";

function ServiceVendorTypesTable({
  data,
  meta,
  serviceId,
  action,
  deleteVendorType,
}: {
  data: VendorType[];
  meta: PaginationMeta;
  serviceId: string;
  action: (data: FormData) => Promise<void>;
  deleteVendorType: (data: FormData) => Promise<void>;
}) {
  return (
    <CustomTable
      title=""
      columns={[
        {
          uid: "name",
          name: "Name",
          renderCell: (type) => (
            <Link href={`/vendors/types/${type.id}`}>{type.name}</Link>
          ),
        },
        { uid: "details", name: "Details" },
        {
          uid: "actions",
          name: "Actions",
          renderCell: (type) => {
            return (
              <div className="flex space-x-2">
                <Sheet>
                  <SheetTrigger asChild>
                    <Button variant="outline" size={"icon"}>
                      <Icon
                        name="icon-[mage--edit-pen-fill]"
                        classNames="text-primary"
                      />
                    </Button>
                  </SheetTrigger>
                  <SheetContent>
                    <SheetHeader>
                      <SheetTitle className="text-primary">
                        Edit vendor type
                      </SheetTitle>
                      <SheetDescription className="text-primary">
                        Enter details below.
                      </SheetDescription>
                    </SheetHeader>
                    <div className="grid gap-4 py-4">
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="name" className="text-right">
                          Name
                        </Label>
                        <Input
                          id="name"
                          value={type.name}
                          className="col-span-3"
                        />
                      </div>
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="username" className="text-right">
                          Description
                        </Label>
                        <Input
                          id="username"
                          value={type.details}
                          className="col-span-3"
                        />
                      </div>
                    </div>
                    <SheetFooter>
                      <SheetClose asChild>
                        <Button type="submit" className="bg-primary">
                          Save changes
                        </Button>
                      </SheetClose>
                    </SheetFooter>
                  </SheetContent>
                </Sheet>

                <form action={deleteVendorType} className="btn btn-danger">
                  <input type="hidden" name="id" value={type.id} />
                  <Button variant="outline" size={"icon"} type="submit">
                    <Icon
                      name="icon-[mingcute--delete-2-line]"
                      classNames="text-red-500"
                    />
                  </Button>
                </form>
              </div>
            );
          },
        },
      ]}
      data={data}
      meta={meta}
      action={
        <div className="mb-4 flex items-center justify-between px-4">
          <p></p>
          <AdminVendorTypesCreateForm
            storeVendorType={action}
            serviceId={serviceId}
          />
        </div>
      }
    />
  );
}

export default ServiceVendorTypesTable;

// return (
//   <div className="flex space-x-2">
//     <Link
//       href={`/vendors/types/${type.id}/edit`}
//       className="text-primary"
//     >
//       <Icon
//         name="icon-[mage--edit-pen-fill]"
//         classNames="text-primary"
//       />
//     </Link>
//     <a
//       href={`/vendors/types/${type.id}/delete`}
//       className="text-red-500"
//     >
//       <Icon name="icon-[mingcute--delete-2-line]" />
//     </a>
//   </div>
// );
