"use client";

import Link from "next/link";
import { CustomTable } from "../custom-table";
import { Icon } from "../icon";

function ServiceVendorsTable({
  data,
  meta,
  serviceId,
}: {
  data: Vendor[];
  meta: PaginationMeta;
  serviceId: string;
}) {
  return (
    <CustomTable
      title=""
      columns={[
        {
          uid: "name",
          name: "Name",
          renderCell: (record) => (
            <Link href={`/vendors/${record.id}`}>{record.name}</Link>
          ),
        },
        { uid: "reg", name: "<PERSON>" },
        { uid: "phone", name: "Phone" },
        {
          uid: "actions",
          name: "Actions",
          renderCell: (record) => (
            <Link href={`/vendors/${record.id}`}>
              <Icon
                name="icon-[mage--edit-pen-fill]"
                classNames="text-primary"
              />
            </Link>
          ),
        },
      ]}
      data={data}
      meta={meta}
      action={
        <div className="mb-4 flex items-center justify-between px-4">
          <Link
            href={`/vendors/create?serviceId=${serviceId}`}
            className="flex items-center justify-center rounded-lg bg-primary px-6 py-2 text-white"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="mr-2 h-5 w-5"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M12 4.5v15m7.5-7.5h-15"
              />
            </svg>

            <span>Create Vendor</span>
          </Link>
        </div>
      }
    />
  );
}

export default ServiceVendorsTable;
