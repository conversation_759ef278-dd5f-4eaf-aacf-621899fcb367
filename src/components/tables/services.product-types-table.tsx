"use client";

import AdminProductTypesEditForm from "@/app/(authenticated)/products/types/edit";
import { CustomTable } from "../custom-table";
import Link from "next/link";
import AdminProductTypesCreateForm from "@/app/(authenticated)/products/types/create";
import toast from "react-hot-toast";

function ServiceProductTypesTable({
  data,
  meta,
  serviceId,
  updateProductType,
  deleteRecord,
  storeProductType,
}: {
  data: ProductType[];
  meta: PaginationMeta;
  serviceId: string;
  storeProductType: (data: FormData) => Promise<void>;
  updateProductType: (data: FormData) => Promise<void>;
  deleteRecord: (id: string) => Promise<void>;
}) {
  const handleDelete = async (id: string) => {
    toast.promise(deleteRecord(id), {
      loading: "Deleting product type...",
      success: "Product type deleted!",
      error: "Failed to delete product type",
    });
  };

  return (
    <CustomTable
      title=""
      columns={[
        {
          uid: "name",
          name: "Name",
          renderCell: (type) => (
            <Link href={`/products/types/${type.id}`}>{type.name}</Link>
          ),
        },
        { uid: "details", name: "Details" },
        {
          uid: "actions",
          name: "Actions",
          renderCell: (type) => {
            return (
              <div className="flex items-center space-x-2">
                <Link href={`/products/types/${type.id}/edit`}>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={1.5}
                    stroke="currentColor"
                    className="h-6 w-6"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125"
                    />
                  </svg>
                </Link>

                <form>
                  <button
                    type="submit"
                    name="id"
                    className="text-red-500"
                    onClick={(e) => handleDelete(type.id)}
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      strokeWidth={1.5}
                      stroke="currentColor"
                      className="h-6 w-6"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"
                      />
                    </svg>
                  </button>
                </form>
              </div>
            );
          },
        },
      ]}
      data={data}
      meta={meta}
      action={
        <div className="mb-4 flex items-center justify-between px-4">
          <AdminProductTypesCreateForm
            storeProductType={storeProductType}
            serviceId={serviceId}
            classNames="flex items-center space-x-2 px-4 bg-primary py-2 text-white rounded-lg"
          />
        </div>
      }
    />
  );
}

export default ServiceProductTypesTable;
