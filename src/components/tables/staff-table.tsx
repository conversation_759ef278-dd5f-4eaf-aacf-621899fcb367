"use client";

import React from "react";
import { CustomTable } from "../custom-table";
import Link from "next/link";
import { User } from "@nextui-org/react";
import { imagePath } from "@/lib/api";
import { Icon } from "../icon";
import VendorActiveStatus from "@/app/(authenticated)/vendors/active";
import { updateActive } from "@/actions/users";
import toast from "react-hot-toast";

export function generateRandomString(length: number): string {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';

  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * characters.length);
    result += characters[randomIndex];
  }

  return result;
}

export default function StaffTable({
  data,
  meta,
  params,
  deleteStaff
}: {
  data: User[];
  meta: PaginationMeta;
  params: { branchId: string},
  deleteStaff: (data: FormData)=> Promise<boolean>
}) {

  const onSumbit=async(data: FormData)=>{
    await toast.promise(deleteStaff(data), {
      loading:"Deleting user...",
      success: "User deleted successfully",
      error:(e)=> `${e.statusCode} ${e.data.message}`
    })
  }

  return (
    <CustomTable
      title="Staff"
      columns={[
        {
          name: "Staff",
          uid: "staff",
          renderCell: (user: User) => (
            <Link
              href={`/branches/${params.branchId}/staff/${user.id}`}
              className="flex items-center space-x-2"
            >
              <User
                name={user.name}
                description={user.email}
                avatarProps={{
                  src: `${imagePath(user.avatar?.url, null)}`,
                  classNames: { img: "object-contain" },
                }}
                classNames={{
                  description: "text-default-600",
                  name: "font-bold",
                }}
              />
            </Link>
          ),
        },
        { uid: "email", name: "Email" },
        {
          uid: "online", name: "Status", renderCell: (user: User) => {
            {
              return user.online ?
                <span className="px-2 py-1 border-none rounded-xl text-green-700 text-sm bg-green-200">online</span> :
                <span className="px-2 py-1 border-none rounded-xl text-red-700 text-sm bg-red-200">offline</span>
            }
          }
        },
        {
          uid: "role",
          name: "Role",
          renderCell: (user: any) => (
            <div className="flex gap-2 flex-wrap">
              {user.roles && user.roles.map((role: Role) => {
                return <p key={role.id} className="px-2 w-fit py-1 rounded bg-gray-100 text-sm text-center text-gray-600">{role.name}</p>
              })}
            </div>
          )
        },
        { uid: "phone", name: "Phone" },
        {
          name: "Active",
          uid: "active",
          renderCell: (user:User) => (
            <VendorActiveStatus
              defaultChecked={user.status === "Active"}
              onChange={(v) => updateActive(v, user)}
            />
          ),
        },
        {
          name: "Actions",
          uid: "actions",
          renderCell: (user) => (
            <div className="flex w-fit items-center gap-5">
              <Link href={`/branches/${params.branchId}/staff/${user.id}`}>
                <Icon
                  name="icon-[mingcute--eye-line]"
                  classNames="text-primary"
                />
              </Link>
              <Link href={`/branches/${params.branchId}/staff/${user.id}/edit`}>
                <Icon
                  name="icon-[mage--edit-pen-fill]"
                  classNames="text-primary"
                />
              </Link>
              <form action={onSumbit}>
                <button
                  name="id"
                  value={user.id}
                  type="submit"
                  className="text-red-500"
                >
                  <Icon name="icon-[mingcute--delete-2-line]" />
                </button>
              </form>
            </div>
          ),
        },
      ]}
      data={data}
      meta={meta}
      action={
        <Link
          className="flex items-center space-x-3 rounded-lg bg-primary px-6 py-2 text-white"
          href={params.branchId ? `/branches/${params.branchId}/staff/create` : "users/create"}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
            className="h-4 w-4"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M12 4.5v15m7.5-7.5h-15"
            />
          </svg>
          <span className="s-only">Add Staff</span>
        </Link>
      }
    />
  );
}
