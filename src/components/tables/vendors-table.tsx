"use client";

import React from "react";
import { CustomTable } from "../custom-table";
import Link from "next/link";
import { User } from "@nextui-org/react";
import { imagePath } from "@/lib/api";
import { Icon } from "../icon";
import VendorActiveStatus from "@/app/(authenticated)/vendors/active";
import VendorFeaturedStatus from "@/app/(authenticated)/vendors/featured";
import { deleteVendor, updateActive, updateFeatured } from "@/actions/vendors";

export default function VendorsTable({
  data,
  meta,
}: {
  data: Vendor[];
  meta: PaginationMeta;
}) {
  return (
    <CustomTable
      title="Vendors"
      columns={[
        {
          name: "Vendors",
          uid: "vendors",
          renderCell: (vendor: Vendor) => (
            <Link
              href={`/vendors/${vendor.id}`}
              className="flex items-center space-x-2"
            >
              <User
                name={vendor.name}
                description={vendor.email}
                avatarProps={{
                  src: `${imagePath(vendor.logo?.url, null)}`,
                  classNames: { img: "object-contain" },
                }}
                classNames={{
                  description: "text-default-600",
                  name: "font-bold",
                }}
              />
            </Link>
          ),
        },
        { uid: "slug", name: "Slug" },
        { uid: "reg", name: "Reg" },
        { uid: "phone", name: "Phone" },
        {
          name: "Active",
          uid: "active",
          renderCell: (vendor) => (
            <VendorActiveStatus
              defaultChecked={vendor.active}
              onChange={(v) => updateActive(v, vendor)}
            />
          ),
        },

        // featured
        {
          name: "Featured",
          uid: "featured",
          renderCell: (vendor) => (
            <VendorFeaturedStatus
              defaultChecked={vendor.featured}
              onChange={(v) => updateFeatured(v, vendor)}
            />
          ),
        },
        {
          name: "Actions",
          uid: "actions",
          renderCell: (vendor) => (
            <div className="flex w-fit items-center gap-5">
              <Link href={`/vendors/${vendor.id}`}>
                <Icon
                  name="icon-[mingcute--eye-line]"
                  classNames="text-primary"
                />
              </Link>
              <Link href={`/vendors/${vendor.id}/tasks`}>
                <Icon
                  name="icon-[mingcute--list-check-3-fill]"
                  classNames="text-primary"
                />
              </Link>

              <Link href={`/vendors/${vendor.id}/edit`}>
                <Icon
                  name="icon-[mage--edit-pen-fill]"
                  classNames="text-primary"
                />
              </Link>
              <form action={deleteVendor}>
                <button
                  name="id"
                  value={vendor.id}
                  type="submit"
                  className="text-red-500"
                >
                  <Icon name="icon-[mingcute--delete-2-line]" />
                </button>
              </form>
            </div>
          ),
        },
      ]}
      data={data}
      meta={meta}
    />
  );
}
