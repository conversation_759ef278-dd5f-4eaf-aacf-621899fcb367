"use client";

import { But<PERSON> } from "@nextui-org/react";
import { useTheme } from "next-themes";
import { Icon } from "./icon";

export const ThemeSwitcher = () => {
  const { theme, setTheme } = useTheme();

  return (
    <div>
      <Button
        isIconOnly
        variant="light"
        aria-label="theme switcher"
        size="sm"
        className="border"
        onClick={() => {
          setTheme(theme === "light" ? "dark" : "light");
        }}
      >
        {theme === "dark" ? (
          <Icon
            name="icon-[heroicons--sun-20-solid]"
            classNames="text-grey-600"
          />
        ) : (
          <Icon name="icon-[lucide--moon-star]" classNames="text-grey-600" />
        )}
      </Button>
    </div>
  );
};
