// import { useEffect, useState } from "react";
// import { getToken, isSupported } from "firebase/messaging";
// import { messaging } from "@/lib/fcm";
// import { MessagePayload, onMessage } from "firebase/messaging";
// import { toast } from "react-toastify";

// const useFCM = () => {
// 	const [fcmToken, setFcmToken] = useState<string>();
// 	const [messages, setMessages] = useState<MessagePayload[]>([]);
// 	const [permission, setPermission] =
// 		useState<NotificationPermission>("default");

// 	const fcm = messaging();
// 	const handler = () => setPermission(Notification.permission);

// 	useEffect(() => {
// 		Notification.requestPermission().then(setPermission);
// 		navigator.permissions
// 			.query({ name: "notifications" })
// 			.then(notificationPerm => {
// 				notificationPerm.onchange = handler;
// 			});
// 	}, []);

// 	useEffect(() => {
// 		handler();

// 		const retrieveToken = async () => {
// 			if (typeof window !== "undefined" && "serviceWorker" in navigator) {
// 				if (permission === "granted") {
// 					const isFCMSupported = await isSupported();
// 					if (!isFCMSupported) return;
// 					const fcmToken = await getToken(fcm, {
// 						vapidKey: process.env.NEXT_PUBLIC_FIREBASE_VAPID_KEY,
// 					});
// 					setFcmToken(fcmToken);
// 				}
// 			}
// 		};

// 		retrieveToken();

// 		if ("serviceWorker" in navigator) {
// 			const unsubscribe = onMessage(fcm, payload => {
// 				toast.dark(payload.notification?.title);
// 				setMessages(messages => [...messages, payload]);
// 			});

// 			return () => unsubscribe();
// 		}

// 	}, [permission, fcmToken]);

// 	return { fcmToken, messages };
// };

// export default useFCM;
