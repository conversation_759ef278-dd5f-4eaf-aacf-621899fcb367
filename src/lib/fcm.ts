"use client";

// Import the functions you need from the SDKs you need
import { type FirebaseOptions, initializeApp } from "firebase/app";
import { getMessaging } from "firebase/messaging";
import { getAnalytics } from "firebase/analytics";
// TODO: Add SDKs for Firebase products that you want to use
// https://firebase.google.com/docs/web/setup#available-libraries

// Your web app's Firebase configuration
const firebaseConfig: FirebaseOptions = {
  apiKey: "AIzaSyADXvBSzLZ77esiIW2EtvwQZr748Lqhh3Y",
  authDomain: "epic-app-in-app.firebaseapp.com",
  projectId: "epic-app-in-app",
  storageBucket: "epic-app-in-app.appspot.com",
  messagingSenderId: "514250087098",
  appId: "1:514250087098:web:6ca47d402aae6abda60083",
  measurementId: "G-MGBZ0HQB69",
};

// Initialize Firebase
const firebaseapp = initializeApp(firebaseConfig);

export const messaging = () => getMessaging(firebaseapp);

// export const analytics = getAnalytics(firebaseapp);

export default firebaseapp;
