import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const createUrl = (
  pathname: string,
  params: URLSearchParams,
  key?: string,
  value?: string,
) => {
  if (key && value) {
    params.set(key, value);
  }

  const paramsString = params.toString();
  const queryString = `${paramsString.length ? "?" : ""}${paramsString}`;

  return `${pathname}${queryString}`;
};

/**
 * @param {Function} func
 * @param {number} delay
 * @param {{ leading?: boolean }} options
 */
export function debounce(
  func: Function,
  delay: number,
  { leading }: { leading?: boolean } = {},
) {
  let timerId: string | number | NodeJS.Timeout | undefined;

  return (...args: unknown[]) => {
    if (!timerId && leading) {
      func(...args);
    }
    if (timerId) {
      clearTimeout(timerId);
    }

    timerId = setTimeout(() => func(...args), delay);
  };
}

export const fieldTypes: FieldType[] = [
  {
    key: "uFirstName",
    label: "Current user first name",
  },
  {
    key: "uLastName",
    label: "Current user last name",
  },
  {
    key: "uEmail",
    label: "Current user email",
  },
  {
    key: "uPhone",
    label: "Current user phone",
  },
  {
    key: "uAddress",
    label: "Current user address",
  },
  {
    key: "text",
    label: "Text input",
  },
  {
    key: "tel",
    label: "Phone input",
  },
  {
    key: "email",
    label: "Email input",
  },
  {
    key: "textarea",
    label: "Textarea input",
  },
  {
    key: "checkbox",
    label: "Checkbox input",
  },
  {
    key: "multicheck",
    label: "Multi-checkbox input",
  },
  {
    key: "radio",
    label: "Radio button",
  },
  {
    key: "select",
    label: "Select options",
  },
  {
    key: "multiselect",
    label: "Multi-select options",
  },
  {
    key: "password",
    label: "Password input",
  },
  {
    key: "date",
    label: "Date picker",
  },
  {
    key: "time",
    label: "Time picker",
  },
  {
    key: "datetime",
    label: "Date and time picker",
  },
  {
    key: "number",
    label: "Number input",
  },
  {
    key: "range",
    label: "Range input",
  },
  {
    key: "color",
    label: "Color picker",
  },
  {
    key: "file",
    label: "File upload",
  },
  {
    key: "image",
    label: "Image upload",
  },
  {
    key: "video",
    label: "Video upload",
  },
  {
    key: "slot",
    label: "Time slot picker",
  },
  {
    key: "location",
    label: "Location picker",
  },
  {
    key: "vendorSelect",
    label: "Vendor selection",
  },
];
