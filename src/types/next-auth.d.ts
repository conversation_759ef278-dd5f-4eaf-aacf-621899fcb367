import { DefaultSession } from "@auth/core/types";

declare module "@auth/core/jwt" {
  interface JWT {
    id: string;
    firstName: string;
    lastName: string;
    accessToken: string;
    roles: string[];
    expiresAt: string;
    branch: Branch;
    vendor: Vendor;
  }
}

declare module "@auth/core/types" {
  /**
   * The shape of the user object returned in the OAuth providers' `profile` callback,
   * or the second parameter of the `session` callback, when using a database.
   */
  interface User {
    id: string;
    title: string;
    firstName: string;
    lastName: string;
    gender: string | null;
    dob: string | null;
    email: string;
    phone: string;
    idpass: string;
    rememberMeToken: string | null;
    details: string | null;
    location: Record<string, any> | null;
    geom: string | null;
    avatar: AttachmentContract | null;
    createdAt: string;
    updatedAt: string;
    name: string;
    status: string;
    avatarUrl: string;
    initials: string;
    createdAt: string;
    updatedAt: string;
    roles: Role[];
    permissions: Permission[];
    notifications: DatabaseNotification[];
    accessToken: string;
    expiresAt: string;
    branch: Branch;
    vendor: Vendor;
  }

  /**
   * Returned by `useSession`, `getSession` and received as a prop on the `SessionProvider` React Context
   */
  interface Session {
    user: User & DefaultSession["user"];
    branch: Branch;
    vendor: Vendor;
    accessToken: string;
    expiresAt: string;
  }

  /**
   * Usually contains information about the provider being used
   * and also extends `TokenSet`, which is different tokens returned by OAuth Providers.
   */
  interface Account {}

  /** The OAuth profile returned from your provider */
  interface Profile {
    emailVerified: string;
  }
}
