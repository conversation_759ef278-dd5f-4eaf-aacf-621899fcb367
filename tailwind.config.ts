import { addDynamicIconSelectors } from "@iconify/tailwind";
import { nextui } from "@nextui-org/react";
import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./src/*.html",
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
    "./node_modules/react-tailwindcss-datepicker/dist/index.esm.js",
    "./node_modules/@nextui-org/theme/dist/**/*.{js,ts,jsx,tsx}",
  ],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        primary: "#65A694",
        red: {
          600: "#E8503D",
        },
        default: {
          50: "#F2F7F6",
          100: "#E6F0EE",
          200: "#CCE0DC",
          300: "#B6D3CD",
          400: "#9DC3BC",
          500: "#83B4AA",
          600: "#5E9C8F",
          700: "#47766C",
          800: "#2E4C46",
          900: "#172623",
          950: "#0C1312",
        },
      },
      backgroundImage: {
        "gradient-radial": "radial-gradient(var(--tw-gradient-stops))",
        "gradient-conic":
          "conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))",
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  darkMode: "class",
  plugins: [
    nextui({
      layout: {
        radius: {
          small: "6px", // rounded-small
          medium: "8px", // rounded-medium
          large: "10px", // rounded-large
        },
      },
      themes: {
        light: {
          colors: {
            background: "#F2F7F6",
            foreground: "#0C1312",
          },
        },
        dark: {
          colors: {
            background: "#172623",
            foreground: "#FFFFFF",
          },
        },
      },
    }),
    require("tailwindcss-animate"),
    addDynamicIconSelectors(),
    function ({ addUtilities }: any) {
      const newUtilities = {
        ".scrollbar-thin": {
          scrollbarWidth: "thin",
          scrollbarColor: "rgb(196 205 213) white",
        },
        ".scrollbar-webkit": {
          "&::-webkit-scrollbar": {
            width: "8px",
          },
          "&::-webkit-scrollbar-track": {
            background: "white",
          },
          "&::-webkit-scrollbar-thumb": {
            backgroundColor: "rgb(196 205 213)",
            borderRadius: "20px",
            border: "1px solid white",
          },
        },
      };
      ("");
      addUtilities(newUtilities, ["responsive", "hover"]);
    },
  ],
};
export default config;
